import { exec } from 'child_process';
import { promisify } from 'util';
import net from 'net';

const execAsync = promisify(exec);

export interface DiscoveredServer {
  ip: string;
  hostname?: string;
  os?: string;
  status: 'online' | 'offline';
  ports: number[];
  services: string[];
  lastSeen: Date;
}

export interface NetworkRange {
  startIP: string;
  endIP: string;
  subnet?: string;
}

/**
 * اكتشاف السيرفرات في الشبكة المحلية
 */
export class ServerDiscovery {
  private discoveredServers: Map<string, DiscoveredServer> = new Map();
  private scanInProgress = false;

  /**
   * اكتشاف السيرفرات في نطاق IP محدد
   */
  async discoverServers(range: NetworkRange): Promise<DiscoveredServer[]> {
    if (this.scanInProgress) {
      throw new Error('عملية اكتشاف أخرى قيد التنفيذ');
    }

    this.scanInProgress = true;
    console.log(`بدء اكتشاف السيرفرات في النطاق: ${range.startIP} - ${range.endIP}`);

    try {
      const ips = this.generateIPRange(range.startIP, range.endIP);
      const results = await Promise.allSettled(
        ips.map(ip => this.scanServer(ip))
      );

      const servers: DiscoveredServer[] = [];
      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          servers.push(result.value);
          this.discoveredServers.set(result.value.ip, result.value);
        }
      });

      console.log(`تم اكتشاف ${servers.length} سيرفر`);
      return servers;
    } finally {
      this.scanInProgress = false;
    }
  }

  /**
   * اكتشاف السيرفرات في الشبكة المحلية تلقائياً
   */
  async discoverLocalNetwork(): Promise<DiscoveredServer[]> {
    try {
      const networkInfo = await this.getLocalNetworkInfo();
      return await this.discoverServers({
        startIP: networkInfo.networkStart,
        endIP: networkInfo.networkEnd,
        subnet: networkInfo.subnet
      });
    } catch (error) {
      console.error('خطأ في اكتشاف الشبكة المحلية:', error);
      return [];
    }
  }

  /**
   * فحص سيرفر واحد
   */
  private async scanServer(ip: string): Promise<DiscoveredServer | null> {
    try {
      // فحص إذا كان الـ IP متاحاً
      const isAlive = await this.pingHost(ip);
      if (!isAlive) {
        return null;
      }

      // فحص المنافذ الشائعة
      const commonPorts = [22, 23, 25, 53, 80, 110, 135, 139, 143, 443, 993, 995, 1433, 3389, 5432, 5985, 5986];
      const openPorts = await this.scanPorts(ip, commonPorts);

      if (openPorts.length === 0) {
        return null;
      }

      // محاولة الحصول على معلومات إضافية
      const hostname = await this.getHostname(ip);
      const os = await this.detectOS(ip, openPorts);
      const services = this.identifyServices(openPorts);

      const server: DiscoveredServer = {
        ip,
        hostname,
        os,
        status: 'online',
        ports: openPorts,
        services,
        lastSeen: new Date()
      };

      return server;
    } catch (error) {
      console.error(`خطأ في فحص السيرفر ${ip}:`, error);
      return null;
    }
  }

  /**
   * فحص إذا كان الهوست متاحاً
   */
  private async pingHost(ip: string): Promise<boolean> {
    try {
      if (process.platform === 'win32') {
        const { stdout } = await execAsync(`ping -n 1 -w 1000 ${ip}`);
        return stdout.includes('TTL=');
      } else {
        const { stdout } = await execAsync(`ping -c 1 -W 1 ${ip}`);
        return stdout.includes('1 received');
      }
    } catch {
      return false;
    }
  }

  /**
   * فحص المنافذ المفتوحة
   */
  private async scanPorts(ip: string, ports: number[]): Promise<number[]> {
    const openPorts: number[] = [];
    const timeout = 2000;

    const portChecks = ports.map(port => 
      this.checkPort(ip, port, timeout)
        .then(isOpen => isOpen ? port : null)
        .catch(() => null)
    );

    const results = await Promise.allSettled(portChecks);
    results.forEach(result => {
      if (result.status === 'fulfilled' && result.value !== null) {
        openPorts.push(result.value);
      }
    });

    return openPorts;
  }

  /**
   * فحص منفذ واحد
   */
  private checkPort(ip: string, port: number, timeout: number): Promise<boolean> {
    return new Promise((resolve) => {
      const socket = new net.Socket();
      
      socket.setTimeout(timeout);
      
      socket.on('connect', () => {
        socket.destroy();
        resolve(true);
      });
      
      socket.on('timeout', () => {
        socket.destroy();
        resolve(false);
      });
      
      socket.on('error', () => {
        resolve(false);
      });
      
      socket.connect(port, ip);
    });
  }

  /**
   * الحصول على اسم الهوست
   */
  private async getHostname(ip: string): Promise<string | undefined> {
    try {
      if (process.platform === 'win32') {
        const { stdout } = await execAsync(`nslookup ${ip}`);
        const match = stdout.match(/Name:\s+(.+)/);
        return match ? match[1].trim() : undefined;
      } else {
        const { stdout } = await execAsync(`host ${ip}`);
        const match = stdout.match(/pointer (.+)\./);
        return match ? match[1] : undefined;
      }
    } catch {
      return undefined;
    }
  }

  /**
   * اكتشاف نوع نظام التشغيل
   */
  private async detectOS(ip: string, openPorts: number[]): Promise<string | undefined> {
    try {
      // فحص منافذ Windows
      if (openPorts.includes(135) || openPorts.includes(139) || openPorts.includes(445) || openPorts.includes(3389)) {
        return 'Windows';
      }
      
      // فحص منافذ Linux/Unix
      if (openPorts.includes(22)) {
        return 'Linux/Unix';
      }
      
      // محاولة استخدام nmap إذا كان متوفراً
      try {
        const { stdout } = await execAsync(`nmap -O ${ip}`);
        if (stdout.includes('Windows')) return 'Windows';
        if (stdout.includes('Linux')) return 'Linux';
        if (stdout.includes('Unix')) return 'Unix';
      } catch {
        // nmap غير متوفر
      }
      
      return 'Unknown';
    } catch {
      return undefined;
    }
  }

  /**
   * تحديد الخدمات بناءً على المنافذ المفتوحة
   */
  private identifyServices(ports: number[]): string[] {
    const serviceMap: { [key: number]: string } = {
      22: 'SSH',
      23: 'Telnet',
      25: 'SMTP',
      53: 'DNS',
      80: 'HTTP',
      110: 'POP3',
      135: 'RPC',
      139: 'NetBIOS',
      143: 'IMAP',
      443: 'HTTPS',
      993: 'IMAPS',
      995: 'POP3S',
      1433: 'SQL Server',
      3389: 'RDP',
      5432: 'PostgreSQL',
      5985: 'WinRM HTTP',
      5986: 'WinRM HTTPS'
    };

    return ports.map(port => serviceMap[port]).filter(Boolean);
  }

  /**
   * الحصول على معلومات الشبكة المحلية
   */
  private async getLocalNetworkInfo(): Promise<{
    networkStart: string;
    networkEnd: string;
    subnet: string;
  }> {
    try {
      if (process.platform === 'win32') {
        const { stdout } = await execAsync('ipconfig');
        const ipMatch = stdout.match(/IPv4 Address[.\s]*:\s*(\d+\.\d+\.\d+\.\d+)/);
        const subnetMatch = stdout.match(/Subnet Mask[.\s]*:\s*(\d+\.\d+\.\d+\.\d+)/);
        
        if (ipMatch && subnetMatch) {
          const ip = ipMatch[1];
          const subnet = subnetMatch[1];
          const network = this.calculateNetworkRange(ip, subnet);
          return network;
        }
      }
      
      // افتراضي للشبكة المحلية
      return {
        networkStart: '***********',
        networkEnd: '*************',
        subnet: '*************'
      };
    } catch (error) {
      console.error('خطأ في الحصول على معلومات الشبكة:', error);
      return {
        networkStart: '***********',
        networkEnd: '*************',
        subnet: '*************'
      };
    }
  }

  /**
   * حساب نطاق الشبكة
   */
  private calculateNetworkRange(ip: string, subnet: string): {
    networkStart: string;
    networkEnd: string;
    subnet: string;
  } {
    const ipParts = ip.split('.').map(Number);
    const subnetParts = subnet.split('.').map(Number);
    
    const networkParts = ipParts.map((part, index) => part & subnetParts[index]);
    const broadcastParts = networkParts.map((part, index) => part | (255 - subnetParts[index]));
    
    const networkStart = networkParts.join('.');
    const networkEnd = broadcastParts.join('.');
    
    return { networkStart, networkEnd, subnet };
  }

  /**
   * توليد نطاق من عناوين IP
   */
  private generateIPRange(startIP: string, endIP: string): string[] {
    const start = this.ipToNumber(startIP);
    const end = this.ipToNumber(endIP);
    const ips: string[] = [];
    
    for (let i = start; i <= end; i++) {
      ips.push(this.numberToIP(i));
    }
    
    return ips;
  }

  /**
   * تحويل IP إلى رقم
   */
  private ipToNumber(ip: string): number {
    return ip.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet), 0) >>> 0;
  }

  /**
   * تحويل رقم إلى IP
   */
  private numberToIP(num: number): string {
    return [
      (num >>> 24) & 255,
      (num >>> 16) & 255,
      (num >>> 8) & 255,
      num & 255
    ].join('.');
  }

  /**
   * الحصول على السيرفرات المكتشفة
   */
  getDiscoveredServers(): DiscoveredServer[] {
    return Array.from(this.discoveredServers.values());
  }

  /**
   * مسح السيرفرات المكتشفة
   */
  clearDiscoveredServers(): void {
    this.discoveredServers.clear();
  }
}

// إنشاء instance مشترك
export const serverDiscovery = new ServerDiscovery();
