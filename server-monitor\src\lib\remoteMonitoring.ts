import { exec } from 'child_process';
import { promisify } from 'util';
import { database } from './database';

const execAsync = promisify(exec);

export interface RemoteServer {
  id: string;
  name: string;
  ip: string;
  os: 'windows' | 'linux' | 'unknown';
  credentials?: {
    username: string;
    password?: string;
    keyPath?: string;
  };
  status: 'online' | 'offline' | 'warning' | 'error';
  lastUpdate: Date;
}

export interface RemoteSystemInfo {
  cpu: number;
  memory: number;
  diskUsage: number;
  disks: Array<{
    id: string;
    name: string;
    health: 'good' | 'warning' | 'critical';
    usage: number;
    size: string;
    badSectors?: number;
  }>;
  uptime: number;
  services?: Array<{
    name: string;
    status: 'running' | 'stopped' | 'unknown';
  }>;
  processes?: Array<{
    name: string;
    cpu: number;
    memory: number;
  }>;
}

/**
 * مراقبة السيرفرات البعيدة
 */
export class RemoteMonitoring {
  private servers: Map<string, RemoteServer> = new Map();

  constructor() {
    this.loadServersFromDatabase();
  }

  /**
   * تحميل السيرفرات من قاعدة البيانات
   */
  private async loadServersFromDatabase(): Promise<void> {
    try {
      await database.connect();
      const serverRecords = await database.getServers();

      for (const record of serverRecords) {
        const server: RemoteServer = {
          id: record.id,
          name: record.name,
          ip: record.ip,
          os: record.os as 'windows' | 'linux' | 'unknown',
          credentials: record.credentials ? JSON.parse(record.credentials) : undefined,
          status: record.status as RemoteServer['status'],
          lastUpdate: new Date(record.updated_at)
        };

        this.servers.set(server.id, server);
      }

      console.log(`تم تحميل ${serverRecords.length} سيرفر من قاعدة البيانات`);
    } catch (error) {
      console.error('خطأ في تحميل السيرفرات من قاعدة البيانات:', error);
    }
  }

  /**
   * إضافة سيرفر للمراقبة
   */
  async addServer(server: RemoteServer): Promise<void> {
    this.servers.set(server.id, server);
    console.log(`تم إضافة السيرفر: ${server.name} (${server.ip})`);

    // حفظ في قاعدة البيانات
    try {
      await database.addServer({
        id: server.id,
        name: server.name,
        ip: server.ip,
        os: server.os,
        credentials: server.credentials ? JSON.stringify(server.credentials) : undefined,
        status: server.status
      });
    } catch (error) {
      console.error('خطأ في حفظ السيرفر في قاعدة البيانات:', error);
    }
  }

  /**
   * إزالة سيرفر من المراقبة
   */
  async removeServer(serverId: string): Promise<boolean> {
    const removed = this.servers.delete(serverId);

    if (removed) {
      // حذف من قاعدة البيانات
      try {
        await database.deleteServer(serverId);
      } catch (error) {
        console.error('خطأ في حذف السيرفر من قاعدة البيانات:', error);
      }
    }

    return removed;
  }

  /**
   * الحصول على جميع السيرفرات
   */
  getServers(): RemoteServer[] {
    return Array.from(this.servers.values());
  }

  /**
   * الحصول على سيرفر محدد
   */
  getServer(serverId: string): RemoteServer | undefined {
    return this.servers.get(serverId);
  }

  /**
   * مراقبة سيرفر بعيد
   */
  async monitorServer(serverId: string): Promise<RemoteSystemInfo | null> {
    const server = this.servers.get(serverId);
    if (!server) {
      throw new Error(`السيرفر غير موجود: ${serverId}`);
    }

    try {
      let systemInfo: RemoteSystemInfo;

      if (server.os === 'windows') {
        systemInfo = await this.monitorWindowsServer(server);
      } else if (server.os === 'linux') {
        systemInfo = await this.monitorLinuxServer(server);
      } else {
        // محاولة اكتشاف نوع النظام
        const detectedOS = await this.detectRemoteOS(server);
        server.os = detectedOS;

        if (detectedOS === 'windows') {
          systemInfo = await this.monitorWindowsServer(server);
        } else {
          systemInfo = await this.monitorLinuxServer(server);
        }
      }

      // تحديث حالة السيرفر
      server.status = 'online';
      server.lastUpdate = new Date();

      return systemInfo;
    } catch (error) {
      console.error(`خطأ في مراقبة السيرفر ${server.name}:`, error);
      server.status = 'error';
      server.lastUpdate = new Date();
      return null;
    }
  }

  /**
   * مراقبة سيرفر Windows بعيد
   */
  private async monitorWindowsServer(server: RemoteServer): Promise<RemoteSystemInfo> {
    const credentials = server.credentials;
    if (!credentials) {
      throw new Error('بيانات الاعتماد مطلوبة لسيرفرات Windows');
    }

    try {
      // استخدام WMI للحصول على معلومات النظام
      const wmiCommands = {
        cpu: `wmic /node:"${server.ip}" /user:"${credentials.username}" /password:"${credentials.password}" cpu get loadpercentage /value`,
        memory: `wmic /node:"${server.ip}" /user:"${credentials.username}" /password:"${credentials.password}" OS get TotalVisibleMemorySize,FreePhysicalMemory /value`,
        disks: `wmic /node:"${server.ip}" /user:"${credentials.username}" /password:"${credentials.password}" logicaldisk get size,freespace,caption /value`,
        uptime: `wmic /node:"${server.ip}" /user:"${credentials.username}" /password:"${credentials.password}" os get lastbootuptime /value`,
        services: `wmic /node:"${server.ip}" /user:"${credentials.username}" /password:"${credentials.password}" service get name,state /value`,
        processes: `wmic /node:"${server.ip}" /user:"${credentials.username}" /password:"${credentials.password}" process get name,percentprocessortime,workingsetsize /value`
      };

      // تنفيذ الأوامر بشكل متوازي
      const [cpuResult, memoryResult, disksResult, uptimeResult] = await Promise.allSettled([
        this.executeWMICommand(wmiCommands.cpu),
        this.executeWMICommand(wmiCommands.memory),
        this.executeWMICommand(wmiCommands.disks),
        this.executeWMICommand(wmiCommands.uptime)
      ]);

      // معالجة النتائج
      const cpu = this.parseWindowsCPU(cpuResult.status === 'fulfilled' ? cpuResult.value : '');
      const memory = this.parseWindowsMemory(memoryResult.status === 'fulfilled' ? memoryResult.value : '');
      const disks = this.parseWindowsDisks(disksResult.status === 'fulfilled' ? disksResult.value : '');
      const uptime = this.parseWindowsUptime(uptimeResult.status === 'fulfilled' ? uptimeResult.value : '');

      return {
        cpu,
        memory: memory.usage,
        diskUsage: disks.avgUsage,
        disks: disks.disks,
        uptime
      };
    } catch (error) {
      console.error('خطأ في مراقبة سيرفر Windows:', error);
      throw error;
    }
  }

  /**
   * مراقبة سيرفر Linux بعيد
   */
  private async monitorLinuxServer(server: RemoteServer): Promise<RemoteSystemInfo> {
    const credentials = server.credentials;
    if (!credentials) {
      throw new Error('بيانات الاعتماد مطلوبة لسيرفرات Linux');
    }

    try {
      const sshBase = credentials.keyPath
        ? `ssh -i "${credentials.keyPath}" ${credentials.username}@${server.ip}`
        : `sshpass -p "${credentials.password}" ssh ${credentials.username}@${server.ip}`;

      const commands = {
        cpu: `${sshBase} "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1"`,
        memory: `${sshBase} "free | grep Mem | awk '{printf \"%.1f\", $3/$2 * 100.0}'"`,
        disk: `${sshBase} "df -h | grep -E '^/dev/' | awk '{print $5}' | sed 's/%//' | sort -n | tail -1"`,
        uptime: `${sshBase} "cat /proc/uptime | awk '{print $1}'"`,
        disks: `${sshBase} "df -h | grep -E '^/dev/'"`,
      };

      // تنفيذ الأوامر
      const [cpuResult, memoryResult, diskResult, uptimeResult, disksResult] = await Promise.allSettled([
        execAsync(commands.cpu),
        execAsync(commands.memory),
        execAsync(commands.disk),
        execAsync(commands.uptime),
        execAsync(commands.disks)
      ]);

      // معالجة النتائج
      const cpu = this.parseLinuxCPU(cpuResult.status === 'fulfilled' ? cpuResult.value.stdout : '');
      const memory = parseFloat(memoryResult.status === 'fulfilled' ? memoryResult.value.stdout : '0');
      const diskUsage = parseInt(diskResult.status === 'fulfilled' ? diskResult.value.stdout : '0');
      const uptime = parseFloat(uptimeResult.status === 'fulfilled' ? uptimeResult.value.stdout : '0');
      const disks = this.parseLinuxDisks(disksResult.status === 'fulfilled' ? disksResult.value.stdout : '');

      return {
        cpu,
        memory,
        diskUsage,
        disks,
        uptime
      };
    } catch (error) {
      console.error('خطأ في مراقبة سيرفر Linux:', error);
      throw error;
    }
  }

  /**
   * اكتشاف نوع نظام التشغيل البعيد
   */
  private async detectRemoteOS(server: RemoteServer): Promise<'windows' | 'linux' | 'unknown'> {
    try {
      // محاولة ping مع TTL للكشف عن نوع النظام
      const { stdout } = await execAsync(`ping -n 1 ${server.ip}`);

      if (stdout.includes('TTL=128') || stdout.includes('TTL=64')) {
        // TTL=128 عادة Windows, TTL=64 عادة Linux
        return stdout.includes('TTL=128') ? 'windows' : 'linux';
      }

      return 'unknown';
    } catch {
      return 'unknown';
    }
  }

  /**
   * تنفيذ أمر WMI
   */
  private async executeWMICommand(command: string): Promise<string> {
    try {
      const { stdout } = await execAsync(command);
      return stdout;
    } catch (error) {
      console.error('خطأ في تنفيذ أمر WMI:', error);
      return '';
    }
  }

  /**
   * معالجة نتائج CPU لـ Windows
   */
  private parseWindowsCPU(output: string): number {
    try {
      const match = output.match(/LoadPercentage=(\d+)/);
      return match ? parseInt(match[1]) : 0;
    } catch {
      return 0;
    }
  }

  /**
   * معالجة نتائج الذاكرة لـ Windows
   */
  private parseWindowsMemory(output: string): { usage: number; total: number; free: number } {
    try {
      const totalMatch = output.match(/TotalVisibleMemorySize=(\d+)/);
      const freeMatch = output.match(/FreePhysicalMemory=(\d+)/);

      if (totalMatch && freeMatch) {
        const total = parseInt(totalMatch[1]);
        const free = parseInt(freeMatch[1]);
        const used = total - free;
        const usage = (used / total) * 100;

        return { usage: Math.round(usage), total, free };
      }

      return { usage: 0, total: 0, free: 0 };
    } catch {
      return { usage: 0, total: 0, free: 0 };
    }
  }

  /**
   * معالجة نتائج الأقراص لـ Windows
   */
  private parseWindowsDisks(output: string): { avgUsage: number; disks: any[] } {
    try {
      const lines = output.split('\n').filter(line => line.includes('='));
      const disks: any[] = [];
      let totalUsage = 0;

      for (let i = 0; i < lines.length; i += 3) {
        const captionLine = lines[i];
        const freeLine = lines[i + 1];
        const sizeLine = lines[i + 2];

        if (captionLine && freeLine && sizeLine) {
          const caption = captionLine.split('=')[1]?.trim();
          const free = parseInt(freeLine.split('=')[1] || '0');
          const size = parseInt(sizeLine.split('=')[1] || '0');

          if (caption && size > 0) {
            const usage = Math.round(((size - free) / size) * 100);
            const sizeGB = Math.round(size / (1024 * 1024 * 1024));

            disks.push({
              id: `disk-${caption}`,
              name: caption,
              health: usage > 90 ? 'critical' : usage > 75 ? 'warning' : 'good',
              usage,
              size: `${sizeGB}GB`,
              badSectors: 0
            });

            totalUsage += usage;
          }
        }
      }

      const avgUsage = disks.length > 0 ? Math.round(totalUsage / disks.length) : 0;
      return { avgUsage, disks };
    } catch {
      return { avgUsage: 0, disks: [] };
    }
  }

  /**
   * معالجة نتائج وقت التشغيل لـ Windows
   */
  private parseWindowsUptime(output: string): number {
    try {
      const match = output.match(/LastBootUpTime=(\d{14})/);
      if (match) {
        const bootTime = match[1];
        const year = parseInt(bootTime.substr(0, 4));
        const month = parseInt(bootTime.substr(4, 2)) - 1;
        const day = parseInt(bootTime.substr(6, 2));
        const hour = parseInt(bootTime.substr(8, 2));
        const minute = parseInt(bootTime.substr(10, 2));
        const second = parseInt(bootTime.substr(12, 2));

        const bootDate = new Date(year, month, day, hour, minute, second);
        const uptime = (Date.now() - bootDate.getTime()) / 1000;

        return Math.round(uptime);
      }

      return 0;
    } catch {
      return 0;
    }
  }

  /**
   * معالجة نتائج CPU لـ Linux
   */
  private parseLinuxCPU(output: string): number {
    try {
      const usage = parseFloat(output.trim());
      return Math.round(usage);
    } catch {
      return 0;
    }
  }

  /**
   * معالجة نتائج الأقراص لـ Linux
   */
  private parseLinuxDisks(output: string): any[] {
    try {
      const lines = output.trim().split('\n');
      const disks: any[] = [];

      lines.forEach(line => {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 6) {
          const device = parts[0];
          const size = parts[1];
          const used = parts[2];
          const available = parts[3];
          const usage = parseInt(parts[4].replace('%', ''));
          const mountpoint = parts[5];

          disks.push({
            id: `disk-${device.replace(/[^a-zA-Z0-9]/g, '')}`,
            name: mountpoint,
            health: usage > 90 ? 'critical' : usage > 75 ? 'warning' : 'good',
            usage,
            size,
            badSectors: 0
          });
        }
      });

      return disks;
    } catch {
      return [];
    }
  }

  /**
   * مراقبة جميع السيرفرات
   */
  async monitorAllServers(): Promise<Map<string, RemoteSystemInfo | null>> {
    const results = new Map<string, RemoteSystemInfo | null>();

    const promises = Array.from(this.servers.keys()).map(async (serverId) => {
      try {
        const systemInfo = await this.monitorServer(serverId);
        results.set(serverId, systemInfo);
      } catch (error) {
        console.error(`خطأ في مراقبة السيرفر ${serverId}:`, error);
        results.set(serverId, null);
      }
    });

    await Promise.allSettled(promises);
    return results;
  }
}

// إنشاء instance مشترك
export const remoteMonitoring = new RemoteMonitoring();
