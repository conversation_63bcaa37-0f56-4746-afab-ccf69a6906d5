# دليل المطور - نظام مراقبة السيرفرات

## 📋 نظرة عامة على المشروع

نظام مراقبة السيرفرات هو تطبيق ويب شامل مطور باستخدام Next.js لمراقبة أداء السيرفرات في الوقت الفعلي. يوفر النظام واجهة عربية متكاملة مع دعم RTL ونظام تنبيهات متقدم.

## 🏗️ هيكل المشروع

```
server-monitor/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # Backend API Routes
│   │   │   ├── alerts/        # إدارة التنبيهات
│   │   │   ├── notifications/ # إرسال الإشعارات
│   │   │   ├── performance/   # بيانات الأداء التاريخية
│   │   │   ├── servers/       # معلومات السيرفرات
│   │   │   ├── settings/      # إعدادات النظام
│   │   │   └── system-analysis/ # تحليل النظام المتقدم
│   │   ├── reports/           # صفحة التقارير
│   │   ├── server/[id]/       # صفحة تفاصيل السيرفر
│   │   ├── settings/          # صفحة الإعدادات
│   │   ├── system-analysis/   # صفحة تحليل النظام
│   │   ├── globals.css        # الأنماط العامة
│   │   ├── layout.tsx         # التخطيط الرئيسي
│   │   └── page.tsx           # الصفحة الرئيسية
│   ├── components/            # مكونات React القابلة لإعادة الاستخدام
│   │   ├── AlertsPanel.tsx    # لوحة التنبيهات
│   │   ├── DashboardStats.tsx # إحصائيات لوحة التحكم
│   │   ├── PerformanceChart.tsx # رسوم الأداء البيانية
│   │   ├── ServerCard.tsx     # بطاقة السيرفر
│   │   ├── SystemErrorsPanel.tsx # لوحة أخطاء النظام
│   │   ├── TempFilesPanel.tsx # لوحة الملفات المؤقتة
│   │   └── LargeFilesPanel.tsx # لوحة الملفات الكبيرة
│   └── lib/                   # مكتبات ووظائف مساعدة
│       ├── notifications.ts   # نظام الإشعارات
│       ├── reportGenerator.ts # مولد التقارير
│       ├── systemMonitor.ts   # مراقب النظام
│       ├── systemAnalyzer.ts  # محلل النظام المتقدم
│       ├── database.ts        # إدارة قاعدة البيانات
│       ├── serverDiscovery.ts # اكتشاف السيرفرات
│       ├── remoteMonitoring.ts # المراقبة عن بُعد
│       └── realTimeAlerts.ts  # التنبيهات الفورية
├── data/                      # ملفات البيانات
│   └── settings.json         # إعدادات النظام المحفوظة
├── public/                    # الملفات العامة
├── package.json              # تبعيات المشروع
├── tailwind.config.js        # إعدادات Tailwind CSS
├── tsconfig.json            # إعدادات TypeScript
└── next.config.js           # إعدادات Next.js
```

## 📦 المكتبات والتبعيات المستخدمة

### Frontend Dependencies
- **Next.js 15.4.4**: إطار عمل React للتطبيقات الحديثة
- **React 19.1.0**: مكتبة واجهة المستخدم
- **TypeScript 5**: لغة البرمجة مع الأنواع الثابتة
- **Tailwind CSS 4**: إطار عمل CSS للتصميم السريع
- **Lucide React**: مكتبة الأيقونات
- **Recharts 3.1.0**: مكتبة الرسوم البيانية

### Backend Dependencies
- **systeminformation 5.27.7**: جمع معلومات النظام
- **node-os-utils 1.3.7**: مراقبة موارد النظام
- **nodemailer 7.0.5**: إرسال الإيميلات
- **ws 8.18.3**: WebSocket للاتصال المباشر

### Report Generation
- **jsPDF 3.0.1**: إنتاج ملفات PDF
- **html2canvas 1.4.1**: تحويل HTML إلى صور
- **chart.js 4.5.0**: رسوم بيانية متقدمة
- **react-chartjs-2 5.3.0**: تكامل Chart.js مع React

### Database & Storage
- **sqlite3 5.1.7**: قاعدة بيانات محلية
- **date-fns 4.1.0**: معالجة التواريخ

## 🔧 وظائف الملفات الرئيسية

### API Routes (`src/app/api/`)

#### `servers/route.ts`
- **الوظيفة**: جلب معلومات جميع السيرفرات
- **المسؤوليات**:
  - مراقبة استخدام CPU, RAM, Disk
  - فحص حالة الأقراص الصلبة
  - كشف Bad Sectors
  - حساب Uptime

#### `alerts/route.ts`
- **الوظيفة**: إدارة التنبيهات النشطة
- **المسؤوليات**:
  - إنشاء تنبيهات جديدة
  - جلب قائمة التنبيهات
  - تصنيف حسب الخطورة

#### `notifications/route.ts`
- **الوظيفة**: إرسال الإشعارات
- **المسؤوليات**:
  - إرسال إيميلات التنبيه
  - إرسال Webhook notifications
  - تسجيل حالة الإرسال

#### `performance/[serverId]/route.ts`
- **الوظيفة**: بيانات الأداء التاريخية
- **المسؤوليات**:
  - إنتاج بيانات وهمية للاختبار
  - تنسيق البيانات للرسوم البيانية
  - فلترة حسب الفترة الزمنية

#### `settings/route.ts`
- **الوظيفة**: إدارة إعدادات النظام
- **المسؤوليات**:
  - حفظ/جلب إعدادات SMTP
  - إدارة إعدادات Webhook
  - ضبط حدود التنبيهات

#### `system-analysis/route.ts`
- **الوظيفة**: تحليل النظام المتقدم
- **المسؤوليات**:
  - مراقبة أخطاء النظام (Windows Event Log / Linux Syslog)
  - اكتشاف وإدارة الملفات المؤقتة
  - البحث عن الملفات الكبيرة
  - حذف الملفات المؤقتة بأمان
  - تقديم تحليل شامل مع توصيات

### Components (`src/components/`)

#### `ServerCard.tsx`
- **الوظيفة**: عرض معلومات السيرفر في بطاقة
- **الميزات**:
  - عرض حالة السيرفر (متصل/تحذير/غير متصل)
  - شريط تقدم لاستخدام الموارد
  - تفاصيل الأقراص الصلبة
  - رابط لصفحة التفاصيل

#### `DashboardStats.tsx`
- **الوظيفة**: عرض الإحصائيات العامة
- **الميزات**:
  - عدد السيرفرات المتصلة
  - متوسط استخدام الموارد
  - عدد التنبيهات النشطة

#### `AlertsPanel.tsx`
- **الوظيفة**: لوحة التنبيهات
- **الميزات**:
  - عرض التنبيهات حسب الأولوية
  - تصنيف ملوّن حسب الخطورة
  - تحديث تلقائي

#### `PerformanceChart.tsx`
- **الوظيفة**: رسم بياني للأداء
- **الميزات**:
  - رسم خطوط متعددة (CPU, RAM, Disk)
  - تفاعلي مع Tooltips
  - دعم اللغة العربية

#### `SystemErrorsPanel.tsx`
- **الوظيفة**: لوحة أخطاء النظام
- **الميزات**:
  - عرض أخطاء Windows Event Log و Linux Syslog
  - تصنيف حسب الخطورة (حرج، خطأ، تحذير)
  - تحديث تلقائي كل 5 دقائق
  - فلترة وبحث في الأخطاء

#### `TempFilesPanel.tsx`
- **الوظيفة**: إدارة الملفات المؤقتة
- **الميزات**:
  - اكتشاف الملفات المؤقتة في مجلدات النظام
  - تحديد متعدد للملفات
  - حذف آمن مع تأكيد
  - عرض الحجم الإجمالي والمحرر

#### `LargeFilesPanel.tsx`
- **الوظيفة**: اكتشاف الملفات الكبيرة
- **الميزات**:
  - البحث عن الملفات الكبيرة مع حد أدنى قابل للتخصيص
  - فلترة وبحث متقدم
  - ترتيب حسب الحجم أو الاسم أو التاريخ
  - عرض المسار الكامل ومعلومات الملف

### Library Files (`src/lib/`)

#### `systemMonitor.ts`
- **الوظيفة**: مراقبة النظام الأساسية
- **المسؤوليات**:
  - جمع معلومات النظام
  - فحص الأقراص الصلبة
  - حساب الإحصائيات

#### `notifications.ts`
- **الوظيفة**: نظام الإشعارات المتكامل
- **المسؤوليات**:
  - إرسال إيميلات HTML
  - إرسال Webhook requests
  - إنشاء قوالب التنبيهات
  - فحص الحدود التلقائي

#### `reportGenerator.ts`
- **الوظيفة**: إنتاج التقارير PDF
- **المسؤوليات**:
  - تجميع بيانات السيرفرات
  - إنشاء تقارير مفصلة
  - إضافة الرسوم البيانية
  - تصدير PDF

#### `systemAnalyzer.ts`
- **الوظيفة**: تحليل النظام المتقدم
- **المسؤوليات**:
  - مراقبة أخطاء النظام (Windows Event Log / Linux Syslog)
  - اكتشاف الملفات المؤقتة في مجلدات النظام
  - البحث عن الملفات الكبيرة مع فلترة متقدمة
  - حذف الملفات المؤقتة بأمان
  - تقديم تحليل شامل مع إحصائيات

#### `database.ts`
- **الوظيفة**: إدارة قاعدة البيانات SQLite
- **المسؤوليات**:
  - إنشاء وإدارة جداول قاعدة البيانات
  - حفظ واسترجاع بيانات السيرفرات
  - إدارة التنبيهات وقواعد التنبيه
  - ضمان استمرارية البيانات

#### `serverDiscovery.ts`
- **الوظيفة**: اكتشاف السيرفرات في الشبكة
- **المسؤوليات**:
  - فحص الشبكة المحلية
  - اكتشاف الأجهزة المتصلة
  - تحديد نوع نظام التشغيل
  - فحص المنافذ والخدمات

#### `remoteMonitoring.ts`
- **الوظيفة**: المراقبة عن بُعد
- **المسؤوليات**:
  - مراقبة سيرفرات Windows عبر WMI
  - مراقبة سيرفرات Linux عبر SSH
  - جمع معلومات الأداء عن بُعد
  - إدارة الاتصالات الآمنة

#### `realTimeAlerts.ts`
- **الوظيفة**: نظام التنبيهات الفورية
- **المسؤوليات**:
  - مراقبة مستمرة للسيرفرات
  - تطبيق قواعد التنبيه الذكية
  - إرسال تنبيهات فورية
  - إدارة فترات التهدئة لتجنب الإزعاج

## 🚀 كيفية تشغيل النظام

### المتطلبات الأساسية
```bash
# Node.js 18+ مطلوب
node --version

# npm أو yarn
npm --version
```

### خطوات التشغيل

1. **تثبيت التبعيات**:
```bash
cd server-monitor
npm install
```

2. **تشغيل الخادم للتطوير**:
```bash
npm run dev
```

3. **الوصول للنظام**:
- افتح المتصفح على: `http://localhost:3000`

4. **بناء النظام للإنتاج**:
```bash
npm run build
npm start
```

### متغيرات البيئة (اختيارية)
إنشاء ملف `.env.local`:
```env
# إعدادات SMTP افتراضية
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# إعدادات Webhook
WEBHOOK_URL=https://your-webhook-url.com

# إعدادات قاعدة البيانات
DATABASE_URL=./data/monitoring.db
```

## 🐛 حل الأخطاء الشائعة

### خطأ: "Module not found"
```bash
# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install
```

### خطأ: "Port 3000 already in use"
```bash
# تغيير المنفذ
npm run dev -- -p 3001
```

### خطأ: "Permission denied" (Linux/Mac)
```bash
# إعطاء صلاحيات للملفات
chmod +x node_modules/.bin/*
```

### خطأ في إرسال الإيميل
1. تأكد من صحة إعدادات SMTP
2. استخدم App Password لـ Gmail
3. تحقق من إعدادات الأمان

### خطأ في قراءة معلومات النظام
```bash
# تثبيت مكتبات النظام (Linux)
sudo apt-get install build-essential

# Windows: تثبيت Visual Studio Build Tools
```

## 🔄 كيفية التطوير والتوسيع

### إضافة سيرفر جديد
1. تعديل `src/app/api/servers/route.ts`
2. إضافة معلومات السيرفر الجديد
3. تحديث واجهة المستخدم

### إضافة نوع تنبيه جديد
1. تعديل `src/lib/notifications.ts`
2. إضافة شروط التنبيه الجديدة
3. تحديث قوالب الإيميل

### إضافة رسم بياني جديد
1. إنشاء مكون جديد في `src/components/`
2. استخدام مكتبة Recharts
3. إضافة البيانات المطلوبة

### إضافة صفحة جديدة
1. إنشاء مجلد في `src/app/`
2. إضافة `page.tsx` و `layout.tsx`
3. تحديث التنقل

### تخصيص التصميم
1. تعديل `src/app/globals.css`
2. استخدام فئات Tailwind CSS
3. إضافة متغيرات CSS مخصصة

## 📊 إضافة مراقبة سيرفرات خارجية

### عبر SSH
```typescript
// في src/lib/remoteMonitor.ts
import { NodeSSH } from 'node-ssh';

export async function getRemoteServerInfo(host: string, username: string, privateKey: string) {
  const ssh = new NodeSSH();
  await ssh.connect({ host, username, privateKey });
  
  const result = await ssh.execCommand('top -bn1 | grep "Cpu(s)"');
  // معالجة النتائج...
}
```

### عبر API
```typescript
// في src/lib/apiMonitor.ts
export async function getServerInfoFromAPI(serverUrl: string, apiKey: string) {
  const response = await fetch(`${serverUrl}/api/system-info`, {
    headers: { 'Authorization': `Bearer ${apiKey}` }
  });
  return response.json();
}
```

## 🔒 الأمان والحماية

### حماية API Routes
```typescript
// middleware.ts
import { NextResponse } from 'next/server';

export function middleware(request: Request) {
  const apiKey = request.headers.get('x-api-key');
  if (!apiKey || apiKey !== process.env.API_KEY) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
}
```

### تشفير البيانات الحساسة
```typescript
import crypto from 'crypto';

export function encryptPassword(password: string): string {
  const algorithm = 'aes-256-cbc';
  const key = process.env.ENCRYPTION_KEY;
  const iv = crypto.randomBytes(16);
  
  const cipher = crypto.createCipher(algorithm, key);
  let encrypted = cipher.update(password, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  return encrypted;
}
```

## 📈 مراقبة الأداء والتحسين

### تحسين الاستعلامات
```typescript
// استخدام caching للبيانات
const cache = new Map();

export async function getCachedSystemInfo(serverId: string) {
  const cacheKey = `system-${serverId}`;
  const cached = cache.get(cacheKey);
  
  if (cached && Date.now() - cached.timestamp < 30000) {
    return cached.data;
  }
  
  const data = await getSystemInfo();
  cache.set(cacheKey, { data, timestamp: Date.now() });
  return data;
}
```

### تحسين حجم Bundle
```javascript
// next.config.js
module.exports = {
  experimental: {
    optimizeCss: true,
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
};
```

## 🚀 النشر والإنتاج

### النشر على Vercel
```bash
npm install -g vercel
vercel --prod
```

### النشر على Docker
```dockerfile
# Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

### النشر على VPS
```bash
# تثبيت PM2
npm install -g pm2

# تشغيل التطبيق
pm2 start npm --name "server-monitor" -- start
pm2 startup
pm2 save
```

## 📝 الصيانة والتحديث

### تحديث التبعيات
```bash
# فحص التحديثات المتاحة
npm outdated

# تحديث التبعيات
npm update

# تحديث Next.js
npm install next@latest
```

### النسخ الاحتياطي
```bash
# نسخ احتياطي للبيانات
cp -r data/ backup/data-$(date +%Y%m%d)/

# نسخ احتياطي لقاعدة البيانات
sqlite3 data/monitoring.db ".backup backup/db-$(date +%Y%m%d).db"
```

### مراقبة السجلات
```bash
# عرض سجلات PM2
pm2 logs server-monitor

# عرض سجلات النظام
tail -f /var/log/server-monitor.log
```

## 📞 الدعم والمساعدة

### الموارد المفيدة
- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Recharts Documentation](https://recharts.org/)

### الأخطاء الشائعة وحلولها
- راجع ملف `TROUBLESHOOTING.md` للمزيد من التفاصيل
- تحقق من سجلات المتصفح (F12 → Console)
- راجع سجلات الخادم في Terminal

## 🔧 أمثلة عملية للتطوير

### إضافة مراقبة جديدة (مثال: مراقبة درجة الحرارة)

1. **تعديل API السيرفرات**:
```typescript
// في src/app/api/servers/route.ts
import { exec } from 'child_process';

async function getTemperature(): Promise<number> {
  return new Promise((resolve) => {
    exec('sensors | grep "Core 0"', (error, stdout) => {
      if (error) resolve(0);
      const temp = stdout.match(/\+(\d+\.\d+)°C/);
      resolve(temp ? parseFloat(temp[1]) : 0);
    });
  });
}

// إضافة في دالة getSystemInfo
const temperature = await getTemperature();
```

2. **تحديث واجهة المستخدم**:
```typescript
// في src/components/ServerCard.tsx
<div className="flex items-center">
  <Thermometer className="w-4 h-4 text-orange-500 ml-1" />
  <span className="text-sm">
    درجة الحرارة: {server.temperature}°C
  </span>
</div>
```

### إضافة تنبيه مخصص

```typescript
// في src/lib/notifications.ts
export function checkTemperatureAlert(temp: number, serverName: string): Alert | null {
  if (temp > 80) {
    return createAlert(
      'error',
      'درجة حرارة مرتفعة',
      `درجة حرارة ${serverName} وصلت إلى ${temp}°C`,
      serverName,
      'critical'
    );
  }
  return null;
}
```

### إضافة صفحة إعدادات متقدمة

```typescript
// src/app/advanced-settings/page.tsx
'use client';

export default function AdvancedSettings() {
  const [settings, setSettings] = useState({
    autoRestart: false,
    maintenanceMode: false,
    debugMode: false,
  });

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">الإعدادات المتقدمة</h1>

      <div className="bg-white p-6 rounded-lg shadow">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={settings.autoRestart}
            onChange={(e) => setSettings(prev => ({
              ...prev,
              autoRestart: e.target.checked
            }))}
          />
          <span className="mr-2">إعادة التشغيل التلقائي</span>
        </label>
      </div>
    </div>
  );
}
```

## 🗄️ إعداد قاعدة البيانات الحقيقية

### استخدام SQLite

```typescript
// src/lib/database.ts
import sqlite3 from 'sqlite3';
import { open } from 'sqlite';

export async function initDatabase() {
  const db = await open({
    filename: './data/monitoring.db',
    driver: sqlite3.Database
  });

  await db.exec(`
    CREATE TABLE IF NOT EXISTS server_metrics (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      server_id TEXT NOT NULL,
      cpu_usage REAL NOT NULL,
      memory_usage REAL NOT NULL,
      disk_usage REAL NOT NULL,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS alerts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      server_id TEXT NOT NULL,
      type TEXT NOT NULL,
      message TEXT NOT NULL,
      severity TEXT NOT NULL,
      resolved BOOLEAN DEFAULT FALSE,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `);

  return db;
}

export async function saveMetrics(serverId: string, metrics: any) {
  const db = await initDatabase();
  await db.run(
    'INSERT INTO server_metrics (server_id, cpu_usage, memory_usage, disk_usage) VALUES (?, ?, ?, ?)',
    [serverId, metrics.cpu, metrics.memory, metrics.diskUsage]
  );
}
```

### استخدام PostgreSQL

```typescript
// src/lib/postgres.ts
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

export async function saveServerMetrics(serverId: string, metrics: any) {
  const client = await pool.connect();
  try {
    await client.query(
      'INSERT INTO server_metrics (server_id, cpu_usage, memory_usage, disk_usage) VALUES ($1, $2, $3, $4)',
      [serverId, metrics.cpu, metrics.memory, metrics.diskUsage]
    );
  } finally {
    client.release();
  }
}
```

## 🔄 إعداد التحديث التلقائي

### WebSocket للتحديث المباشر

```typescript
// src/lib/websocket.ts
import WebSocket from 'ws';

export class MonitoringWebSocket {
  private wss: WebSocket.Server;

  constructor(port: number) {
    this.wss = new WebSocket.Server({ port });
    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    this.wss.on('connection', (ws) => {
      console.log('عميل جديد متصل');

      // إرسال البيانات كل 30 ثانية
      const interval = setInterval(async () => {
        const data = await this.getLatestData();
        ws.send(JSON.stringify(data));
      }, 30000);

      ws.on('close', () => {
        clearInterval(interval);
      });
    });
  }

  private async getLatestData() {
    // جلب أحدث البيانات
    return {
      servers: await getServersData(),
      alerts: await getActiveAlerts(),
      timestamp: new Date().toISOString(),
    };
  }
}
```

### استخدام Server-Sent Events

```typescript
// src/app/api/events/route.ts
export async function GET() {
  const stream = new ReadableStream({
    start(controller) {
      const interval = setInterval(async () => {
        const data = await getSystemData();
        const message = `data: ${JSON.stringify(data)}\n\n`;
        controller.enqueue(new TextEncoder().encode(message));
      }, 30000);

      return () => clearInterval(interval);
    },
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  });
}
```

## 📱 إضافة دعم PWA (Progressive Web App)

### إعداد Service Worker

```javascript
// public/sw.js
const CACHE_NAME = 'server-monitor-v1';
const urlsToCache = [
  '/',
  '/static/css/main.css',
  '/static/js/main.js',
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        return response || fetch(event.request);
      })
  );
});
```

### إعداد Manifest

```json
// public/manifest.json
{
  "name": "نظام مراقبة السيرفرات",
  "short_name": "مراقب السيرفرات",
  "description": "نظام مراقبة السيرفرات في الوقت الفعلي",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#3b82f6",
  "dir": "rtl",
  "lang": "ar",
  "icons": [
    {
      "src": "/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/icon-512x512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

## 🔐 إضافة نظام المصادقة

### استخدام NextAuth.js

```bash
npm install next-auth
```

```typescript
// src/app/api/auth/[...nextauth]/route.ts
import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';

const handler = NextAuth({
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        username: { label: 'اسم المستخدم', type: 'text' },
        password: { label: 'كلمة المرور', type: 'password' }
      },
      async authorize(credentials) {
        // التحقق من بيانات المستخدم
        if (credentials?.username === 'admin' && credentials?.password === 'password') {
          return { id: '1', name: 'Admin', email: '<EMAIL>' };
        }
        return null;
      }
    })
  ],
  pages: {
    signIn: '/auth/signin',
  },
  session: {
    strategy: 'jwt',
  },
});

export { handler as GET, handler as POST };
```

## 📊 إضافة المزيد من الرسوم البيانية

### رسم بياني دائري للتوزيع

```typescript
// src/components/PieChart.tsx
import { PieChart, Pie, Cell, ResponsiveContainer, Legend } from 'recharts';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

export default function ServerDistributionChart({ data }: { data: any[] }) {
  return (
    <ResponsiveContainer width="100%" height={300}>
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
          outerRadius={80}
          fill="#8884d8"
          dataKey="value"
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Legend />
      </PieChart>
    </ResponsiveContainer>
  );
}
```

### رسم بياني للمقارنة

```typescript
// src/components/ComparisonChart.tsx
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

export default function ServerComparisonChart({ data }: { data: any[] }) {
  return (
    <ResponsiveContainer width="100%" height={400}>
      <BarChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip />
        <Bar dataKey="cpu" fill="#3B82F6" name="المعالج" />
        <Bar dataKey="memory" fill="#10B981" name="الذاكرة" />
        <Bar dataKey="disk" fill="#8B5CF6" name="التخزين" />
      </BarChart>
    </ResponsiveContainer>
  );
}
```

## 🚨 نظام التنبيهات المتقدم

### تنبيهات ذكية بالذكاء الاصطناعي

```typescript
// src/lib/aiAlerts.ts
export class AIAlertSystem {
  private patterns: Map<string, number[]> = new Map();

  analyzePattern(serverId: string, metrics: number[]): boolean {
    const historical = this.patterns.get(serverId) || [];
    historical.push(...metrics);

    // الاحتفاظ بآخر 100 قراءة فقط
    if (historical.length > 100) {
      historical.splice(0, historical.length - 100);
    }

    this.patterns.set(serverId, historical);

    // تحليل الاتجاه
    return this.detectAnomalies(historical);
  }

  private detectAnomalies(data: number[]): boolean {
    if (data.length < 10) return false;

    const recent = data.slice(-5);
    const average = data.reduce((a, b) => a + b, 0) / data.length;
    const recentAverage = recent.reduce((a, b) => a + b, 0) / recent.length;

    // إذا كان المتوسط الحديث أعلى بـ 50% من المتوسط العام
    return recentAverage > average * 1.5;
  }
}
```

### تنبيهات مجدولة

```typescript
// src/lib/scheduledAlerts.ts
import cron from 'node-cron';

export class ScheduledAlerts {
  constructor() {
    this.setupSchedules();
  }

  private setupSchedules() {
    // تقرير يومي في الساعة 8 صباحاً
    cron.schedule('0 8 * * *', async () => {
      await this.sendDailyReport();
    });

    // فحص أسبوعي يوم الأحد
    cron.schedule('0 9 * * 0', async () => {
      await this.sendWeeklyReport();
    });

    // تنبيه صحة النظام كل ساعة
    cron.schedule('0 * * * *', async () => {
      await this.checkSystemHealth();
    });
  }

  private async sendDailyReport() {
    const report = await generateDailyReport();
    await sendEmailReport(report, 'تقرير يومي - نظام مراقبة السيرفرات');
  }

  private async checkSystemHealth() {
    const servers = await getAllServers();
    for (const server of servers) {
      if (server.status === 'offline') {
        await sendUrgentAlert(`السيرفر ${server.name} غير متصل`);
      }
    }
  }
}
```

---

## 📋 قائمة المراجعة للنشر

### قبل النشر
- [ ] اختبار جميع الوظائف
- [ ] فحص الأمان والثغرات
- [ ] تحسين الأداء
- [ ] إعداد متغيرات البيئة
- [ ] إعداد قاعدة البيانات
- [ ] اختبار النسخ الاحتياطي
- [ ] إعداد مراقبة السجلات
- [ ] اختبار التنبيهات
- [ ] مراجعة الأذونات
- [ ] إعداد SSL/HTTPS

### بعد النشر
- [ ] مراقبة الأداء
- [ ] فحص السجلات
- [ ] اختبار التنبيهات
- [ ] التأكد من النسخ الاحتياطي
- [ ] مراقبة استخدام الموارد
- [ ] اختبار الوصول من أجهزة مختلفة

---

**ملاحظة**: هذا الدليل يتم تحديثه باستمرار. تأكد من مراجعة أحدث إصدار قبل البدء في التطوير.

للمزيد من المساعدة، راجع الوثائق الرسمية للمكتبات المستخدمة أو تواصل مع فريق التطوير.
