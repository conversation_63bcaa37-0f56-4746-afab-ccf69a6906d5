# دليل حذف الملفات الآمن - Safe File Deletion Guide

## نظرة عامة
تم تحديث نظام تحليل الملفات المؤقتة ليعرض الملفات الآمنة للحذف فقط، مع استبعاد الملفات الحساسة التي قد تسبب مشاكل في النظام.

## الملفات المستبعدة (الحساسة)

### 1. ملفات قواعد البيانات
- **امتدادات**: `.db`, `.sqlite`, `.mdb`, `.accdb`, `.sql`, `.bak`, `.backup`
- **السبب**: حذفها قد يؤدي إلى فقدان البيانات المهمة
- **أمثلة**: 
  - `database.db`
  - `backup.sql`
  - `users.sqlite`

### 2. ملفات النظام الحساسة
- **مجلدات**: `system32`, `windows`, `program files`, `programdata`
- **مجلدات المستخدم**: `appdata\local\microsoft`, `appdata\roaming\microsoft`
- **السبب**: ضرورية لتشغيل النظام والتطبيقات
- **أمثلة**:
  - `C:\Windows\System32\temp\system.tmp`
  - `C:\Program Files\App\temp\config.tmp`

### 3. ملفات التطبيقات الحساسة
- **امتدادات**: `.config`, `.ini`, `.reg`, `.dll`, `.exe`, `.sys`
- **السبب**: تحتوي على إعدادات مهمة أو ملفات تشغيل
- **أمثلة**:
  - `app.config`
  - `settings.ini`
  - `registry.reg`

### 4. ملفات قواعد البيانات المحددة
- **أنواع**: MySQL, PostgreSQL, MongoDB, Redis, Elasticsearch, Oracle, SQL Server
- **السبب**: ملفات قواعد البيانات النشطة
- **أمثلة**:
  - `mysql\temp\query.tmp`
  - `postgresql\temp\index.tmp`

### 5. ملفات التطوير المهمة
- **مجلدات**: `node_modules`, `.git`, `.svn`, `vendor`, `composer`
- **السبب**: ضرورية لعمل المشاريع البرمجية
- **أمثلة**:
  - `node_modules\temp\cache.tmp`
  - `.git\temp\objects.tmp`

### 6. ملفات الأمان والشهادات
- **امتدادات**: `.key`, `.pem`, `.crt`, `.cert`, `.p12`, `.pfx`
- **السبب**: ملفات أمان حساسة
- **أمثلة**:
  - `ssl.key`
  - `certificate.pem`

### 7. ملفات التشغيل النشطة
- **امتدادات**: `.pid`, `.lock`, `.running`
- **السبب**: تشير إلى عمليات نشطة
- **أمثلة**:
  - `app.pid`
  - `database.lock`

## الملفات المسموح بحذفها (الآمنة)

### 1. ملفات مؤقتة عامة
- **امتدادات**: `.tmp`, `.temp`, `~$*`, `.cache`, `.old`
- **أمثلة**:
  - `document.tmp`
  - `cache.temp`
  - `~$backup.old`

### 2. ملفات المتصفحات
- **مجلدات**: `browser*cache`, `chrome*cache`, `firefox*cache`, `edge*cache`
- **أمثلة**:
  - `Chrome\Cache\temp.cache`
  - `Firefox\Cache\image.tmp`

### 3. ملفات التحديث والتنزيل
- **مجلدات**: `update*temp`, `download*temp`, `install*temp`
- **امتدادات**: `.part`, `.crdownload`, `.download`
- **أمثلة**:
  - `update_temp\installer.tmp`
  - `download.part`

### 4. ملفات النصوص والسجلات
- **امتدادات**: `.txt`, `.log`, `.out` (في مجلدات temp فقط)
- **أمثلة**:
  - `temp\debug.log`
  - `tmp\output.txt`

### 5. ملفات الضغط المؤقتة
- **امتدادات**: `.zip.tmp`, `.rar.tmp`
- **مجلدات**: `extract*temp`
- **أمثلة**:
  - `archive.zip.tmp`
  - `extract_temp\file.tmp`

## المجلدات الآمنة للفحص

### Windows
- `%TEMP%` - مجلد المستخدم المؤقت
- `%TMP%` - مجلد النظام المؤقت
- `C:\Temp` - مجلد عام
- `C:\tmp` - مجلد عام بديل
- `%LOCALAPPDATA%\Temp` - مجلد البيانات المحلية
- `C:\Windows\SoftwareDistribution\Download` - ملفات التحديث

### Linux
- `/tmp` - مجلد مؤقت عام
- `/var/tmp` - مجلد مؤقت متغير
- `/dev/shm` - ذاكرة مشتركة

## آلية الفلترة الذكية

### 1. فحص الامتدادات
```typescript
// فحص الامتدادات الحساسة
const sensitiveExtensions = ['.db', '.sqlite', '.config', '.key'];
if (sensitiveExtensions.some(ext => fileName.endsWith(ext))) {
  return false; // غير آمن للحذف
}
```

### 2. فحص المسارات
```typescript
// فحص المسارات الحساسة
const sensitivePaths = [/system32/i, /program files/i];
if (sensitivePaths.some(pattern => pattern.test(filePath))) {
  return false; // غير آمن للحذف
}
```

### 3. فحص الكلمات المفتاحية
```typescript
// فحص الكلمات الحساسة
const sensitiveWords = ['database', 'config', 'system'];
if (sensitiveWords.some(word => fileName.includes(word))) {
  return false; // غير آمن للحذف
}
```

## إرشادات الأمان

### 1. للمطورين
- **اختبر دائماً**: اختبر الفلترة على بيئة تطوير أولاً
- **راجع القوائم**: راجع قوائم الملفات قبل الحذف
- **احتفظ بنسخ احتياطية**: قم بعمل نسخ احتياطية قبل الحذف الجماعي

### 2. للمستخدمين
- **راجع الملفات**: راجع قائمة الملفات قبل الحذف
- **ابدأ بالصغير**: احذف ملفات قليلة أولاً للاختبار
- **تجنب الحذف الجماعي**: لا تحذف آلاف الملفات دفعة واحدة

### 3. للمديرين
- **راقب السجلات**: راقب سجلات النظام بعد الحذف
- **اختبر الوظائف**: تأكد من عمل جميع التطبيقات بعد الحذف
- **خطة استرداد**: احتفظ بخطة لاسترداد الملفات المحذوفة

## رسائل النظام

### رسالة الأمان في الواجهة
```
ملفات آمنة للحذف
يعرض النظام الملفات المؤقتة الآمنة للحذف فقط. 
تم استبعاد ملفات قواعد البيانات والملفات الحساسة تلقائياً لحماية النظام.
```

### رسائل التأكيد
- **قبل الحذف**: "هل أنت متأكد من حذف X ملف؟"
- **بعد الحذف**: "تم حذف X ملف بنجاح، فشل في حذف Y ملف"
- **في حالة الخطأ**: "فشل في حذف بعض الملفات، تحقق من الصلاحيات"

## استكشاف الأخطاء

### مشاكل شائعة
1. **لا توجد ملفات للعرض**
   - السبب: جميع الملفات محمية
   - الحل: تحقق من وجود ملفات مؤقتة فعلية

2. **فشل في الحذف**
   - السبب: نقص صلاحيات أو ملف مستخدم
   - الحل: تشغيل كمدير أو إغلاق التطبيقات

3. **حذف بطيء**
   - السبب: عدد كبير من الملفات
   - الحل: استخدام التصفح والحذف التدريجي

## التحديثات المستقبلية

### ميزات مخططة
- **قوائم مخصصة**: إمكانية تخصيص قوائم الملفات الآمنة
- **فحص متقدم**: فحص محتوى الملفات للتأكد من الأمان
- **نسخ احتياطية تلقائية**: نسخ احتياطي قبل الحذف
- **استرداد الملفات**: إمكانية استرداد الملفات المحذوفة

### تحسينات الأمان
- **تشفير القوائم**: تشفير قوائم الملفات الحساسة
- **مراجعة دورية**: مراجعة دورية لقوائم الأمان
- **تقارير مفصلة**: تقارير مفصلة عن عمليات الحذف
