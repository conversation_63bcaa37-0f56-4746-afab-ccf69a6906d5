'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

interface PerformanceData {
  timestamp: string;
  cpu: number;
  memory: number;
  diskUsage: number;
}

interface PerformanceChartProps {
  data: PerformanceData[];
}

export default function PerformanceChart({ data }: PerformanceChartProps) {
  // إذا لم تكن هناك بيانات، أنشئ بيانات تجريبية
  const chartData = data.length > 0 ? data : generateSampleData();

  return (
    <div className="w-full h-80">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            dataKey="timestamp"
            tickFormatter={(value) => new Date(value).toLocaleTimeString('en-US', {
              hour: '2-digit',
              minute: '2-digit'
            })}
          />
          <YAxis domain={[0, 100]} />
          <Tooltip
            labelFormatter={(value) => new Date(value).toLocaleString('en-US')}
            formatter={(value: number, name: string) => [
              `${value}%`,
              name === 'cpu' ? 'المعالج' :
                name === 'memory' ? 'الذاكرة' : 'التخزين'
            ]}
          />
          <Legend
            formatter={(value) =>
              value === 'cpu' ? 'المعالج' :
                value === 'memory' ? 'الذاكرة' : 'التخزين'
            }
          />
          <Line
            type="monotone"
            dataKey="cpu"
            stroke="#3B82F6"
            strokeWidth={2}
            dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6 }}
          />
          <Line
            type="monotone"
            dataKey="memory"
            stroke="#10B981"
            strokeWidth={2}
            dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6 }}
          />
          <Line
            type="monotone"
            dataKey="diskUsage"
            stroke="#8B5CF6"
            strokeWidth={2}
            dot={{ fill: '#8B5CF6', strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}

function generateSampleData(): PerformanceData[] {
  const data: PerformanceData[] = [];
  const now = new Date();

  for (let i = 23; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000); // كل ساعة
    data.push({
      timestamp: timestamp.toISOString(),
      cpu: Math.floor(Math.random() * 40) + 30, // 30-70%
      memory: Math.floor(Math.random() * 30) + 40, // 40-70%
      diskUsage: Math.floor(Math.random() * 20) + 50, // 50-70%
    });
  }

  return data;
}
