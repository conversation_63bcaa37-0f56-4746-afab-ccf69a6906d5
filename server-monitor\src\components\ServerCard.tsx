'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Server, HardDrive, Cpu, MemoryStick, Activity, AlertTriangle, CheckCircle, XCircle, ExternalLink } from 'lucide-react';

interface ServerProps {
  server: {
    id: string;
    name: string;
    status: 'online' | 'offline' | 'warning';
    cpu: number;
    memory: number;
    diskUsage: number;
    disks: Array<{
      id: string;
      name: string;
      health: 'good' | 'warning' | 'critical';
      usage: number;
      size: string;
      badSectors?: number;
    }>;
    lastUpdate: string;
  };
}

export default function ServerCard({ server }: ServerProps) {
  const router = useRouter();
  const [expanded, setExpanded] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'offline': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return <CheckCircle className="w-4 h-4" />;
      case 'warning': return <AlertTriangle className="w-4 h-4" />;
      case 'offline': return <XCircle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const getDiskHealthColor = (health: string) => {
    switch (health) {
      case 'good': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getUsageColor = (usage: number) => {
    if (usage >= 90) return 'bg-red-500';
    if (usage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Server className="w-6 h-6 text-blue-600 ml-2" />
            <h3 className="text-lg font-semibold text-gray-900">{server.name}</h3>
            <button
              onClick={() => router.push(`/server/${server.id}`)}
              className="mr-2 p-1 text-gray-400 hover:text-blue-600 transition-colors"
              title="عرض التفاصيل"
            >
              <ExternalLink className="w-4 h-4" />
            </button>
          </div>
          <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(server.status)}`}>
            {getStatusIcon(server.status)}
            <span className="mr-1">
              {server.status === 'online' ? 'متصل' :
                server.status === 'warning' ? 'تحذير' : 'غير متصل'}
            </span>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="p-4">
        <div className="grid grid-cols-3 gap-4 mb-4">
          {/* CPU */}
          <div className="text-center">
            <Cpu className="w-6 h-6 text-blue-600 mx-auto mb-1" />
            <div className="text-sm text-gray-600">المعالج</div>
            <div className="text-lg font-semibold">{server.cpu}%</div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
              <div
                className={`h-2 rounded-full ${getUsageColor(server.cpu)}`}
                style={{ width: `${server.cpu}%` }}
              ></div>
            </div>
          </div>

          {/* Memory */}
          <div className="text-center">
            <MemoryStick className="w-6 h-6 text-green-600 mx-auto mb-1" />
            <div className="text-sm text-gray-600">الذاكرة</div>
            <div className="text-lg font-semibold">{server.memory}%</div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
              <div
                className={`h-2 rounded-full ${getUsageColor(server.memory)}`}
                style={{ width: `${server.memory}%` }}
              ></div>
            </div>
          </div>

          {/* Disk */}
          <div className="text-center">
            <HardDrive className="w-6 h-6 text-purple-600 mx-auto mb-1" />
            <div className="text-sm text-gray-600">التخزين</div>
            <div className="text-lg font-semibold">{server.diskUsage}%</div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
              <div
                className={`h-2 rounded-full ${getUsageColor(server.diskUsage)}`}
                style={{ width: `${server.diskUsage}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Disks Details Toggle */}
        <button
          onClick={() => setExpanded(!expanded)}
          className="w-full text-sm text-blue-600 hover:text-blue-800 font-medium"
        >
          {expanded ? 'إخفاء تفاصيل الهاردات' : `عرض تفاصيل الهاردات (${server.disks.length})`}
        </button>

        {/* Expanded Disks */}
        {expanded && (
          <div className="mt-4 space-y-2">
            {server.disks.map((disk) => (
              <div key={disk.id} className="bg-gray-50 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <HardDrive className={`w-4 h-4 ml-2 ${getDiskHealthColor(disk.health)}`} />
                    <span className="text-sm font-medium">{disk.name}</span>
                    <span className="text-xs text-gray-500 mr-2">({disk.size})</span>
                  </div>
                  <span className={`text-xs font-medium ${getDiskHealthColor(disk.health)}`}>
                    {disk.health === 'good' ? 'جيد' :
                      disk.health === 'warning' ? 'تحذير' : 'خطر'}
                  </span>
                </div>

                <div className="flex items-center justify-between text-xs text-gray-600">
                  <span>الاستخدام: {disk.usage}%</span>
                  {disk.badSectors && disk.badSectors > 0 && (
                    <span className="text-red-600">Bad Sectors: {disk.badSectors}</span>
                  )}
                </div>

                <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                  <div
                    className={`h-1.5 rounded-full ${getUsageColor(disk.usage)}`}
                    style={{ width: `${disk.usage}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Last Update */}
        <div className="mt-4 text-xs text-gray-500 text-center">
          آخر تحديث: {new Date(server.lastUpdate).toLocaleString('en-US')}
        </div>
      </div>
    </div>
  );
}
