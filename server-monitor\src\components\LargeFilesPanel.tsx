'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { HardDrive, RefreshCw, File, FolderOpen, Search, Filter, Loader2 } from 'lucide-react';

interface LargeFile {
  path: string;
  name: string;
  size: number;
  sizeFormatted: string;
  lastModified: Date;
  directory: string;
}

interface LargeFilesData {
  files: LargeFile[];
  count: number;
  minSizeMB: number;
}

export default function LargeFilesPanel() {
  const [largeData, setLargeData] = useState<LargeFilesData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [minSizeMB, setMinSizeMB] = useState(100);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'size' | 'name' | 'date'>('size');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const fetchLargeFiles = async (minSize: number = minSizeMB) => {
    try {
      setRefreshing(true);
      const response = await fetch(`/api/system-analysis?type=large-files&minSize=${minSize}`);
      const result = await response.json();

      if (result.success) {
        // تحويل التواريخ من string إلى Date
        const filesWithDates = result.data.files.map((file: any) => ({
          ...file,
          lastModified: new Date(file.lastModified)
        }));

        setLargeData({
          ...result.data,
          files: filesWithDates
        });
      }
    } catch (error) {
      console.error('خطأ في جلب الملفات الكبيرة:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchLargeFiles();
  }, []);

  const handleMinSizeChange = (newMinSize: number) => {
    setMinSizeMB(newMinSize);
    fetchLargeFiles(newMinSize);
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getFilteredAndSortedFiles = () => {
    if (!largeData) return [];

    let filtered = largeData.files;

    // تطبيق البحث
    if (searchTerm) {
      filtered = filtered.filter(file =>
        file.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        file.directory.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // تطبيق الترتيب
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'size':
          comparison = a.size - b.size;
          break;
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'date':
          comparison = a.lastModified.getTime() - b.lastModified.getTime();
          break;
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });

    return filtered;
  };

  const getTotalSize = () => {
    if (!largeData) return 0;
    return largeData.files.reduce((total, file) => total + file.size, 0);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const filteredFiles = getFilteredAndSortedFiles();

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-center h-32">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-500" />
          <span className="mr-3 text-gray-600">جاري تحميل الملفات الكبيرة...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <HardDrive className="w-6 h-6 text-purple-500 ml-3" />
            <h2 className="text-xl font-bold text-gray-800">الملفات الكبيرة</h2>
            {largeData && (
              <span className="mr-3 px-2 py-1 bg-purple-100 text-purple-800 text-sm rounded-full">
                {largeData.count} ملف
              </span>
            )}
          </div>
          <button
            onClick={() => fetchLargeFiles()}
            disabled={refreshing}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ml-2 ${refreshing ? 'animate-spin' : ''}`} />
            تحديث
          </button>
        </div>

        {/* Statistics */}
        {largeData && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-sm text-purple-600">إجمالي الحجم</div>
              <div className="text-2xl font-bold text-purple-800">{formatFileSize(getTotalSize())}</div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-sm text-blue-600">عدد الملفات</div>
              <div className="text-2xl font-bold text-blue-800">{largeData.count}</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-sm text-green-600">الحد الأدنى</div>
              <div className="text-2xl font-bold text-green-800">{largeData.minSizeMB} MB</div>
            </div>
          </div>
        )}

        {/* Filters and Search */}
        <div className="flex flex-col md:flex-row gap-4">
          {/* Size Filter */}
          <div className="flex items-center">
            <Filter className="w-4 h-4 text-gray-500 ml-2" />
            <label className="text-sm text-gray-600 ml-2">الحد الأدنى (MB):</label>
            <select
              value={minSizeMB}
              onChange={(e) => handleMinSizeChange(Number(e.target.value))}
              className="px-3 py-1 border border-gray-300 rounded-lg text-sm"
            >
              <option value={50}>50 MB</option>
              <option value={100}>100 MB</option>
              <option value={500}>500 MB</option>
              <option value={1000}>1 GB</option>
              <option value={5000}>5 GB</option>
            </select>
          </div>

          {/* Search */}
          <div className="flex items-center flex-1">
            <Search className="w-4 h-4 text-gray-500 ml-2" />
            <input
              type="text"
              placeholder="البحث في أسماء الملفات أو المجلدات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 px-3 py-1 border border-gray-300 rounded-lg text-sm"
            />
          </div>

          {/* Sort */}
          <div className="flex items-center gap-2">
            <label className="text-sm text-gray-600">ترتيب:</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'size' | 'name' | 'date')}
              className="px-3 py-1 border border-gray-300 rounded-lg text-sm"
            >
              <option value="size">الحجم</option>
              <option value="name">الاسم</option>
              <option value="date">التاريخ</option>
            </select>
            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="px-2 py-1 bg-gray-100 rounded text-sm hover:bg-gray-200"
            >
              {sortOrder === 'desc' ? '↓' : '↑'}
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {filteredFiles.length === 0 ? (
          <div className="text-center py-8">
            <File className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 text-lg">
              {searchTerm ? 'لا توجد ملفات تطابق البحث' : 'لا توجد ملفات كبيرة'}
            </p>
            <p className="text-gray-400 text-sm">
              {searchTerm ? 'جرب تغيير مصطلح البحث' : `لا توجد ملفات أكبر من ${minSizeMB} MB`}
            </p>
          </div>
        ) : (
          <div className="space-y-2">
            {filteredFiles.map((file, index) => (
              <div
                key={`${file.path}-${index}`}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center flex-1">
                    <File className="w-5 h-5 text-gray-400 ml-3" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-800">{file.name}</div>
                      <div className="flex items-center text-sm text-gray-500 mt-1">
                        <FolderOpen className="w-4 h-4 ml-1" />
                        <span className="truncate max-w-md">{file.directory}</span>
                      </div>
                      <div className="text-xs text-gray-400 mt-1">
                        آخر تعديل: {formatTimestamp(file.lastModified)}
                      </div>
                    </div>
                  </div>
                  <div className="text-left">
                    <div className="text-lg font-bold text-purple-600">
                      {file.sizeFormatted}
                    </div>
                    <div className="text-xs text-gray-500">
                      {(file.size / (1024 * 1024)).toFixed(1)} MB
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      {filteredFiles.length > 0 && (
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>عرض {filteredFiles.length} من {largeData?.count || 0} ملف</span>
            <span>إجمالي الحجم المعروض: {formatFileSize(filteredFiles.reduce((total, file) => total + file.size, 0))}</span>
          </div>
        </div>
      )}
    </div>
  );
}
