'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ArrowRight, Server, Cpu, MemoryStick, HardDrive, Activity, AlertTriangle } from 'lucide-react';
import PerformanceChart from '@/components/PerformanceChart';

export default function ServerDetails() {
  const params = useParams();
  const router = useRouter();
  const [server, setServer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [performanceData, setPerformanceData] = useState([]);

  useEffect(() => {
    fetchServerDetails();
    fetchPerformanceData();

    // تحديث البيانات كل 10 ثوان
    const interval = setInterval(() => {
      fetchServerDetails();
      fetchPerformanceData();
    }, 10000);

    return () => clearInterval(interval);
  }, [params.id]);

  const fetchServerDetails = async () => {
    try {
      const response = await fetch('/api/servers');
      const servers = await response.json();
      const currentServer = servers.find(s => s.id === params.id);
      setServer(currentServer);
      setLoading(false);
    } catch (error) {
      console.error('خطأ في جلب تفاصيل السيرفر:', error);
      setLoading(false);
    }
  };

  const fetchPerformanceData = async () => {
    try {
      const response = await fetch(`/api/performance/${params.id}`);
      const data = await response.json();
      setPerformanceData(data);
    } catch (error) {
      console.error('خطأ في جلب بيانات الأداء:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Activity className="w-12 h-12 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">جاري تحميل تفاصيل السيرفر...</p>
        </div>
      </div>
    );
  }

  if (!server) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Server className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">السيرفر غير موجود</h3>
          <p className="text-gray-500 mb-4">لم يتم العثور على السيرفر المطلوب</p>
          <button
            onClick={() => router.push('/')}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            العودة للرئيسية
          </button>
        </div>
      </div>
    );
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'online': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'offline': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center">
              <button
                onClick={() => router.push('/')}
                className="ml-4 p-2 text-gray-400 hover:text-gray-600"
              >
                <ArrowRight className="w-6 h-6" />
              </button>
              <Server className="w-8 h-8 text-blue-600 ml-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{server.name}</h1>
                <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(server.status)}`}>
                  {server.status === 'online' ? 'متصل' :
                    server.status === 'warning' ? 'تحذير' : 'غير متصل'}
                </div>
              </div>
            </div>
            <div className="text-sm text-gray-500">
              آخر تحديث: {new Date(server.lastUpdate).toLocaleString('en-US')}
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Performance Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <div className="flex items-center">
              <Cpu className="w-8 h-8 text-blue-600 ml-3" />
              <div>
                <p className="text-sm font-medium text-gray-600">المعالج</p>
                <p className="text-3xl font-bold text-gray-900">{server.cpu}%</p>
              </div>
            </div>
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className={`h-3 rounded-full ${server.cpu > 80 ? 'bg-red-500' : server.cpu > 60 ? 'bg-yellow-500' : 'bg-green-500'}`}
                  style={{ width: `${server.cpu}%` }}
                ></div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <div className="flex items-center">
              <MemoryStick className="w-8 h-8 text-green-600 ml-3" />
              <div>
                <p className="text-sm font-medium text-gray-600">الذاكرة</p>
                <p className="text-3xl font-bold text-gray-900">{server.memory}%</p>
              </div>
            </div>
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className={`h-3 rounded-full ${server.memory > 80 ? 'bg-red-500' : server.memory > 60 ? 'bg-yellow-500' : 'bg-green-500'}`}
                  style={{ width: `${server.memory}%` }}
                ></div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <div className="flex items-center">
              <HardDrive className="w-8 h-8 text-purple-600 ml-3" />
              <div>
                <p className="text-sm font-medium text-gray-600">التخزين</p>
                <p className="text-3xl font-bold text-gray-900">{server.diskUsage}%</p>
              </div>
            </div>
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className={`h-3 rounded-full ${server.diskUsage > 80 ? 'bg-red-500' : server.diskUsage > 60 ? 'bg-yellow-500' : 'bg-green-500'}`}
                  style={{ width: `${server.diskUsage}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Performance Chart */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">الأداء التاريخي</h2>
          <PerformanceChart data={performanceData} />
        </div>

        {/* Disks Details */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">تفاصيل الهاردات</h2>
          <div className="space-y-4">
            {server.disks.map((disk) => (
              <div key={disk.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <HardDrive className={`w-5 h-5 ml-2 ${disk.health === 'good' ? 'text-green-600' :
                      disk.health === 'warning' ? 'text-yellow-600' : 'text-red-600'
                      }`} />
                    <span className="font-medium">{disk.name}</span>
                    <span className="text-sm text-gray-500 mr-2">({disk.size})</span>
                  </div>
                  <div className="flex items-center">
                    {disk.badSectors > 0 && (
                      <div className="flex items-center text-red-600 text-sm ml-4">
                        <AlertTriangle className="w-4 h-4 ml-1" />
                        Bad Sectors: {disk.badSectors}
                      </div>
                    )}
                    <span className={`text-sm font-medium ${disk.health === 'good' ? 'text-green-600' :
                      disk.health === 'warning' ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                      {disk.health === 'good' ? 'جيد' :
                        disk.health === 'warning' ? 'تحذير' : 'خطر'}
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                  <span>الاستخدام: {disk.usage}%</span>
                  <span>المساحة المتاحة: {Math.round((100 - disk.usage) * parseInt(disk.size) / 100)}GB</span>
                </div>

                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${disk.usage > 90 ? 'bg-red-500' :
                      disk.usage > 75 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                    style={{ width: `${disk.usage}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </main>
    </div>
  );
}
