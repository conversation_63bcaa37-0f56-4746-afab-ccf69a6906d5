import sqlite3 from 'sqlite3';
import { open, Database } from 'sqlite';
import path from 'path';
import fs from 'fs';

export interface ServerRecord {
  id: string;
  name: string;
  ip: string;
  os: string;
  credentials?: string; // JSON string
  status: string;
  created_at: string;
  updated_at: string;
}

export interface AlertRecord {
  id: string;
  server_id: string;
  type: string;
  title: string;
  message: string;
  severity: string;
  category: string;
  threshold_data?: string; // JSON string
  acknowledged: number; // 0 or 1
  resolved: number; // 0 or 1
  created_at: string;
  updated_at: string;
}

export interface AlertRuleRecord {
  id: string;
  name: string;
  category: string;
  condition: string;
  threshold: number;
  severity: string;
  enabled: number; // 0 or 1
  cooldown: number;
  last_triggered?: string;
  created_at: string;
  updated_at: string;
}

/**
 * مدير قاعدة البيانات
 */
export class DatabaseManager {
  private db: Database | null = null;
  private dbPath: string;

  constructor() {
    // إنشاء مجلد البيانات إذا لم يكن موجوداً
    const dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    this.dbPath = path.join(dataDir, 'monitoring.db');
  }

  /**
   * الاتصال بقاعدة البيانات
   */
  async connect(): Promise<void> {
    if (this.db) {
      return;
    }

    try {
      this.db = await open({
        filename: this.dbPath,
        driver: sqlite3.Database
      });

      console.log(`تم الاتصال بقاعدة البيانات: ${this.dbPath}`);
      await this.initializeTables();
    } catch (error) {
      console.error('خطأ في الاتصال بقاعدة البيانات:', error);
      throw error;
    }
  }

  /**
   * إنشاء الجداول
   */
  private async initializeTables(): Promise<void> {
    if (!this.db) {
      throw new Error('قاعدة البيانات غير متصلة');
    }

    try {
      // جدول السيرفرات
      await this.db.exec(`
        CREATE TABLE IF NOT EXISTS servers (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          ip TEXT NOT NULL UNIQUE,
          os TEXT NOT NULL DEFAULT 'unknown',
          credentials TEXT,
          status TEXT NOT NULL DEFAULT 'offline',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // جدول التنبيهات
      await this.db.exec(`
        CREATE TABLE IF NOT EXISTS alerts (
          id TEXT PRIMARY KEY,
          server_id TEXT NOT NULL,
          type TEXT NOT NULL,
          title TEXT NOT NULL,
          message TEXT NOT NULL,
          severity TEXT NOT NULL,
          category TEXT NOT NULL,
          threshold_data TEXT,
          acknowledged INTEGER DEFAULT 0,
          resolved INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (server_id) REFERENCES servers (id) ON DELETE CASCADE
        )
      `);

      // جدول قواعد التنبيهات
      await this.db.exec(`
        CREATE TABLE IF NOT EXISTS alert_rules (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          category TEXT NOT NULL,
          condition TEXT NOT NULL,
          threshold REAL NOT NULL,
          severity TEXT NOT NULL,
          enabled INTEGER DEFAULT 1,
          cooldown INTEGER DEFAULT 5,
          last_triggered DATETIME,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // إنشاء الفهارس
      await this.db.exec(`
        CREATE INDEX IF NOT EXISTS idx_alerts_server_id ON alerts (server_id);
        CREATE INDEX IF NOT EXISTS idx_alerts_created_at ON alerts (created_at);
        CREATE INDEX IF NOT EXISTS idx_alerts_severity ON alerts (severity);
        CREATE INDEX IF NOT EXISTS idx_servers_ip ON servers (ip);
        CREATE INDEX IF NOT EXISTS idx_alert_rules_category ON alert_rules (category);
      `);

      console.log('تم إنشاء جداول قاعدة البيانات بنجاح');

      // إدراج القواعد الافتراضية
      await this.insertDefaultAlertRules();
    } catch (error) {
      console.error('خطأ في إنشاء الجداول:', error);
      throw error;
    }
  }

  /**
   * إدراج قواعد التنبيهات الافتراضية
   */
  private async insertDefaultAlertRules(): Promise<void> {
    if (!this.db) return;

    const defaultRules = [
      {
        id: 'cpu-high',
        name: 'استخدام عالي للمعالج',
        category: 'cpu',
        condition: 'greater_than',
        threshold: 80,
        severity: 'high',
        enabled: 1,
        cooldown: 5
      },
      {
        id: 'cpu-critical',
        name: 'استخدام حرج للمعالج',
        category: 'cpu',
        condition: 'greater_than',
        threshold: 95,
        severity: 'critical',
        enabled: 1,
        cooldown: 2
      },
      {
        id: 'memory-high',
        name: 'استخدام عالي للذاكرة',
        category: 'memory',
        condition: 'greater_than',
        threshold: 85,
        severity: 'high',
        enabled: 1,
        cooldown: 5
      },
      {
        id: 'memory-critical',
        name: 'استخدام حرج للذاكرة',
        category: 'memory',
        condition: 'greater_than',
        threshold: 95,
        severity: 'critical',
        enabled: 1,
        cooldown: 2
      },
      {
        id: 'disk-high',
        name: 'مساحة قرص منخفضة',
        category: 'disk',
        condition: 'greater_than',
        threshold: 85,
        severity: 'medium',
        enabled: 1,
        cooldown: 10
      },
      {
        id: 'disk-critical',
        name: 'مساحة قرص حرجة',
        category: 'disk',
        condition: 'greater_than',
        threshold: 95,
        severity: 'critical',
        enabled: 1,
        cooldown: 5
      }
    ];

    for (const rule of defaultRules) {
      try {
        const existing = await this.db.get(
          'SELECT id FROM alert_rules WHERE id = ?',
          [rule.id]
        );

        if (!existing) {
          await this.db.run(`
            INSERT INTO alert_rules (id, name, category, condition, threshold, severity, enabled, cooldown)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            rule.id,
            rule.name,
            rule.category,
            rule.condition,
            rule.threshold,
            rule.severity,
            rule.enabled,
            rule.cooldown
          ]);
        }
      } catch (error) {
        console.error(`خطأ في إدراج القاعدة ${rule.id}:`, error);
      }
    }
  }

  /**
   * إضافة سيرفر جديد
   */
  async addServer(server: Omit<ServerRecord, 'created_at' | 'updated_at'>): Promise<void> {
    if (!this.db) {
      await this.connect();
    }

    try {
      await this.db!.run(`
        INSERT INTO servers (id, name, ip, os, credentials, status)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [
        server.id,
        server.name,
        server.ip,
        server.os,
        server.credentials,
        server.status
      ]);
    } catch (error) {
      console.error('خطأ في إضافة السيرفر:', error);
      throw error;
    }
  }

  /**
   * تحديث سيرفر
   */
  async updateServer(id: string, updates: Partial<ServerRecord>): Promise<void> {
    if (!this.db) {
      await this.connect();
    }

    const fields = Object.keys(updates).filter(key => key !== 'id').map(key => `${key} = ?`);
    const values = Object.values(updates).filter((_, index) => Object.keys(updates)[index] !== 'id');

    if (fields.length === 0) return;

    try {
      await this.db!.run(`
        UPDATE servers 
        SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [...values, id]);
    } catch (error) {
      console.error('خطأ في تحديث السيرفر:', error);
      throw error;
    }
  }

  /**
   * حذف سيرفر
   */
  async deleteServer(id: string): Promise<void> {
    if (!this.db) {
      await this.connect();
    }

    try {
      await this.db!.run('DELETE FROM servers WHERE id = ?', [id]);
    } catch (error) {
      console.error('خطأ في حذف السيرفر:', error);
      throw error;
    }
  }

  /**
   * الحصول على جميع السيرفرات
   */
  async getServers(): Promise<ServerRecord[]> {
    if (!this.db) {
      await this.connect();
    }

    try {
      const servers = await this.db!.all('SELECT * FROM servers ORDER BY created_at DESC');
      return servers;
    } catch (error) {
      console.error('خطأ في جلب السيرفرات:', error);
      throw error;
    }
  }

  /**
   * الحصول على سيرفر محدد
   */
  async getServer(id: string): Promise<ServerRecord | undefined> {
    if (!this.db) {
      await this.connect();
    }

    try {
      const server = await this.db!.get('SELECT * FROM servers WHERE id = ?', [id]);
      return server;
    } catch (error) {
      console.error('خطأ في جلب السيرفر:', error);
      throw error;
    }
  }

  /**
   * إضافة تنبيه جديد
   */
  async addAlert(alert: Omit<AlertRecord, 'created_at' | 'updated_at'>): Promise<void> {
    if (!this.db) {
      await this.connect();
    }

    try {
      await this.db!.run(`
        INSERT INTO alerts (id, server_id, type, title, message, severity, category, threshold_data, acknowledged, resolved)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        alert.id,
        alert.server_id,
        alert.type,
        alert.title,
        alert.message,
        alert.severity,
        alert.category,
        alert.threshold_data,
        alert.acknowledged,
        alert.resolved
      ]);
    } catch (error) {
      console.error('خطأ في إضافة التنبيه:', error);
      throw error;
    }
  }

  /**
   * تحديث تنبيه
   */
  async updateAlert(id: string, updates: Partial<AlertRecord>): Promise<void> {
    if (!this.db) {
      await this.connect();
    }

    const fields = Object.keys(updates).filter(key => key !== 'id').map(key => `${key} = ?`);
    const values = Object.values(updates).filter((_, index) => Object.keys(updates)[index] !== 'id');

    if (fields.length === 0) return;

    try {
      await this.db!.run(`
        UPDATE alerts 
        SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [...values, id]);
    } catch (error) {
      console.error('خطأ في تحديث التنبيه:', error);
      throw error;
    }
  }

  /**
   * الحصول على التنبيهات
   */
  async getAlerts(limit?: number, offset?: number): Promise<AlertRecord[]> {
    if (!this.db) {
      await this.connect();
    }

    try {
      let query = 'SELECT * FROM alerts ORDER BY created_at DESC';
      const params: unknown[] = [];

      if (limit) {
        query += ' LIMIT ?';
        params.push(limit);

        if (offset) {
          query += ' OFFSET ?';
          params.push(offset);
        }
      }

      const alerts = await this.db!.all(query, params);
      return alerts;
    } catch (error) {
      console.error('خطأ في جلب التنبيهات:', error);
      throw error;
    }
  }

  /**
   * الحصول على التنبيهات النشطة
   */
  async getActiveAlerts(): Promise<AlertRecord[]> {
    if (!this.db) {
      await this.connect();
    }

    try {
      const alerts = await this.db!.all(
        'SELECT * FROM alerts WHERE resolved = 0 ORDER BY created_at DESC'
      );
      return alerts;
    } catch (error) {
      console.error('خطأ في جلب التنبيهات النشطة:', error);
      throw error;
    }
  }

  /**
   * إغلاق الاتصال
   */
  async close(): Promise<void> {
    if (this.db) {
      await this.db.close();
      this.db = null;
      console.log('تم إغلاق الاتصال بقاعدة البيانات');
    }
  }
}

// إنشاء instance مشترك
export const database = new DatabaseManager();
