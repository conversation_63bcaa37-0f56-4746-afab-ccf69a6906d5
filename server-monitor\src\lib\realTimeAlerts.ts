import { getSystemInfo } from './systemMonitor';
import { remoteMonitoring } from './remoteMonitoring';
// import { sendEmailAlert, sendWebhookAlert } from './notifications';
import { database } from './database';

export interface Alert {
  id: string;
  type: 'warning' | 'error' | 'critical' | 'info';
  title: string;
  message: string;
  serverName: string;
  serverId: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  acknowledged: boolean;
  resolved: boolean;
  category: 'cpu' | 'memory' | 'disk' | 'network' | 'service' | 'system';
  threshold?: {
    current: number;
    limit: number;
    unit: string;
  };
}

export interface AlertRule {
  id: string;
  name: string;
  category: Alert['category'];
  condition: 'greater_than' | 'less_than' | 'equals' | 'not_equals';
  threshold: number;
  severity: Alert['severity'];
  enabled: boolean;
  cooldown: number; // بالدقائق
  lastTriggered?: Date;
}

/**
 * نظام التنبيهات الحقيقية
 */
export class RealTimeAlerts {
  private alerts: Map<string, Alert> = new Map();
  private alertRules: Map<string, AlertRule> = new Map();
  private monitoringInterval: NodeJS.Timeout | null = null;
  private isMonitoring = false;

  constructor() {
    this.initializeDefaultRules();
    this.initializeDatabase();
  }

  /**
   * تهيئة قاعدة البيانات
   */
  private async initializeDatabase(): Promise<void> {
    try {
      await database.connect();
    } catch (error) {
      console.error('خطأ في تهيئة قاعدة البيانات:', error);
    }
  }

  /**
   * تهيئة القواعد الافتراضية للتنبيهات
   */
  private initializeDefaultRules(): void {
    const defaultRules: AlertRule[] = [
      {
        id: 'cpu-high',
        name: 'استخدام عالي للمعالج',
        category: 'cpu',
        condition: 'greater_than',
        threshold: 80,
        severity: 'high',
        enabled: true,
        cooldown: 5
      },
      {
        id: 'cpu-critical',
        name: 'استخدام حرج للمعالج',
        category: 'cpu',
        condition: 'greater_than',
        threshold: 95,
        severity: 'critical',
        enabled: true,
        cooldown: 2
      },
      {
        id: 'memory-high',
        name: 'استخدام عالي للذاكرة',
        category: 'memory',
        condition: 'greater_than',
        threshold: 85,
        severity: 'high',
        enabled: true,
        cooldown: 5
      },
      {
        id: 'memory-critical',
        name: 'استخدام حرج للذاكرة',
        category: 'memory',
        condition: 'greater_than',
        threshold: 95,
        severity: 'critical',
        enabled: true,
        cooldown: 2
      },
      {
        id: 'disk-high',
        name: 'مساحة قرص منخفضة',
        category: 'disk',
        condition: 'greater_than',
        threshold: 85,
        severity: 'medium',
        enabled: true,
        cooldown: 10
      },
      {
        id: 'disk-critical',
        name: 'مساحة قرص حرجة',
        category: 'disk',
        condition: 'greater_than',
        threshold: 95,
        severity: 'critical',
        enabled: true,
        cooldown: 5
      }
    ];

    defaultRules.forEach(rule => {
      this.alertRules.set(rule.id, rule);
    });
  }

  /**
   * بدء مراقبة التنبيهات
   */
  startMonitoring(intervalMinutes: number = 1): void {
    if (this.isMonitoring) {
      console.log('نظام التنبيهات يعمل بالفعل');
      return;
    }

    this.isMonitoring = true;
    console.log(`بدء مراقبة التنبيهات كل ${intervalMinutes} دقيقة`);

    this.monitoringInterval = setInterval(async () => {
      await this.checkAllServers();
    }, intervalMinutes * 60 * 1000);

    // فحص فوري
    this.checkAllServers();
  }

  /**
   * إيقاف مراقبة التنبيهات
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    console.log('تم إيقاف مراقبة التنبيهات');
  }

  /**
   * فحص جميع السيرفرات
   */
  private async checkAllServers(): Promise<void> {
    try {
      // فحص السيرفر المحلي
      await this.checkLocalServer();

      // فحص السيرفرات البعيدة
      await this.checkRemoteServers();
    } catch (error) {
      console.error('خطأ في فحص السيرفرات:', error);
    }
  }

  /**
   * فحص السيرفر المحلي
   */
  private async checkLocalServer(): Promise<void> {
    try {
      const systemInfo = await getSystemInfo();

      await this.checkMetrics('local-server', 'السيرفر المحلي', {
        cpu: systemInfo.cpu,
        memory: systemInfo.memory,
        diskUsage: systemInfo.diskUsage,
        disks: systemInfo.disks
      });
    } catch (error) {
      console.error('خطأ في فحص السيرفر المحلي:', error);
    }
  }

  /**
   * فحص السيرفرات البعيدة
   */
  private async checkRemoteServers(): Promise<void> {
    try {
      const servers = remoteMonitoring.getServers();
      const systemInfos = await remoteMonitoring.monitorAllServers();

      for (const server of servers) {
        const systemInfo = systemInfos.get(server.id);
        if (systemInfo) {
          await this.checkMetrics(server.id, server.name, {
            cpu: systemInfo.cpu,
            memory: systemInfo.memory,
            diskUsage: systemInfo.diskUsage,
            disks: systemInfo.disks
          });
        } else {
          // السيرفر غير متاح
          await this.createAlert({
            serverId: server.id,
            serverName: server.name,
            type: 'error',
            category: 'system',
            severity: 'high',
            title: 'السيرفر غير متاح',
            message: `لا يمكن الوصول إلى السيرفر ${server.name} (${server.ip})`
          });
        }
      }
    } catch (error) {
      console.error('خطأ في فحص السيرفرات البعيدة:', error);
    }
  }

  /**
   * فحص المقاييس وإنشاء التنبيهات
   */
  private async checkMetrics(serverId: string, serverName: string, metrics: {
    cpu: number;
    memory: number;
    diskUsage: number;
    disks: Array<{ name: string; usage: number }>;
  }): Promise<void> {
    // فحص المعالج
    await this.checkThreshold(serverId, serverName, 'cpu', metrics.cpu, '%');

    // فحص الذاكرة
    await this.checkThreshold(serverId, serverName, 'memory', metrics.memory, '%');

    // فحص الأقراص
    await this.checkThreshold(serverId, serverName, 'disk', metrics.diskUsage, '%');

    // فحص الأقراص الفردية
    for (const disk of metrics.disks) {
      if (disk.badSectors && disk.badSectors > 0) {
        await this.createAlert({
          serverId,
          serverName,
          type: 'warning',
          category: 'disk',
          severity: disk.badSectors > 10 ? 'critical' : 'medium',
          title: 'Bad Sectors مكتشفة',
          message: `تم اكتشاف ${disk.badSectors} Bad Sectors في القرص ${disk.name}`,
          threshold: {
            current: disk.badSectors,
            limit: 0,
            unit: 'sectors'
          }
        });
      }

      if (disk.health === 'critical') {
        await this.createAlert({
          serverId,
          serverName,
          type: 'critical',
          category: 'disk',
          severity: 'critical',
          title: 'حالة قرص حرجة',
          message: `القرص ${disk.name} في حالة حرجة ويحتاج إلى استبدال فوري`
        });
      }
    }
  }

  /**
   * فحص الحدود وإنشاء التنبيهات
   */
  private async checkThreshold(
    serverId: string,
    serverName: string,
    category: Alert['category'],
    currentValue: number,
    unit: string
  ): Promise<void> {
    const relevantRules = Array.from(this.alertRules.values())
      .filter(rule => rule.category === category && rule.enabled);

    for (const rule of relevantRules) {
      const shouldTrigger = this.evaluateCondition(currentValue, rule.condition, rule.threshold);

      if (shouldTrigger && this.canTriggerRule(rule)) {
        await this.createAlert({
          serverId,
          serverName,
          type: rule.severity === 'critical' ? 'critical' : rule.severity === 'high' ? 'error' : 'warning',
          category: rule.category,
          severity: rule.severity,
          title: rule.name,
          message: `${rule.name}: ${currentValue}${unit} (الحد الأقصى: ${rule.threshold}${unit})`,
          threshold: {
            current: currentValue,
            limit: rule.threshold,
            unit
          }
        });

        rule.lastTriggered = new Date();
      }
    }
  }

  /**
   * تقييم الشرط
   */
  private evaluateCondition(value: number, condition: AlertRule['condition'], threshold: number): boolean {
    switch (condition) {
      case 'greater_than':
        return value > threshold;
      case 'less_than':
        return value < threshold;
      case 'equals':
        return value === threshold;
      case 'not_equals':
        return value !== threshold;
      default:
        return false;
    }
  }

  /**
   * التحقق من إمكانية تشغيل القاعدة (Cooldown)
   */
  private canTriggerRule(rule: AlertRule): boolean {
    if (!rule.lastTriggered) {
      return true;
    }

    const cooldownMs = rule.cooldown * 60 * 1000;
    const timeSinceLastTrigger = Date.now() - rule.lastTriggered.getTime();

    return timeSinceLastTrigger >= cooldownMs;
  }

  /**
   * إنشاء تنبيه جديد
   */
  private async createAlert(alertData: {
    serverId: string;
    serverName: string;
    type: Alert['type'];
    category: Alert['category'];
    severity: Alert['severity'];
    title: string;
    message: string;
    threshold?: Alert['threshold'];
  }): Promise<void> {
    const alertId = `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    const alert: Alert = {
      id: alertId,
      type: alertData.type,
      title: alertData.title,
      message: alertData.message,
      serverName: alertData.serverName,
      serverId: alertData.serverId,
      severity: alertData.severity,
      timestamp: new Date(),
      acknowledged: false,
      resolved: false,
      category: alertData.category,
      threshold: alertData.threshold
    };

    // التحقق من عدم وجود تنبيه مشابه حديث
    const existingAlert = this.findSimilarAlert(alert);
    if (existingAlert) {
      console.log(`تنبيه مشابه موجود بالفعل: ${alert.title}`);
      return;
    }

    this.alerts.set(alertId, alert);
    console.log(`تنبيه جديد: ${alert.title} - ${alert.serverName}`);

    // حفظ التنبيه في قاعدة البيانات
    try {
      await database.addAlert({
        id: alert.id,
        server_id: alert.serverId,
        type: alert.type,
        title: alert.title,
        message: alert.message,
        severity: alert.severity,
        category: alert.category,
        threshold_data: alert.threshold ? JSON.stringify(alert.threshold) : undefined,
        acknowledged: alert.acknowledged ? 1 : 0,
        resolved: alert.resolved ? 1 : 0
      });
    } catch (error) {
      console.error('خطأ في حفظ التنبيه في قاعدة البيانات:', error);
    }

    // إرسال الإشعارات
    await this.sendNotifications(alert);
  }

  /**
   * البحث عن تنبيه مشابه
   */
  private findSimilarAlert(newAlert: Alert): Alert | undefined {
    const recentAlerts = Array.from(this.alerts.values())
      .filter(alert =>
        alert.serverId === newAlert.serverId &&
        alert.category === newAlert.category &&
        alert.title === newAlert.title &&
        !alert.resolved &&
        (Date.now() - alert.timestamp.getTime()) < 10 * 60 * 1000 // آخر 10 دقائق
      );

    return recentAlerts[0];
  }

  /**
   * إرسال الإشعارات
   */
  private async sendNotifications(alert: Alert): Promise<void> {
    try {
      // إرسال إيميل للتنبيهات الحرجة والعالية
      if (alert.severity === 'critical' || alert.severity === 'high') {
        // await sendEmailAlert(alert);
        console.log(`تنبيه بريد إلكتروني: ${alert.title}`);
      }

      // إرسال webhook لجميع التنبيهات
      // await sendWebhookAlert(alert);
      console.log(`تنبيه webhook: ${alert.title}`);
    } catch (error) {
      console.error('خطأ في إرسال الإشعارات:', error);
    }
  }

  /**
   * الحصول على جميع التنبيهات
   */
  getAlerts(): Alert[] {
    return Array.from(this.alerts.values())
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * الحصول على التنبيهات النشطة
   */
  getActiveAlerts(): Alert[] {
    return this.getAlerts().filter(alert => !alert.resolved);
  }

  /**
   * تأكيد تنبيه
   */
  acknowledgeAlert(alertId: string): boolean {
    const alert = this.alerts.get(alertId);
    if (alert) {
      alert.acknowledged = true;
      return true;
    }
    return false;
  }

  /**
   * حل تنبيه
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.get(alertId);
    if (alert) {
      alert.resolved = true;
      return true;
    }
    return false;
  }

  /**
   * إضافة قاعدة تنبيه جديدة
   */
  addAlertRule(rule: AlertRule): void {
    this.alertRules.set(rule.id, rule);
  }

  /**
   * الحصول على قواعد التنبيهات
   */
  getAlertRules(): AlertRule[] {
    return Array.from(this.alertRules.values());
  }

  /**
   * تحديث قاعدة تنبيه
   */
  updateAlertRule(ruleId: string, updates: Partial<AlertRule>): boolean {
    const rule = this.alertRules.get(ruleId);
    if (rule) {
      Object.assign(rule, updates);
      return true;
    }
    return false;
  }

  /**
   * حذف قاعدة تنبيه
   */
  deleteAlertRule(ruleId: string): boolean {
    return this.alertRules.delete(ruleId);
  }
}

// إنشاء instance مشترك
export const realTimeAlerts = new RealTimeAlerts();
