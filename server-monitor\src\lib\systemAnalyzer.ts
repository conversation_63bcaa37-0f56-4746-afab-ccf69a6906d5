// مكتبة تحليل النظام المتقدم - أخطاء السيرفر والملفات المؤقتة والملفات الكبيرة
import { exec } from 'child_process';
import { promisify } from 'util';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

const execAsync = promisify(exec);

export interface SystemError {
  id: string;
  timestamp: Date;
  level: 'error' | 'warning' | 'critical';
  source: string;
  message: string;
  details?: string;
}

export interface TempFile {
  path: string;
  name: string;
  size: number;
  sizeFormatted: string;
  lastModified: Date;
  canDelete: boolean;
  isDuplicate?: boolean;
  duplicateCount?: number;
  duplicatePaths?: string[];
}

export interface LargeFile {
  path: string;
  name: string;
  size: number;
  sizeFormatted: string;
  lastModified: Date;
  directory: string;
}

export interface SystemAnalysis {
  errors: SystemError[];
  tempFiles: TempFile[];
  largeFiles: LargeFile[];
  totalTempSize: number;
  totalTempSizeFormatted: string;
  tempFileCount: number;
  largeFileCount: number;
}

export class SystemAnalyzer {
  private static instance: SystemAnalyzer;

  public static getInstance(): SystemAnalyzer {
    if (!SystemAnalyzer.instance) {
      SystemAnalyzer.instance = new SystemAnalyzer();
    }
    return SystemAnalyzer.instance;
  }

  /**
   * تحليل شامل للنظام
   */
  public async analyzeSystem(): Promise<SystemAnalysis> {
    const [errors, tempFiles, largeFiles] = await Promise.all([
      this.getSystemErrors(),
      this.getTempFiles(),
      this.getLargeFiles()
    ]);

    const totalTempSize = tempFiles.reduce((total, file) => total + file.size, 0);

    return {
      errors,
      tempFiles,
      largeFiles,
      totalTempSize,
      totalTempSizeFormatted: this.formatFileSize(totalTempSize),
      tempFileCount: tempFiles.length,
      largeFileCount: largeFiles.length
    };
  }

  /**
   * الحصول على أخطاء النظام
   */
  public async getSystemErrors(): Promise<SystemError[]> {
    const errors: SystemError[] = [];

    try {
      if (os.platform() === 'win32') {
        await this.getWindowsErrors(errors);
      } else {
        await this.getLinuxErrors(errors);
      }
    } catch (error) {
      console.error('خطأ في جمع أخطاء النظام:', error);
    }

    return errors.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * الحصول على أخطاء Windows
   */
  private async getWindowsErrors(errors: SystemError[]): Promise<void> {
    try {
      // أخطاء Application Log
      const appLogCommand = `Get-EventLog -LogName Application -EntryType Error -Newest 50 | ConvertTo-Json`;
      const { stdout: appLogs } = await execAsync(`powershell -Command "${appLogCommand}"`);

      if (appLogs.trim()) {
        const appEvents = JSON.parse(appLogs);
        const events = Array.isArray(appEvents) ? appEvents : [appEvents];

        events.forEach((event: any) => {
          errors.push({
            id: `app-${event.Index}`,
            timestamp: new Date(event.TimeGenerated),
            level: 'error',
            source: event.Source || 'Application',
            message: event.Message || 'رسالة غير متوفرة',
            details: `Event ID: ${event.EventID}`
          });
        });
      }

      // أخطاء System Log
      const sysLogCommand = `Get-EventLog -LogName System -EntryType Error -Newest 30 | ConvertTo-Json`;
      const { stdout: sysLogs } = await execAsync(`powershell -Command "${sysLogCommand}"`);

      if (sysLogs.trim()) {
        const sysEvents = JSON.parse(sysLogs);
        const events = Array.isArray(sysEvents) ? sysEvents : [sysEvents];

        events.forEach((event: any) => {
          errors.push({
            id: `sys-${event.Index}`,
            timestamp: new Date(event.TimeGenerated),
            level: 'critical',
            source: event.Source || 'System',
            message: event.Message || 'رسالة غير متوفرة',
            details: `Event ID: ${event.EventID}`
          });
        });
      }

    } catch (error) {
      console.error('خطأ في جمع أخطاء Windows:', error);
    }
  }

  /**
   * الحصول على أخطاء Linux
   */
  private async getLinuxErrors(errors: SystemError[]): Promise<void> {
    try {
      // فحص syslog
      const { stdout: syslog } = await execAsync(`tail -n 100 /var/log/syslog | grep -i error || echo ""`);
      if (syslog) {
        const lines = syslog.split('\n').filter(line => line.trim());
        lines.forEach((line, index) => {
          errors.push({
            id: `syslog-${index}`,
            timestamp: new Date(),
            level: 'error',
            source: 'syslog',
            message: line.trim(),
          });
        });
      }

      // فحص kern.log
      const { stdout: kernlog } = await execAsync(`tail -n 50 /var/log/kern.log | grep -i error || echo ""`);
      if (kernlog) {
        const lines = kernlog.split('\n').filter(line => line.trim());
        lines.forEach((line, index) => {
          errors.push({
            id: `kern-${index}`,
            timestamp: new Date(),
            level: 'critical',
            source: 'kernel',
            message: line.trim(),
          });
        });
      }

    } catch (error) {
      console.error('خطأ في جمع أخطاء Linux:', error);
    }
  }

  /**
   * الحصول على الملفات المؤقتة
   */
  public async getTempFiles(): Promise<TempFile[]> {
    const tempFiles: TempFile[] = [];

    try {
      if (os.platform() === 'win32') {
        await this.getWindowsTempFiles(tempFiles);
      } else {
        await this.getLinuxTempFiles(tempFiles);
      }

      // فلترة الملفات الآمنة للحذف فقط
      const safeFiles = tempFiles.filter(file => this.isSafeToDelete(file));

      // إزالة الملفات المكررة
      const uniqueFiles = this.removeDuplicateFiles(safeFiles);

      return uniqueFiles.sort((a, b) => b.size - a.size);
    } catch (error) {
      console.error('خطأ في جمع الملفات المؤقتة:', error);
      return [];
    }
  }

  /**
   * الحصول على الملفات المؤقتة في Windows
   */
  private async getWindowsTempFiles(tempFiles: TempFile[]): Promise<void> {
    // مجلدات الملفات المؤقتة الآمنة فقط
    const safeTempDirs = [
      // مجلدات المستخدم الآمنة
      process.env.TEMP,
      process.env.TMP,

      // مجلدات إضافية آمنة
      'C:\\Temp',
      'C:\\tmp',

      // مجلدات المتصفحات (إذا كانت موجودة)
      path.join(process.env.LOCALAPPDATA || '', 'Temp'),

      // مجلدات التحديثات المؤقتة
      'C:\\Windows\\SoftwareDistribution\\Download',
      'C:\\Windows\\Temp\\MpCmdRun'
    ].filter(Boolean); // إزالة القيم الفارغة

    for (const tempDir of safeTempDirs) {
      try {
        if (tempDir && fs.existsSync(tempDir)) {
          await this.scanDirectory(tempDir, tempFiles, true);
        }
      } catch (error) {
        console.error(`خطأ في فحص مجلد ${tempDir}:`, error);
      }
    }
  }

  /**
   * الحصول على الملفات المؤقتة في Linux
   */
  private async getLinuxTempFiles(tempFiles: TempFile[]): Promise<void> {
    const tempDirs = ['/tmp', '/var/tmp', '/dev/shm'];

    for (const tempDir of tempDirs) {
      try {
        if (fs.existsSync(tempDir)) {
          await this.scanDirectory(tempDir, tempFiles, true);
        }
      } catch (error) {
        console.error(`خطأ في فحص مجلد ${tempDir}:`, error);
      }
    }
  }

  /**
   * الحصول على الملفات الكبيرة
   */
  public async getLargeFiles(minSizeMB: number = 100): Promise<LargeFile[]> {
    const largeFiles: LargeFile[] = [];
    const minSize = minSizeMB * 1024 * 1024; // تحويل إلى بايت

    try {
      if (os.platform() === 'win32') {
        await this.getWindowsLargeFiles(largeFiles, minSize);
      } else {
        await this.getLinuxLargeFiles(largeFiles, minSize);
      }
    } catch (error) {
      console.error('خطأ في جمع الملفات الكبيرة:', error);
    }

    return largeFiles.sort((a, b) => b.size - a.size);
  }

  /**
   * الحصول على الملفات الكبيرة في Windows
   */
  private async getWindowsLargeFiles(largeFiles: LargeFile[], minSize: number): Promise<void> {
    const drives = ['C:', 'D:', 'E:', 'F:'];

    for (const drive of drives) {
      try {
        if (fs.existsSync(drive)) {
          await this.scanDirectoryForLargeFiles(drive, largeFiles, minSize, 2); // عمق 2 مستويات
        }
      } catch (error) {
        console.error(`خطأ في فحص القرص ${drive}:`, error);
      }
    }
  }

  /**
   * الحصول على الملفات الكبيرة في Linux
   */
  private async getLinuxLargeFiles(largeFiles: LargeFile[], minSize: number): Promise<void> {
    const searchDirs = ['/', '/home', '/var', '/opt'];

    for (const searchDir of searchDirs) {
      try {
        if (fs.existsSync(searchDir)) {
          await this.scanDirectoryForLargeFiles(searchDir, largeFiles, minSize, 3); // عمق 3 مستويات
        }
      } catch (error) {
        console.error(`خطأ في فحص مجلد ${searchDir}:`, error);
      }
    }
  }

  /**
   * فحص مجلد للملفات
   */
  private async scanDirectory(dirPath: string, files: TempFile[], isTempFile: boolean, maxDepth: number = 2, currentDepth: number = 0): Promise<void> {
    if (currentDepth >= maxDepth) return;

    try {
      const items = fs.readdirSync(dirPath);

      for (const item of items) {
        const fullPath = path.join(dirPath, item);

        try {
          const stats = fs.statSync(fullPath);

          if (stats.isFile() && isTempFile) {
            files.push({
              path: fullPath,
              name: item,
              size: stats.size,
              sizeFormatted: this.formatFileSize(stats.size),
              lastModified: stats.mtime,
              canDelete: this.canDeleteTempFile(fullPath, stats)
            });
          } else if (stats.isDirectory() && currentDepth < maxDepth - 1) {
            await this.scanDirectory(fullPath, files, isTempFile, maxDepth, currentDepth + 1);
          }
        } catch (error) {
          // تجاهل الملفات التي لا يمكن الوصول إليها
        }
      }
    } catch (error) {
      console.error(`خطأ في قراءة مجلد ${dirPath}:`, error);
    }
  }

  /**
   * فحص مجلد للملفات الكبيرة
   */
  private async scanDirectoryForLargeFiles(dirPath: string, files: LargeFile[], minSize: number, maxDepth: number, currentDepth: number = 0): Promise<void> {
    if (currentDepth >= maxDepth) return;

    try {
      const items = fs.readdirSync(dirPath);

      for (const item of items) {
        const fullPath = path.join(dirPath, item);

        try {
          const stats = fs.statSync(fullPath);

          if (stats.isFile() && stats.size >= minSize) {
            files.push({
              path: fullPath,
              name: item,
              size: stats.size,
              sizeFormatted: this.formatFileSize(stats.size),
              lastModified: stats.mtime,
              directory: path.dirname(fullPath)
            });
          } else if (stats.isDirectory() && currentDepth < maxDepth - 1) {
            await this.scanDirectoryForLargeFiles(fullPath, files, minSize, maxDepth, currentDepth + 1);
          }
        } catch (error) {
          // تجاهل الملفات التي لا يمكن الوصول إليها
        }
      }
    } catch (error) {
      console.error(`خطأ في قراءة مجلد ${dirPath}:`, error);
    }
  }

  /**
   * تحديد إمكانية حذف الملف المؤقت
   */
  private canDeleteTempFile(filePath: string, stats: fs.Stats): boolean {
    try {
      // فحص إذا كان الملف قديم (أكثر من يوم)
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      if (stats.mtime < oneDayAgo) {
        return true;
      }

      // فحص امتدادات الملفات المؤقتة الشائعة
      const tempExtensions = ['.tmp', '.temp', '.log', '.bak', '.cache'];
      const ext = path.extname(filePath).toLowerCase();

      return tempExtensions.includes(ext);
    } catch (error) {
      return false;
    }
  }

  /**
   * تنسيق حجم الملف
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * حذف الملفات المؤقتة
   */
  public async deleteTempFiles(filePaths: string[]): Promise<{ success: string[], failed: string[] }> {
    const success: string[] = [];
    const failed: string[] = [];

    if (!filePaths || filePaths.length === 0) {
      return { success, failed };
    }

    console.log(`بدء حذف ${filePaths.length} ملف...`);

    try {
      // الحصول على قائمة الملفات الحالية لمعرفة الملفات المكررة
      const currentFiles = await this.getTempFiles();
      const duplicateMap = new Map<string, string[]>();

      // بناء خريطة الملفات المكررة
      for (const file of currentFiles) {
        if (file.isDuplicate && file.duplicatePaths) {
          duplicateMap.set(file.path, file.duplicatePaths);
        }
      }

      for (const filePath of filePaths) {
        if (!filePath || typeof filePath !== 'string') {
          failed.push(filePath || 'مسار غير صحيح');
          continue;
        }

        try {
          // التحقق من وجود الملف أولاً
          let fileExists = false;
          try {
            fileExists = fs.existsSync(filePath);
          } catch (checkError) {
            console.error(`خطأ في فحص وجود الملف ${filePath}:`, checkError);
            failed.push(filePath);
            continue;
          }

          // حذف الملف الأساسي
          if (fileExists) {
            try {
              // التحقق من صلاحيات الكتابة
              fs.accessSync(filePath, fs.constants.W_OK);
              fs.unlinkSync(filePath);
              success.push(filePath);
              console.log(`تم حذف الملف: ${filePath}`);
            } catch (deleteError) {
              console.error(`فشل في حذف الملف ${filePath}:`, deleteError);
              failed.push(filePath);
              continue;
            }
          } else {
            // الملف غير موجود، قد يكون محذوف مسبقاً
            console.log(`الملف غير موجود: ${filePath}`);
            success.push(filePath); // نعتبره نجاح لأن الهدف تحقق
          }

          // حذف جميع النسخ المكررة إذا كانت موجودة
          if (duplicateMap.has(filePath)) {
            const duplicatePaths = duplicateMap.get(filePath)!;
            console.log(`حذف ${duplicatePaths.length} نسخة مكررة للملف ${filePath}`);

            for (const duplicatePath of duplicatePaths) {
              if (duplicatePath !== filePath) {
                try {
                  if (fs.existsSync(duplicatePath)) {
                    // التحقق من صلاحيات الكتابة
                    fs.accessSync(duplicatePath, fs.constants.W_OK);
                    fs.unlinkSync(duplicatePath);
                    success.push(duplicatePath);
                    console.log(`تم حذف النسخة المكررة: ${duplicatePath}`);
                  } else {
                    // النسخة المكررة غير موجودة
                    success.push(duplicatePath);
                  }
                } catch (duplicateError) {
                  failed.push(duplicatePath);
                  console.error(`فشل في حذف النسخة المكررة ${duplicatePath}:`, duplicateError);
                }
              }
            }
          }
        } catch (error) {
          failed.push(filePath);
          console.error(`خطأ عام في حذف الملف ${filePath}:`, error);
        }
      }
    } catch (error) {
      console.error('خطأ في عملية الحذف:', error);
      // في حالة خطأ عام، نضع جميع الملفات في قائمة الفشل
      filePaths.forEach(path => {
        if (!success.includes(path) && !failed.includes(path)) {
          failed.push(path);
        }
      });
    }

    console.log(`انتهى الحذف: ${success.length} نجح، ${failed.length} فشل`);
    return { success, failed };
  }

  /**
   * التحقق من أن الملف آمن للحذف
   */
  private isSafeToDelete(file: TempFile): boolean {
    const fileName = file.name.toLowerCase();
    const filePath = file.path.toLowerCase();

    // الملفات والمجلدات الحساسة التي يجب تجنب حذفها
    const sensitivePatterns = [
      // قواعد البيانات
      /\.db$/i,
      /\.sqlite$/i,
      /\.mdb$/i,
      /\.accdb$/i,
      /\.sql$/i,
      /\.bak$/i,
      /\.backup$/i,

      // ملفات النظام الحساسة
      /system32/i,
      /windows/i,
      /program files/i,
      /programdata/i,
      /users.*appdata.*local.*microsoft/i,
      /users.*appdata.*roaming.*microsoft/i,

      // ملفات التطبيقات الحساسة
      /\.config$/i,
      /\.ini$/i,
      /\.reg$/i,
      /\.dll$/i,
      /\.exe$/i,
      /\.sys$/i,

      // ملفات قواعد البيانات المحددة
      /mysql/i,
      /postgresql/i,
      /mongodb/i,
      /redis/i,
      /elasticsearch/i,
      /oracle/i,
      /sqlserver/i,

      // ملفات التطبيقات المهمة
      /node_modules/i,
      /\.git/i,
      /\.svn/i,
      /vendor/i,
      /composer/i,
      /npm/i,

      // ملفات الأمان والشهادات
      /\.key$/i,
      /\.pem$/i,
      /\.crt$/i,
      /\.cert$/i,
      /\.p12$/i,
      /\.pfx$/i,

      // ملفات التشغيل الحساسة
      /\.pid$/i,
      /\.lock$/i,
      /\.running$/i,

      // مجلدات النظام المهمة
      /temp.*microsoft/i,
      /temp.*windows/i,
      /temp.*system/i,
      /temp.*service/i,
      /temp.*iis/i,
      /temp.*sql/i,
    ];

    // التحقق من الأنماط الحساسة
    for (const pattern of sensitivePatterns) {
      if (pattern.test(fileName) || pattern.test(filePath)) {
        return false;
      }
    }

    // الملفات الآمنة للحذف
    const safePatterns = [
      // ملفات مؤقتة عامة
      /\.tmp$/i,
      /\.temp$/i,
      /~\$.*$/i,
      /\.cache$/i,
      /\.old$/i,

      // ملفات المتصفحات
      /browser.*cache/i,
      /chrome.*cache/i,
      /firefox.*cache/i,
      /edge.*cache/i,

      // ملفات التحديث
      /update.*temp/i,
      /download.*temp/i,
      /install.*temp/i,

      // ملفات الوسائط المؤقتة
      /\.part$/i,
      /\.crdownload$/i,
      /\.download$/i,

      // ملفات النصوص المؤقتة
      /\.txt$/i,
      /\.log$/i,
      /\.out$/i,

      // ملفات الضغط المؤقتة
      /\.zip\.tmp$/i,
      /\.rar\.tmp$/i,
      /extract.*temp/i,
    ];

    // التحقق من الأنماط الآمنة
    for (const pattern of safePatterns) {
      if (pattern.test(fileName) || pattern.test(filePath)) {
        return true;
      }
    }

    // إذا كان الملف في مجلد temp عام وليس حساساً
    const generalTempPaths = [
      /\\temp\\/i,
      /\/tmp\//i,
      /\\tmp\\/i,
      /temp$/i,
      /tmp$/i,
    ];

    for (const pattern of generalTempPaths) {
      if (pattern.test(filePath)) {
        // تحقق إضافي للتأكد من عدم وجود كلمات حساسة
        const sensitiveWords = ['database', 'db', 'sql', 'config', 'system', 'microsoft', 'windows'];
        const hassensitiveWord = sensitiveWords.some(word =>
          fileName.includes(word) || filePath.includes(word)
        );

        if (!hassensitiveWord) {
          return true;
        }
      }
    }

    // افتراضياً، لا تحذف الملفات غير المؤكدة
    return false;
  }

  /**
   * إزالة الملفات المكررة بناءً على الاسم والحجم
   */
  private removeDuplicateFiles(files: TempFile[]): TempFile[] {
    const uniqueFiles = new Map<string, TempFile>();
    const duplicateGroups = new Map<string, TempFile[]>();

    for (const file of files) {
      // إنشاء مفتاح فريد بناءً على اسم الملف والحجم
      const key = `${file.name}_${file.size}`;

      if (!uniqueFiles.has(key)) {
        // أول ملف من هذا النوع
        uniqueFiles.set(key, {
          ...file,
          isDuplicate: false,
          duplicateCount: 1,
          duplicatePaths: [file.path]
        });
        duplicateGroups.set(key, [file]);
      } else {
        // ملف مكرر
        const existingFile = uniqueFiles.get(key)!;
        const duplicates = duplicateGroups.get(key)!;

        duplicates.push(file);

        // تحديث معلومات الملف الأساسي
        uniqueFiles.set(key, {
          ...existingFile,
          isDuplicate: true,
          duplicateCount: duplicates.length,
          duplicatePaths: duplicates.map(f => f.path),
          // اختيار أحدث ملف كممثل للمجموعة
          lastModified: duplicates.reduce((latest, current) =>
            current.lastModified > latest ? current.lastModified : latest,
            existingFile.lastModified
          ),
          // اختيار أقصر مسار كمسار أساسي
          path: duplicates.reduce((shortest, current) =>
            current.path.length < shortest.length ? current.path : shortest,
            existingFile.path
          )
        });
      }
    }

    return Array.from(uniqueFiles.values());
  }
}

// تصدير المثيل الوحيد
export const systemAnalyzer = SystemAnalyzer.getInstance();
