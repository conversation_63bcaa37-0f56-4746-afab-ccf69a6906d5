# دليل النشر والصيانة - نظام مراقبة السيرفرات

## 🚀 خيارات النشر

### 1. النشر على Vercel (الأسهل)

#### الخطوات:
1. **إنشاء حساب على Vercel**:
   - اذهب إلى [vercel.com](https://vercel.com)
   - سجل دخول باستخدام GitHub

2. **ربط المشروع**:
```bash
npm install -g vercel
vercel login
vercel --prod
```

3. **إعداد متغيرات البيئة**:
```bash
# في لوحة تحكم Vercel
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
WEBHOOK_URL=https://your-webhook.com
```

4. **النشر**:
```bash
vercel --prod
```

### 2. الن<PERSON>ر على VPS/خادم مخصص

#### متطلبات الخادم:
- Ubuntu 20.04+ أو CentOS 8+
- Node.js 18+
- nginx
- PM2
- SSL Certificate

#### خطوات التثبيت:

1. **تحديث النظام**:
```bash
sudo apt update && sudo apt upgrade -y
```

2. **تثبيت Node.js**:
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

3. **تثبيت PM2**:
```bash
sudo npm install -g pm2
```

4. **تثبيت nginx**:
```bash
sudo apt install nginx -y
sudo systemctl start nginx
sudo systemctl enable nginx
```

5. **رفع المشروع**:
```bash
git clone https://github.com/your-repo/server-monitor.git
cd server-monitor
npm install
npm run build
```

6. **إعداد PM2**:
```bash
# إنشاء ملف ecosystem.config.js
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'server-monitor',
    script: 'npm',
    args: 'start',
    cwd: '/path/to/server-monitor',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}
EOF

pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

7. **إعداد nginx**:
```nginx
# /etc/nginx/sites-available/server-monitor
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
sudo ln -s /etc/nginx/sites-available/server-monitor /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

8. **إعداد SSL مع Let's Encrypt**:
```bash
sudo apt install certbot python3-certbot-nginx -y
sudo certbot --nginx -d your-domain.com
```

### 3. النشر باستخدام Docker

#### إنشاء Dockerfile:
```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

# نسخ ملفات package
COPY package*.json ./
RUN npm ci --only=production

# نسخ الكود المصدري
COPY . .

# بناء التطبيق
RUN npm run build

# إنشاء مستخدم غير root
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# تغيير ملكية الملفات
USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["npm", "start"]
```

#### إنشاء docker-compose.yml:
```yaml
version: '3.8'

services:
  server-monitor:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
    volumes:
      - ./data:/app/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - server-monitor
    restart: unless-stopped
```

#### تشغيل Docker:
```bash
docker-compose up -d
```

## 🔧 إعداد قاعدة البيانات للإنتاج

### استخدام PostgreSQL

1. **تثبيت PostgreSQL**:
```bash
sudo apt install postgresql postgresql-contrib -y
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

2. **إنشاء قاعدة البيانات**:
```sql
sudo -u postgres psql

CREATE DATABASE server_monitor;
CREATE USER monitor_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE server_monitor TO monitor_user;
\q
```

3. **تحديث إعدادات التطبيق**:
```bash
# .env.production
DATABASE_URL=postgresql://monitor_user:secure_password@localhost:5432/server_monitor
```

### استخدام Redis للتخزين المؤقت

1. **تثبيت Redis**:
```bash
sudo apt install redis-server -y
sudo systemctl start redis
sudo systemctl enable redis
```

2. **إعداد Redis في التطبيق**:
```typescript
// src/lib/redis.ts
import Redis from 'ioredis';

const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
});

export default redis;
```

## 📊 مراقبة الأداء في الإنتاج

### إعداد مراقبة النظام

1. **تثبيت htop و iotop**:
```bash
sudo apt install htop iotop -y
```

2. **مراقبة PM2**:
```bash
pm2 monit
pm2 logs server-monitor
```

3. **إعداد تنبيهات النظام**:
```bash
# إنشاء سكريبت مراقبة
cat > /opt/monitor.sh << 'EOF'
#!/bin/bash

# فحص استخدام CPU
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
    echo "تحذير: استخدام CPU مرتفع: $CPU_USAGE%" | mail -s "تنبيه خادم" <EMAIL>
fi

# فحص استخدام الذاكرة
MEM_USAGE=$(free | grep Mem | awk '{printf("%.2f", $3/$2 * 100.0)}')
if (( $(echo "$MEM_USAGE > 85" | bc -l) )); then
    echo "تحذير: استخدام الذاكرة مرتفع: $MEM_USAGE%" | mail -s "تنبيه خادم" <EMAIL>
fi

# فحص مساحة القرص
DISK_USAGE=$(df -h / | awk 'NR==2{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 90 ]; then
    echo "تحذير: مساحة القرص منخفضة: $DISK_USAGE%" | mail -s "تنبيه خادم" <EMAIL>
fi
EOF

chmod +x /opt/monitor.sh

# إضافة إلى crontab
echo "*/5 * * * * /opt/monitor.sh" | crontab -
```

### إعداد Grafana للمراقبة المتقدمة

1. **تثبيت Grafana**:
```bash
sudo apt-get install -y software-properties-common
sudo add-apt-repository "deb https://packages.grafana.com/oss/deb stable main"
wget -q -O - https://packages.grafana.com/gpg.key | sudo apt-key add -
sudo apt-get update
sudo apt-get install grafana
sudo systemctl start grafana-server
sudo systemctl enable grafana-server
```

2. **الوصول إلى Grafana**:
- URL: `http://your-server:3000`
- المستخدم: `admin`
- كلمة المرور: `admin`

## 🔒 الأمان في الإنتاج

### إعداد Firewall

```bash
# تفعيل UFW
sudo ufw enable

# السماح بـ SSH
sudo ufw allow ssh

# السماح بـ HTTP و HTTPS
sudo ufw allow 80
sudo ufw allow 443

# السماح بمنفذ التطبيق (إذا لزم الأمر)
sudo ufw allow 3000

# عرض الحالة
sudo ufw status
```

### إعداد Fail2Ban

```bash
# تثبيت Fail2Ban
sudo apt install fail2ban -y

# إنشاء إعدادات مخصصة
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local

# تعديل الإعدادات
sudo nano /etc/fail2ban/jail.local

# تشغيل الخدمة
sudo systemctl start fail2ban
sudo systemctl enable fail2ban
```

### تحديثات الأمان

```bash
# إنشاء سكريبت التحديث التلقائي
cat > /opt/auto-update.sh << 'EOF'
#!/bin/bash

# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تحديث Node.js packages
cd /path/to/server-monitor
npm audit fix
npm update

# إعادة تشغيل التطبيق
pm2 restart server-monitor

# تنظيف الملفات المؤقتة
sudo apt autoremove -y
sudo apt autoclean
EOF

chmod +x /opt/auto-update.sh

# جدولة التحديث الأسبوعي
echo "0 2 * * 0 /opt/auto-update.sh" | sudo crontab -
```

## 📋 النسخ الاحتياطي

### نسخ احتياطي للبيانات

```bash
# إنشاء سكريبت النسخ الاحتياطي
cat > /opt/backup.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/backup/server-monitor"
DATE=$(date +%Y%m%d_%H%M%S)

# إنشاء مجلد النسخ الاحتياطي
mkdir -p $BACKUP_DIR

# نسخ ملفات البيانات
tar -czf $BACKUP_DIR/data_$DATE.tar.gz /path/to/server-monitor/data

# نسخ قاعدة البيانات (إذا كانت PostgreSQL)
pg_dump server_monitor > $BACKUP_DIR/database_$DATE.sql

# حذف النسخ القديمة (أكثر من 30 يوم)
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete

echo "تم إنشاء النسخة الاحتياطية: $DATE"
EOF

chmod +x /opt/backup.sh

# جدولة النسخ الاحتياطي اليومي
echo "0 1 * * * /opt/backup.sh" | crontab -
```

### استعادة النسخ الاحتياطي

```bash
# استعادة ملفات البيانات
tar -xzf /backup/server-monitor/data_YYYYMMDD_HHMMSS.tar.gz -C /

# استعادة قاعدة البيانات
psql server_monitor < /backup/server-monitor/database_YYYYMMDD_HHMMSS.sql

# إعادة تشغيل التطبيق
pm2 restart server-monitor
```

## 🔄 التحديث والصيانة

### تحديث التطبيق

```bash
# إيقاف التطبيق
pm2 stop server-monitor

# جلب آخر التحديثات
git pull origin main

# تثبيت التبعيات الجديدة
npm install

# بناء التطبيق
npm run build

# تشغيل التطبيق
pm2 start server-monitor
```

### مراقبة السجلات

```bash
# عرض سجلات PM2
pm2 logs server-monitor

# عرض سجلات nginx
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# عرض سجلات النظام
sudo journalctl -u nginx -f
```

## 📞 الدعم والصيانة

### معلومات الاتصال للطوارئ
- **مطور النظام**: <EMAIL>
- **مدير النظام**: <EMAIL>
- **الدعم الفني**: <EMAIL>

### إجراءات الطوارئ
1. **في حالة توقف الخدمة**:
   ```bash
   pm2 restart server-monitor
   sudo systemctl restart nginx
   ```

2. **في حالة امتلاء القرص**:
   ```bash
   # تنظيف السجلات
   sudo journalctl --vacuum-time=7d
   
   # تنظيف ملفات npm
   npm cache clean --force
   
   # حذف ملفات مؤقتة
   sudo apt autoremove -y
   sudo apt autoclean
   ```

3. **في حالة مشاكل الأداء**:
   ```bash
   # فحص استخدام الموارد
   htop
   iotop
   
   # إعادة تشغيل الخدمات
   pm2 restart all
   sudo systemctl restart nginx
   ```

---

**ملاحظة**: تأكد من اختبار جميع الإجراءات في بيئة تطوير قبل تطبيقها في الإنتاج.
