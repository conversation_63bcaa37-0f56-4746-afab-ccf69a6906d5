import { NextResponse } from 'next/server';

export async function GET(
  request: Request,
  { params }: { params: { serverId: string } }
) {
  try {
    const { serverId } = params;
    
    // في البيئة الحقيقية، ستجلب البيانات من قاعدة البيانات
    // هنا سننشئ بيانات تجريبية للعرض التوضيحي
    const performanceData = generatePerformanceData(serverId);
    
    return NextResponse.json(performanceData);
  } catch (error) {
    console.error('خطأ في جلب بيانات الأداء:', error);
    return NextResponse.json(
      { error: 'فشل في جلب بيانات الأداء' },
      { status: 500 }
    );
  }
}

function generatePerformanceData(serverId: string) {
  const data = [];
  const now = new Date();
  
  // إنشاء بيانات لآخر 24 ساعة (نقطة كل ساعة)
  for (let i = 23; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);
    
    // محاكاة أنماط مختلفة للسيرفرات المختلفة
    let cpuBase = 30;
    let memoryBase = 40;
    let diskBase = 50;
    
    switch (serverId) {
      case 'server-1':
        cpuBase = 45;
        memoryBase = 55;
        diskBase = 60;
        break;
      case 'server-2':
        cpuBase = 25;
        memoryBase = 35;
        diskBase = 45;
        break;
      case 'server-3':
        cpuBase = 65;
        memoryBase = 70;
        diskBase = 75;
        break;
    }
    
    // إضافة تباين عشوائي
    const cpuVariation = Math.floor(Math.random() * 30) - 15;
    const memoryVariation = Math.floor(Math.random() * 20) - 10;
    const diskVariation = Math.floor(Math.random() * 15) - 7;
    
    data.push({
      timestamp: timestamp.toISOString(),
      cpu: Math.max(0, Math.min(100, cpuBase + cpuVariation)),
      memory: Math.max(0, Math.min(100, memoryBase + memoryVariation)),
      diskUsage: Math.max(0, Math.min(100, diskBase + diskVariation)),
    });
  }
  
  return data;
}
