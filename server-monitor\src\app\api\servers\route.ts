import { NextResponse } from 'next/server';
import { getSystemInfo } from '@/lib/systemMonitor';
import { remoteMonitoring } from '@/lib/remoteMonitoring';
import { serverDiscovery } from '@/lib/serverDiscovery';

export async function GET() {
  try {
    // الحصول على معلومات السيرفر المحلي
    const localSystemInfo = await getSystemInfo();

    // الحصول على معلومات السيرفرات البعيدة
    const remoteServers = remoteMonitoring.getServers();
    const remoteSystemInfos = await remoteMonitoring.monitorAllServers();

    // إنشاء قائمة السيرفرات
    const servers = [
      // السيرفر المحلي
      {
        id: 'local-server',
        name: 'السيرفر المحلي (هذا الجهاز)',
        ip: 'localhost',
        status: localSystemInfo.cpu > 80 ? 'warning' : 'online',
        cpu: localSystemInfo.cpu,
        memory: localSystemInfo.memory,
        diskUsage: localSystemInfo.diskUsage,
        disks: localSystemInfo.disks,
        lastUpdate: new Date().toISOString(),
        isLocal: true,
      },
      // السيرفرات البعيدة
      ...remoteServers.map(server => {
        const systemInfo = remoteSystemInfos.get(server.id);
        return {
          id: server.id,
          name: server.name,
          ip: server.ip,
          status: server.status,
          cpu: systemInfo?.cpu || 0,
          memory: systemInfo?.memory || 0,
          diskUsage: systemInfo?.diskUsage || 0,
          disks: systemInfo?.disks || [],
          lastUpdate: server.lastUpdate.toISOString(),
          isLocal: false,
          os: server.os,
        };
      })
    ];

    return NextResponse.json(servers);
  } catch (error) {
    console.error('خطأ في جلب بيانات السيرفرات:', error);
    return NextResponse.json(
      { error: 'فشل في جلب بيانات السيرفرات' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { serverId, action } = body;

    // معالجة الإجراءات مثل إعادة التشغيل، إيقاف الخدمات، إلخ
    switch (action) {
      case 'restart':
        // منطق إعادة تشغيل السيرفر
        console.log(`إعادة تشغيل السيرفر: ${serverId}`);
        break;
      case 'stop':
        // منطق إيقاف السيرفر
        console.log(`إيقاف السيرفر: ${serverId}`);
        break;
      case 'start':
        // منطق تشغيل السيرفر
        console.log(`تشغيل السيرفر: ${serverId}`);
        break;
      default:
        return NextResponse.json(
          { error: 'إجراء غير مدعوم' },
          { status: 400 }
        );
    }

    return NextResponse.json({ success: true, message: 'تم تنفيذ الإجراء بنجاح' });
  } catch (error) {
    console.error('خطأ في تنفيذ الإجراء:', error);
    return NextResponse.json(
      { error: 'فشل في تنفيذ الإجراء' },
      { status: 500 }
    );
  }
}
