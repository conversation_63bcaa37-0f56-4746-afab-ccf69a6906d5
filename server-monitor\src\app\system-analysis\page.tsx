'use client';

import React, { useState } from 'react';
import { AlertTriangle, FolderOpen, HardDrive, BarChart3 } from 'lucide-react';
import SystemErrorsPanel from '@/components/SystemErrorsPanel';
import TempFilesPanel from '@/components/TempFilesPanel';
import LargeFilesPanel from '@/components/LargeFilesPanel';

type TabType = 'errors' | 'temp-files' | 'large-files' | 'overview';

export default function SystemAnalysisPage() {
  const [activeTab, setActiveTab] = useState<TabType>('overview');

  const tabs = [
    {
      id: 'overview' as TabType,
      name: 'نظرة عامة',
      icon: BarChart3,
      color: 'text-blue-500'
    },
    {
      id: 'errors' as TabType,
      name: 'أخطاء النظام',
      icon: AlertTriangle,
      color: 'text-red-500'
    },
    {
      id: 'temp-files' as TabType,
      name: 'الملفات المؤقتة',
      icon: FolderOpen,
      color: 'text-orange-500'
    },
    {
      id: 'large-files' as TabType,
      name: 'الملفات الكبيرة',
      icon: HardDrive,
      color: 'text-purple-500'
    }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'errors':
        return <SystemErrorsPanel />;
      case 'temp-files':
        return <TempFilesPanel />;
      case 'large-files':
        return <LargeFilesPanel />;
      case 'overview':
      default:
        return <SystemOverview />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">تحليل النظام المتقدم</h1>
          <p className="text-gray-600">
            مراقبة أخطاء النظام والملفات المؤقتة والملفات الكبيرة لتحسين الأداء
          </p>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-md mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6" aria-label="Tabs">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? `border-blue-500 ${tab.color}`
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-5 h-5 ml-2" />
                    {tab.name}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="space-y-6">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
}

// مكون النظرة العامة
function SystemOverview() {
  const [overviewData, setOverviewData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  React.useEffect(() => {
    const fetchOverview = async () => {
      try {
        const response = await fetch('/api/system-analysis?type=full-analysis');
        const result = await response.json();
        
        if (result.success) {
          setOverviewData(result.data);
        }
      } catch (error) {
        console.error('خطأ في جلب نظرة عامة:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchOverview();
  }, []);

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="mr-3 text-gray-600">جاري تحميل البيانات...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="p-3 bg-red-100 rounded-full">
              <AlertTriangle className="w-6 h-6 text-red-500" />
            </div>
            <div className="mr-4">
              <h3 className="text-lg font-semibold text-gray-800">أخطاء النظام</h3>
              <p className="text-3xl font-bold text-red-600">
                {overviewData?.errors?.length || 0}
              </p>
              <p className="text-sm text-gray-500">خطأ مكتشف</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="p-3 bg-orange-100 rounded-full">
              <FolderOpen className="w-6 h-6 text-orange-500" />
            </div>
            <div className="mr-4">
              <h3 className="text-lg font-semibold text-gray-800">الملفات المؤقتة</h3>
              <p className="text-3xl font-bold text-orange-600">
                {overviewData?.tempFileCount || 0}
              </p>
              <p className="text-sm text-gray-500">
                {overviewData?.totalTempSizeFormatted || '0 B'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="p-3 bg-purple-100 rounded-full">
              <HardDrive className="w-6 h-6 text-purple-500" />
            </div>
            <div className="mr-4">
              <h3 className="text-lg font-semibold text-gray-800">الملفات الكبيرة</h3>
              <p className="text-3xl font-bold text-purple-600">
                {overviewData?.largeFileCount || 0}
              </p>
              <p className="text-sm text-gray-500">ملف كبير</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Errors */}
      {overviewData?.errors && overviewData.errors.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">أحدث الأخطاء</h3>
          <div className="space-y-3">
            {overviewData.errors.slice(0, 5).map((error: any, index: number) => (
              <div key={index} className="flex items-center p-3 bg-red-50 rounded-lg">
                <AlertTriangle className="w-5 h-5 text-red-500 ml-3" />
                <div className="flex-1">
                  <div className="font-medium text-gray-800">{error.source}</div>
                  <div className="text-sm text-gray-600 truncate">
                    {error.message.substring(0, 100)}...
                  </div>
                </div>
                <div className="text-xs text-gray-500">
                  {new Date(error.timestamp).toLocaleString('en-US')}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Top Large Files */}
      {overviewData?.largeFiles && overviewData.largeFiles.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">أكبر الملفات</h3>
          <div className="space-y-3">
            {overviewData.largeFiles.slice(0, 5).map((file: any, index: number) => (
              <div key={index} className="flex items-center p-3 bg-purple-50 rounded-lg">
                <HardDrive className="w-5 h-5 text-purple-500 ml-3" />
                <div className="flex-1">
                  <div className="font-medium text-gray-800">{file.name}</div>
                  <div className="text-sm text-gray-600 truncate">
                    {file.directory}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-purple-600">{file.sizeFormatted}</div>
                  <div className="text-xs text-gray-500">
                    {new Date(file.lastModified).toLocaleDateString('en-US')}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recommendations */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">التوصيات</h3>
        <div className="space-y-3">
          {overviewData?.tempFileCount > 0 && (
            <div className="flex items-center p-3 bg-yellow-50 rounded-lg">
              <FolderOpen className="w-5 h-5 text-yellow-500 ml-3" />
              <div>
                <div className="font-medium text-gray-800">تنظيف الملفات المؤقتة</div>
                <div className="text-sm text-gray-600">
                  يمكنك توفير {overviewData.totalTempSizeFormatted} من المساحة بحذف الملفات المؤقتة
                </div>
              </div>
            </div>
          )}
          
          {overviewData?.errors?.length > 0 && (
            <div className="flex items-center p-3 bg-red-50 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-red-500 ml-3" />
              <div>
                <div className="font-medium text-gray-800">مراجعة أخطاء النظام</div>
                <div className="text-sm text-gray-600">
                  هناك {overviewData.errors.length} خطأ يحتاج إلى مراجعة
                </div>
              </div>
            </div>
          )}

          {overviewData?.largeFileCount > 0 && (
            <div className="flex items-center p-3 bg-blue-50 rounded-lg">
              <HardDrive className="w-5 h-5 text-blue-500 ml-3" />
              <div>
                <div className="font-medium text-gray-800">مراجعة الملفات الكبيرة</div>
                <div className="text-sm text-gray-600">
                  تم العثور على {overviewData.largeFileCount} ملف كبير قد يحتاج إلى مراجعة
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
