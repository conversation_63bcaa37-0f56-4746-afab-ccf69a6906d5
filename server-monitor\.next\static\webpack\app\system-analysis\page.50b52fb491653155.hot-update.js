"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/system-analysis/page",{

/***/ "(app-pages-browser)/./src/components/TempFilesPanel.tsx":
/*!*******************************************!*\
  !*** ./src/components/TempFilesPanel.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TempFilesPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction TempFilesPanel() {\n    _s();\n    const [tempData, setTempData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFiles, setSelectedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [deleting, setDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('size');\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('desc');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [itemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(25); // تقليل عدد العناصر لتحسين الأداء\n    const [showOnlyDeletable, setShowOnlyDeletable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchTempFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TempFilesPanel.useCallback[fetchTempFiles]\": async ()=>{\n            try {\n                setRefreshing(true);\n                const response = await fetch('/api/system-analysis?type=temp-files');\n                const result = await response.json();\n                if (result.success) {\n                    // تحويل التواريخ من string إلى Date\n                    const filesWithDates = result.data.files.map({\n                        \"TempFilesPanel.useCallback[fetchTempFiles].filesWithDates\": (file)=>({\n                                ...file,\n                                lastModified: new Date(file.lastModified)\n                            })\n                    }[\"TempFilesPanel.useCallback[fetchTempFiles].filesWithDates\"]);\n                    setTempData({\n                        ...result.data,\n                        files: filesWithDates\n                    });\n                    setSelectedFiles(new Set()); // مسح التحديد عند التحديث\n                }\n            } catch (error) {\n                console.error('خطأ في جلب الملفات المؤقتة:', error);\n            } finally{\n                setLoading(false);\n                setRefreshing(false);\n            }\n        }\n    }[\"TempFilesPanel.useCallback[fetchTempFiles]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TempFilesPanel.useEffect\": ()=>{\n            fetchTempFiles();\n        }\n    }[\"TempFilesPanel.useEffect\"], [\n        fetchTempFiles\n    ]);\n    // تحسين التحديد باستخدام useCallback لمنع إعادة الرندر غير الضرورية\n    const toggleFileSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TempFilesPanel.useCallback[toggleFileSelection]\": (filePath)=>{\n            setSelectedFiles({\n                \"TempFilesPanel.useCallback[toggleFileSelection]\": (prev)=>{\n                    const newSelected = new Set(prev);\n                    if (newSelected.has(filePath)) {\n                        newSelected.delete(filePath);\n                    } else {\n                        newSelected.add(filePath);\n                    }\n                    return newSelected;\n                }\n            }[\"TempFilesPanel.useCallback[toggleFileSelection]\"]);\n        }\n    }[\"TempFilesPanel.useCallback[toggleFileSelection]\"], []);\n    // تحسين الفلترة والترتيب باستخدام useMemo\n    const filteredAndSortedFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TempFilesPanel.useMemo[filteredAndSortedFiles]\": ()=>{\n            if (!tempData) return [];\n            let filtered = tempData.files;\n            // فلترة الملفات القابلة للحذف فقط\n            if (showOnlyDeletable) {\n                filtered = filtered.filter({\n                    \"TempFilesPanel.useMemo[filteredAndSortedFiles]\": (file)=>file.canDelete\n                }[\"TempFilesPanel.useMemo[filteredAndSortedFiles]\"]);\n            }\n            // البحث النصي\n            if (searchTerm) {\n                const lowerSearchTerm = searchTerm.toLowerCase();\n                filtered = filtered.filter({\n                    \"TempFilesPanel.useMemo[filteredAndSortedFiles]\": (file)=>file.name.toLowerCase().includes(lowerSearchTerm) || file.path.toLowerCase().includes(lowerSearchTerm)\n                }[\"TempFilesPanel.useMemo[filteredAndSortedFiles]\"]);\n            }\n            // الترتيب\n            filtered.sort({\n                \"TempFilesPanel.useMemo[filteredAndSortedFiles]\": (a, b)=>{\n                    let comparison = 0;\n                    switch(sortBy){\n                        case 'name':\n                            comparison = a.name.localeCompare(b.name);\n                            break;\n                        case 'size':\n                            comparison = a.size - b.size;\n                            break;\n                        case 'date':\n                            comparison = a.lastModified.getTime() - b.lastModified.getTime();\n                            break;\n                    }\n                    return sortOrder === 'desc' ? -comparison : comparison;\n                }\n            }[\"TempFilesPanel.useMemo[filteredAndSortedFiles]\"]);\n            return filtered;\n        }\n    }[\"TempFilesPanel.useMemo[filteredAndSortedFiles]\"], [\n        tempData,\n        searchTerm,\n        sortBy,\n        sortOrder,\n        showOnlyDeletable\n    ]);\n    // حساب الصفحات\n    const totalPages = Math.ceil(filteredAndSortedFiles.length / itemsPerPage);\n    const paginatedFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TempFilesPanel.useMemo[paginatedFiles]\": ()=>{\n            const startIndex = (currentPage - 1) * itemsPerPage;\n            return filteredAndSortedFiles.slice(startIndex, startIndex + itemsPerPage);\n        }\n    }[\"TempFilesPanel.useMemo[paginatedFiles]\"], [\n        filteredAndSortedFiles,\n        currentPage,\n        itemsPerPage\n    ]);\n    const selectAllVisibleFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TempFilesPanel.useCallback[selectAllVisibleFiles]\": ()=>{\n            const visiblePaths = paginatedFiles.filter({\n                \"TempFilesPanel.useCallback[selectAllVisibleFiles].visiblePaths\": (file)=>file.canDelete\n            }[\"TempFilesPanel.useCallback[selectAllVisibleFiles].visiblePaths\"]).map({\n                \"TempFilesPanel.useCallback[selectAllVisibleFiles].visiblePaths\": (file)=>file.path\n            }[\"TempFilesPanel.useCallback[selectAllVisibleFiles].visiblePaths\"]);\n            setSelectedFiles({\n                \"TempFilesPanel.useCallback[selectAllVisibleFiles]\": (prev)=>{\n                    const newSelected = new Set(prev);\n                    const allVisibleSelected = visiblePaths.every({\n                        \"TempFilesPanel.useCallback[selectAllVisibleFiles].allVisibleSelected\": (path)=>newSelected.has(path)\n                    }[\"TempFilesPanel.useCallback[selectAllVisibleFiles].allVisibleSelected\"]);\n                    if (allVisibleSelected) {\n                        // إلغاء تحديد الملفات المرئية\n                        visiblePaths.forEach({\n                            \"TempFilesPanel.useCallback[selectAllVisibleFiles]\": (path)=>newSelected.delete(path)\n                        }[\"TempFilesPanel.useCallback[selectAllVisibleFiles]\"]);\n                    } else {\n                        // تحديد الملفات المرئية\n                        visiblePaths.forEach({\n                            \"TempFilesPanel.useCallback[selectAllVisibleFiles]\": (path)=>newSelected.add(path)\n                        }[\"TempFilesPanel.useCallback[selectAllVisibleFiles]\"]);\n                    }\n                    return newSelected;\n                }\n            }[\"TempFilesPanel.useCallback[selectAllVisibleFiles]\"]);\n        }\n    }[\"TempFilesPanel.useCallback[selectAllVisibleFiles]\"], [\n        paginatedFiles\n    ]);\n    const selectAllDeletableFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TempFilesPanel.useCallback[selectAllDeletableFiles]\": ()=>{\n            if (!tempData) return;\n            const deletableFiles = filteredAndSortedFiles.filter({\n                \"TempFilesPanel.useCallback[selectAllDeletableFiles].deletableFiles\": (file)=>file.canDelete\n            }[\"TempFilesPanel.useCallback[selectAllDeletableFiles].deletableFiles\"]).map({\n                \"TempFilesPanel.useCallback[selectAllDeletableFiles].deletableFiles\": (file)=>file.path\n            }[\"TempFilesPanel.useCallback[selectAllDeletableFiles].deletableFiles\"]);\n            setSelectedFiles(new Set(deletableFiles));\n        }\n    }[\"TempFilesPanel.useCallback[selectAllDeletableFiles]\"], [\n        filteredAndSortedFiles,\n        tempData\n    ]);\n    const clearSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TempFilesPanel.useCallback[clearSelection]\": ()=>{\n            setSelectedFiles(new Set());\n        }\n    }[\"TempFilesPanel.useCallback[clearSelection]\"], []);\n    const deleteSelectedFiles = async ()=>{\n        if (selectedFiles.size === 0) return;\n        try {\n            setDeleting(true);\n            const response = await fetch('/api/system-analysis', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'delete-temp-files',\n                    filePaths: Array.from(selectedFiles)\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                alert(\"تم حذف \".concat(result.data.success.length, \" ملف بنجاح\"));\n                if (result.data.failed.length > 0) {\n                    alert(\"فشل في حذف \".concat(result.data.failed.length, \" ملف\"));\n                }\n                // تحديث القائمة\n                await fetchTempFiles();\n                setSelectedFiles(new Set());\n            }\n        } catch (error) {\n            console.error('خطأ في حذف الملفات:', error);\n            alert('فشل في حذف الملفات');\n        } finally{\n            setDeleting(false);\n        }\n    };\n    const formatTimestamp = (timestamp)=>{\n        return timestamp.toLocaleString('en-US', {\n            year: 'numeric',\n            month: '2-digit',\n            day: '2-digit',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    const getSelectedSize = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TempFilesPanel.useMemo[getSelectedSize]\": ()=>{\n            if (!tempData) return 0;\n            return tempData.files.filter({\n                \"TempFilesPanel.useMemo[getSelectedSize]\": (file)=>selectedFiles.has(file.path)\n            }[\"TempFilesPanel.useMemo[getSelectedSize]\"]).reduce({\n                \"TempFilesPanel.useMemo[getSelectedSize]\": (total, file)=>{\n                    // إضافة حجم الملف مضروباً في عدد النسخ المكررة\n                    const multiplier = file.isDuplicate && file.duplicateCount ? file.duplicateCount : 1;\n                    return total + file.size * multiplier;\n                }\n            }[\"TempFilesPanel.useMemo[getSelectedSize]\"], 0);\n        }\n    }[\"TempFilesPanel.useMemo[getSelectedSize]\"], [\n        tempData,\n        selectedFiles\n    ]);\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 B';\n        const k = 1024;\n        const sizes = [\n            'B',\n            'KB',\n            'MB',\n            'GB',\n            'TB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"mr-3 text-gray-600\",\n                        children: \"جاري تحميل الملفات المؤقتة...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                lineNumber: 231,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n            lineNumber: 230,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-6 h-6 text-orange-500 ml-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-800\",\n                                        children: \"الملفات المؤقتة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this),\n                                    tempData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-3 px-2 py-1 bg-orange-100 text-orange-800 text-sm rounded-full\",\n                                        children: [\n                                            tempData.count,\n                                            \" ملف\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: fetchTempFiles,\n                                disabled: refreshing,\n                                className: \"flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-4 h-4 ml-2 \".concat(refreshing ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"تحديث\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this),\n                    tempData && tempData.files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"البحث في الملفات...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>{\n                                            setSearchTerm(e.target.value);\n                                            setCurrentPage(1); // العودة للصفحة الأولى عند البحث\n                                        },\n                                        className: \"w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: sortBy,\n                                        onChange: (e)=>setSortBy(e.target.value),\n                                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"size\",\n                                                children: \"ترتيب حسب الحجم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"name\",\n                                                children: \"ترتيب حسب الاسم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"date\",\n                                                children: \"ترتيب حسب التاريخ\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc'),\n                                        className: \"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                        title: sortOrder === 'asc' ? 'تصاعدي' : 'تنازلي',\n                                        children: sortOrder === 'asc' ? '↑' : '↓'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: showOnlyDeletable,\n                                            onChange: (e)=>{\n                                                setShowOnlyDeletable(e.target.checked);\n                                                setCurrentPage(1);\n                                            },\n                                            className: \"ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-700\",\n                                            children: \"الملفات القابلة للحذف فقط\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 border border-green-200 rounded-lg p-4 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5 text-green-600 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-green-800\",\n                                            children: \"ملفات آمنة للحذف\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-700 mt-1\",\n                                            children: \"يعرض النظام الملفات المؤقتة الآمنة للحذف فقط. تم استبعاد ملفات قواعد البيانات والملفات الحساسة تلقائياً لحماية النظام.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, this),\n                    tempData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-5 gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-orange-600\",\n                                        children: \"إجمالي الحجم\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-800\",\n                                        children: tempData.totalSizeFormatted\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-blue-600\",\n                                        children: \"الملفات المعروضة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-800\",\n                                        children: filteredAndSortedFiles.length\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-yellow-600\",\n                                        children: \"ملفات مكررة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-800\",\n                                        children: filteredAndSortedFiles.filter((f)=>f.isDuplicate && f.duplicateCount && f.duplicateCount > 1).length\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-purple-600\",\n                                        children: \"الملفات المحددة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-800\",\n                                        children: selectedFiles.size\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-green-600\",\n                                        children: \"حجم المحدد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-800\",\n                                        children: formatFileSize(getSelectedSize)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, this),\n                    tempData && filteredAndSortedFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: selectAllVisibleFiles,\n                                        className: \"flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"تحديد الصفحة الحالية\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: selectAllDeletableFiles,\n                                        className: \"flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"تحديد الكل (\",\n                                            filteredAndSortedFiles.filter((f)=>f.canDelete).length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: clearSelection,\n                                        className: \"flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إلغاء التحديد\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: deleteSelectedFiles,\n                                        disabled: selectedFiles.size === 0 || deleting,\n                                        className: \"flex items-center px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50\",\n                                        children: [\n                                            deleting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 19\n                                            }, this),\n                                            deleting ? 'جاري الحذف...' : \"حذف المحدد (\".concat(selectedFiles.size, \")\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, this),\n                            totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"صفحة \",\n                                    currentPage,\n                                    \" من \",\n                                    totalPages,\n                                    \" (\",\n                                    filteredAndSortedFiles.length,\n                                    \" ملف)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: !tempData || tempData.files.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-lg\",\n                            children: \"لا توجد ملفات مؤقتة\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: \"النظام نظيف من الملفات المؤقتة\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 11\n                }, this) : filteredAndSortedFiles.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-lg\",\n                            children: \"لا توجد نتائج\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: \"جرب تغيير معايير البحث أو الفلترة\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                    lineNumber: 418,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: paginatedFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow \".concat(selectedFiles.has(file.path) ? 'bg-blue-50 border-blue-300' : ''),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>toggleFileSelection(file.path),\n                                                        className: \"ml-3\",\n                                                        disabled: !file.canDelete,\n                                                        children: selectedFiles.has(file.path) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-5 h-5 text-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-5 h-5 \".concat(file.canDelete ? 'text-gray-400 hover:text-blue-500' : 'text-gray-300')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-5 h-5 text-gray-400 ml-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-gray-800\",\n                                                                        children: file.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                        lineNumber: 448,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    file.isDuplicate && file.duplicateCount && file.duplicateCount > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-2 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full\",\n                                                                        children: [\n                                                                            file.duplicateCount,\n                                                                            \" نسخ\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                        lineNumber: 450,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 truncate max-w-md\",\n                                                                children: file.path\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            file.isDuplicate && file.duplicatePaths && file.duplicatePaths.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-orange-600 mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                                                    className: \"cursor-pointer\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                                            className: \"hover:text-orange-800\",\n                                                                            children: [\n                                                                                \"عرض جميع المواقع (\",\n                                                                                file.duplicatePaths.length,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                            lineNumber: 459,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-1 pr-4 max-h-32 overflow-y-auto\",\n                                                                            children: file.duplicatePaths.map((path, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs text-gray-500 py-1 border-r-2 border-orange-200 pr-2\",\n                                                                                    children: path\n                                                                                }, index, false, {\n                                                                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                                    lineNumber: 462,\n                                                                                    columnNumber: 35\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                            lineNumber: 460,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: [\n                                                                    \"آخر تعديل: \",\n                                                                    formatTimestamp(file.lastModified)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-end\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: file.sizeFormatted\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    file.isDuplicate && file.duplicateCount && file.duplicateCount > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-orange-600\",\n                                                        children: [\n                                                            \"إجمالي: \",\n                                                            formatFileSize(file.size * file.duplicateCount)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    !file.canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4 text-yellow-500 mt-1\",\n                                                        title: \"لا يمكن حذف هذا الملف\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 19\n                                    }, this)\n                                }, file.path, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 425,\n                            columnNumber: 13\n                        }, this),\n                        totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mt-6 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage(Math.max(1, currentPage - 1)),\n                                    disabled: currentPage === 1,\n                                    className: \"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"السابق\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-1\",\n                                    children: Array.from({\n                                        length: Math.min(5, totalPages)\n                                    }, (_, i)=>{\n                                        let pageNum;\n                                        if (totalPages <= 5) {\n                                            pageNum = i + 1;\n                                        } else if (currentPage <= 3) {\n                                            pageNum = i + 1;\n                                        } else if (currentPage >= totalPages - 2) {\n                                            pageNum = totalPages - 4 + i;\n                                        } else {\n                                            pageNum = currentPage - 2 + i;\n                                        }\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentPage(pageNum),\n                                            className: \"px-3 py-2 rounded-lg \".concat(currentPage === pageNum ? 'bg-blue-500 text-white' : 'border border-gray-300 hover:bg-gray-50'),\n                                            children: pageNum\n                                        }, pageNum, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 23\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage(Math.min(totalPages, currentPage + 1)),\n                                    disabled: currentPage === totalPages,\n                                    className: \"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"التالي\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                lineNumber: 410,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n_s(TempFilesPanel, \"Cri2UDBcltCK6QvRS0pgJjCAcko=\");\n_c = TempFilesPanel;\nvar _c;\n$RefreshReg$(_c, \"TempFilesPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TempFilesPanel.tsx\n"));

/***/ })

});