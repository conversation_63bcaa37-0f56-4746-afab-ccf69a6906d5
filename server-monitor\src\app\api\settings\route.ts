import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

const SETTINGS_FILE = path.join(process.cwd(), 'data', 'settings.json');

// إعدادات افتراضية
const defaultSettings = {
  email: {
    enabled: false,
    smtpHost: 'smtp.gmail.com',
    smtpPort: 587,
    smtpUser: '',
    smtpPass: '',
    fromEmail: '',
    toEmails: '',
  },
  webhook: {
    enabled: false,
    url: '',
  },
  monitoring: {
    interval: 30,
    cpuThreshold: 80,
    memoryThreshold: 85,
    diskThreshold: 90,
  },
};

async function ensureDataDirectory() {
  const dataDir = path.join(process.cwd(), 'data');
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

async function loadSettings() {
  try {
    await ensureDataDirectory();
    const data = await fs.readFile(SETTINGS_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    // إذا لم يكن الملف موجوداً، أرجع الإعدادات الافتراضية
    return defaultSettings;
  }
}

async function saveSettings(settings: any) {
  try {
    await ensureDataDirectory();
    await fs.writeFile(SETTINGS_FILE, JSON.stringify(settings, null, 2));
    return true;
  } catch (error) {
    console.error('خطأ في حفظ الإعدادات:', error);
    return false;
  }
}

export async function GET() {
  try {
    const settings = await loadSettings();
    return NextResponse.json(settings);
  } catch (error) {
    console.error('خطأ في جلب الإعدادات:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الإعدادات' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const newSettings = await request.json();
    
    // التحقق من صحة البيانات
    if (!validateSettings(newSettings)) {
      return NextResponse.json(
        { error: 'بيانات الإعدادات غير صحيحة' },
        { status: 400 }
      );
    }

    const success = await saveSettings(newSettings);
    
    if (success) {
      return NextResponse.json({ 
        success: true, 
        message: 'تم حفظ الإعدادات بنجاح' 
      });
    } else {
      return NextResponse.json(
        { error: 'فشل في حفظ الإعدادات' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('خطأ في حفظ الإعدادات:', error);
    return NextResponse.json(
      { error: 'خطأ في الخادم' },
      { status: 500 }
    );
  }
}

function validateSettings(settings: any): boolean {
  try {
    // التحقق من وجود الحقول المطلوبة
    if (!settings.email || !settings.webhook || !settings.monitoring) {
      return false;
    }

    // التحقق من إعدادات الإيميل
    if (settings.email.enabled) {
      if (!settings.email.smtpHost || !settings.email.smtpUser || !settings.email.fromEmail) {
        return false;
      }
      
      if (typeof settings.email.smtpPort !== 'number' || 
          settings.email.smtpPort < 1 || 
          settings.email.smtpPort > 65535) {
        return false;
      }
    }

    // التحقق من إعدادات Webhook
    if (settings.webhook.enabled && !settings.webhook.url) {
      return false;
    }

    // التحقق من إعدادات المراقبة
    const monitoring = settings.monitoring;
    if (typeof monitoring.interval !== 'number' || 
        monitoring.interval < 10 || 
        monitoring.interval > 300) {
      return false;
    }

    if (typeof monitoring.cpuThreshold !== 'number' || 
        monitoring.cpuThreshold < 50 || 
        monitoring.cpuThreshold > 95) {
      return false;
    }

    if (typeof monitoring.memoryThreshold !== 'number' || 
        monitoring.memoryThreshold < 50 || 
        monitoring.memoryThreshold > 95) {
      return false;
    }

    if (typeof monitoring.diskThreshold !== 'number' || 
        monitoring.diskThreshold < 70 || 
        monitoring.diskThreshold > 98) {
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
}
