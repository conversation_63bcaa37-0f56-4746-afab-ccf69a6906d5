# دليل تحليل النظام المتقدم

## 📋 نظرة عامة

تحليل النظام المتقدم هو ميزة جديدة في نظام مراقبة السيرفرات تتيح لك مراقبة وتحليل جوانب متقدمة من النظام بما في ذلك:

- **أخطاء النظام**: رصد ومراقبة أخطاء النظام من مصادر مختلفة
- **الملفات المؤقتة**: اكتشاف وإدارة الملفات المؤقتة
- **الملفات الكبيرة**: البحث عن الملفات الكبيرة وتحليلها
- **نظرة عامة شاملة**: ملخص كامل مع توصيات التحسين

## 🚨 مراقبة أخطاء النظام

### Windows
يقوم النظام بمراقبة:
- **Application Log**: أخطاء التطبيقات
- **System Log**: أخطاء النظام الحرجة
- **Security Log**: أخطاء الأمان (إذا توفرت الصلاحيات)

### Linux/Unix
يقوم النظام بمراقبة:
- **Syslog**: `/var/log/syslog`
- **Kernel Log**: `/var/log/kern.log`
- **Application Logs**: `/var/log/messages`

### ميزات مراقبة الأخطاء
- **تصنيف الخطورة**: حرج، خطأ، تحذير
- **المصدر**: تحديد مصدر الخطأ
- **الوقت**: طابع زمني دقيق
- **التفاصيل**: معلومات إضافية عن الخطأ
- **التحديث التلقائي**: كل 5 دقائق

## 📁 إدارة الملفات المؤقتة

### المجلدات المراقبة

#### Windows
- `%TEMP%` - مجلد المستخدم المؤقت
- `%TMP%` - مجلد النظام المؤقت
- `C:\Windows\Temp` - مجلد Windows المؤقت

#### Linux/Unix
- `/tmp` - المجلد المؤقت الرئيسي
- `/var/tmp` - المجلد المؤقت للنظام
- `/dev/shm` - الذاكرة المشتركة

### ميزات إدارة الملفات المؤقتة
- **الحجم الإجمالي**: عرض المساحة المستخدمة
- **عدد الملفات**: إحصائيات مفصلة
- **الحذف الآمن**: حذف الملفات القديمة فقط
- **التحديد المتعدد**: تحديد ملفات متعددة للحذف
- **معاينة الحجم**: عرض الحجم المحرر بعد الحذف

### معايير الحذف الآمن
- الملفات الأقدم من 24 ساعة
- امتدادات الملفات المؤقتة: `.tmp`, `.temp`, `.log`, `.bak`, `.cache`
- تجنب الملفات المستخدمة حالياً

## 💾 اكتشاف الملفات الكبيرة

### معايير البحث
- **الحد الأدنى**: قابل للتخصيص (50MB, 100MB, 500MB, 1GB, 5GB)
- **العمق**: فحص حتى 3 مستويات في المجلدات
- **الاستثناءات**: تجنب ملفات النظام الحساسة

### المجلدات المفحوصة

#### Windows
- `C:`, `D:`, `E:`, `F:` - الأقراص الرئيسية
- تجنب مجلدات النظام الحساسة

#### Linux/Unix
- `/` - الجذر
- `/home` - مجلدات المستخدمين
- `/var` - بيانات متغيرة
- `/opt` - تطبيقات إضافية

### ميزات البحث والفلترة
- **البحث النصي**: في أسماء الملفات والمجلدات
- **الترتيب**: حسب الحجم، الاسم، أو التاريخ
- **الفلترة**: حسب الحد الأدنى للحجم
- **التفاصيل**: مسار كامل، حجم مفصل، تاريخ آخر تعديل

## 📊 النظرة العامة الشاملة

### الإحصائيات الرئيسية
- **عدد الأخطاء**: إجمالي أخطاء النظام
- **الملفات المؤقتة**: العدد والحجم الإجمالي
- **الملفات الكبيرة**: العدد والإحصائيات

### أحدث الأخطاء
- عرض آخر 5 أخطاء
- تفاصيل مختصرة لكل خطأ
- روابط سريعة للتفاصيل الكاملة

### أكبر الملفات
- عرض أكبر 5 ملفات
- الحجم والموقع
- تاريخ آخر تعديل

### التوصيات الذكية
- **تنظيف الملفات المؤقتة**: إذا كان الحجم كبير
- **مراجعة الأخطاء**: إذا كان هناك أخطاء حديثة
- **مراجعة الملفات الكبيرة**: إذا وُجدت ملفات كبيرة غير ضرورية

## 🔧 الاستخدام

### الوصول للميزة
1. افتح نظام مراقبة السيرفرات
2. انقر على "تحليل النظام" في الشريط العلوي
3. اختر التبويب المطلوب

### التبويبات المتاحة
- **نظرة عامة**: ملخص شامل
- **أخطاء النظام**: تفاصيل الأخطاء
- **الملفات المؤقتة**: إدارة الملفات المؤقتة
- **الملفات الكبيرة**: البحث والتحليل

### العمليات المتاحة
- **التحديث**: تحديث البيانات يدوياً
- **البحث**: البحث في النتائج
- **الفلترة**: تطبيق مرشحات مختلفة
- **الحذف**: حذف الملفات المؤقتة المحددة

## ⚠️ تحذيرات مهمة

### الصلاحيات
- قد تحتاج صلاحيات إدارية لقراءة بعض ملفات السجل
- حذف الملفات يتطلب صلاحيات الكتابة

### الأمان
- لا تحذف ملفات لا تعرف وظيفتها
- تأكد من عمل نسخة احتياطية قبل الحذف الجماعي
- راجع الأخطاء بعناية قبل اتخاذ إجراءات

### الأداء
- فحص الملفات الكبيرة قد يستغرق وقتاً
- تجنب تشغيل الفحص أثناء أوقات الذروة
- استخدم حدود حجم مناسبة لتجنب النتائج المفرطة

## 🔄 التحديث التلقائي

- **أخطاء النظام**: كل 5 دقائق
- **الملفات المؤقتة**: عند الطلب
- **الملفات الكبيرة**: عند الطلب
- **النظرة العامة**: عند تحميل الصفحة

## 📈 نصائح للاستخدام الأمثل

1. **راجع الأخطاء بانتظام**: لاكتشاف المشاكل مبكراً
2. **نظف الملفات المؤقتة أسبوعياً**: لتوفير المساحة
3. **راقب الملفات الكبيرة شهرياً**: لإدارة التخزين
4. **اتبع التوصيات**: للحصول على أفضل أداء
5. **استخدم الفلاتر**: لتركيز البحث على ما يهمك

## 🆘 استكشاف الأخطاء

### مشاكل شائعة
- **لا تظهر أخطاء**: تحقق من الصلاحيات
- **فحص بطيء**: قلل نطاق البحث
- **فشل الحذف**: تحقق من صلاحيات الكتابة
- **بيانات قديمة**: انقر على تحديث

### الحلول
- تشغيل التطبيق كمدير
- إغلاق التطبيقات التي تستخدم الملفات
- التحقق من مساحة القرص المتاحة
- إعادة تشغيل الخدمة

---

للمزيد من المساعدة، راجع الوثائق الرئيسية أو اتصل بفريق الدعم.
