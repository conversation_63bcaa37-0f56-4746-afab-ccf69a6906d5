'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowRight, FileText, Download, Calendar, Clock } from 'lucide-react';
import { generateServerReport } from '@/lib/reportGenerator';

export default function ReportsPage() {
  const router = useRouter();
  const [generating, setGenerating] = useState(false);
  const [reportPeriod, setReportPeriod] = useState({
    from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // آخر أسبوع
    to: new Date().toISOString().split('T')[0],
  });

  const generateReport = async (period?: 'daily' | 'weekly' | 'monthly' | 'custom') => {
    setGenerating(true);
    try {
      let reportData;
      
      switch (period) {
        case 'daily':
          reportData = {
            from: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            to: new Date().toISOString(),
          };
          break;
        case 'weekly':
          reportData = {
            from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            to: new Date().toISOString(),
          };
          break;
        case 'monthly':
          reportData = {
            from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            to: new Date().toISOString(),
          };
          break;
        case 'custom':
          reportData = {
            from: new Date(reportPeriod.from).toISOString(),
            to: new Date(reportPeriod.to).toISOString(),
          };
          break;
        default:
          reportData = undefined;
      }

      const blob = await generateServerReport(reportData);
      
      // تحميل الملف
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `server-report-${period || 'custom'}-${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('خطأ في إنتاج التقرير:', error);
      alert('فشل في إنتاج التقرير');
    } finally {
      setGenerating(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center">
              <button
                onClick={() => router.push('/')}
                className="ml-4 p-2 text-gray-400 hover:text-gray-600"
              >
                <ArrowRight className="w-6 h-6" />
              </button>
              <FileText className="w-8 h-8 text-blue-600 ml-3" />
              <h1 className="text-2xl font-bold text-gray-900">التقارير</h1>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Quick Reports */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">التقارير السريعة</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                onClick={() => generateReport('daily')}
                disabled={generating}
                className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors disabled:opacity-50"
              >
                <div className="text-center">
                  <Clock className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                  <h3 className="font-medium text-gray-900">تقرير يومي</h3>
                  <p className="text-sm text-gray-500">آخر 24 ساعة</p>
                </div>
              </button>

              <button
                onClick={() => generateReport('weekly')}
                disabled={generating}
                className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors disabled:opacity-50"
              >
                <div className="text-center">
                  <Calendar className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <h3 className="font-medium text-gray-900">تقرير أسبوعي</h3>
                  <p className="text-sm text-gray-500">آخر 7 أيام</p>
                </div>
              </button>

              <button
                onClick={() => generateReport('monthly')}
                disabled={generating}
                className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors disabled:opacity-50"
              >
                <div className="text-center">
                  <FileText className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                  <h3 className="font-medium text-gray-900">تقرير شهري</h3>
                  <p className="text-sm text-gray-500">آخر 30 يوم</p>
                </div>
              </button>
            </div>
          </div>

          {/* Custom Report */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">تقرير مخصص</h2>
            
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    من تاريخ
                  </label>
                  <input
                    type="date"
                    value={reportPeriod.from}
                    onChange={(e) => setReportPeriod(prev => ({ ...prev, from: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    إلى تاريخ
                  </label>
                  <input
                    type="date"
                    value={reportPeriod.to}
                    onChange={(e) => setReportPeriod(prev => ({ ...prev, to: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <button
                onClick={() => generateReport('custom')}
                disabled={generating}
                className="flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                <Download className="w-4 h-4 ml-2" />
                {generating ? 'جاري إنتاج التقرير...' : 'إنتاج التقرير'}
              </button>
            </div>
          </div>

          {/* Report Information */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">معلومات التقرير</h2>
            
            <div className="space-y-3 text-sm text-gray-600">
              <div className="flex items-start">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                <p>يتضمن التقرير ملخصاً تنفيذياً لحالة جميع السيرفرات</p>
              </div>
              
              <div className="flex items-start">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                <p>تفاصيل استخدام المعالج والذاكرة والتخزين لكل سيرفر</p>
              </div>
              
              <div className="flex items-start">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                <p>قائمة بجميع التنبيهات النشطة مع مستوى الخطورة</p>
              </div>
              
              <div className="flex items-start">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                <p>توصيات لتحسين الأداء وحل المشاكل</p>
              </div>
              
              <div className="flex items-start">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                <p>معلومات تفصيلية عن حالة الأقراص الصلبة</p>
              </div>
            </div>
          </div>

          {/* Scheduled Reports */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">التقارير المجدولة</h2>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Clock className="w-5 h-5 text-yellow-600" />
                </div>
                <div className="mr-3">
                  <h3 className="text-sm font-medium text-yellow-800">
                    قريباً
                  </h3>
                  <p className="text-sm text-yellow-700">
                    سيتم إضافة ميزة التقارير المجدولة التلقائية في التحديث القادم
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
