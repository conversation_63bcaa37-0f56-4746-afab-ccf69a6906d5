'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { AlertTriangle, RefreshCw, Clock, Server, AlertCircle, Loader2, Search, Filter } from 'lucide-react';

interface SystemError {
  id: string;
  timestamp: Date;
  level: 'error' | 'warning' | 'critical';
  source: string;
  message: string;
  details?: string;
}

export default function SystemErrorsPanel() {
  const [errors, setErrors] = useState<SystemError[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchErrors = async () => {
    try {
      setRefreshing(true);
      const response = await fetch('/api/system-analysis?type=errors');
      const result = await response.json();

      if (result.success) {
        // تحويل التواريخ من string إلى Date
        const errorsWithDates = result.data.map((error: any) => ({
          ...error,
          timestamp: new Date(error.timestamp)
        }));
        setErrors(errorsWithDates);
      }
    } catch (error) {
      console.error('خطأ في جلب أخطاء النظام:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchErrors();
    // تحديث كل 5 دقائق
    const interval = setInterval(fetchErrors, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const getErrorIcon = (level: string) => {
    switch (level) {
      case 'critical':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-orange-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  const getErrorBadgeColor = (level: string) => {
    switch (level) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'error':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const truncateMessage = (message: string, maxLength: number = 100) => {
    if (message.length <= maxLength) return message;
    return message.substring(0, maxLength) + '...';
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-center h-32">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-500" />
          <span className="mr-3 text-gray-600">جاري تحميل أخطاء النظام...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <AlertTriangle className="w-6 h-6 text-red-500 ml-3" />
            <h2 className="text-xl font-bold text-gray-800">أخطاء النظام</h2>
            <span className="mr-3 px-2 py-1 bg-red-100 text-red-800 text-sm rounded-full">
              {errors.length} خطأ
            </span>
          </div>
          <button
            onClick={fetchErrors}
            disabled={refreshing}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ml-2 ${refreshing ? 'animate-spin' : ''}`} />
            تحديث
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {errors.length === 0 ? (
          <div className="text-center py-8">
            <AlertCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 text-lg">لا توجد أخطاء في النظام</p>
            <p className="text-gray-400 text-sm">هذا أمر جيد! النظام يعمل بشكل طبيعي</p>
          </div>
        ) : (
          <div className="space-y-4">
            {errors.map((error) => (
              <div
                key={error.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start flex-1">
                    <div className="ml-3 mt-1">
                      {getErrorIcon(error.level)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getErrorBadgeColor(error.level)}`}>
                          {error.level === 'critical' ? 'حرج' :
                            error.level === 'error' ? 'خطأ' : 'تحذير'}
                        </span>
                        <div className="flex items-center mr-3 text-sm text-gray-500">
                          <Server className="w-4 h-4 ml-1" />
                          {error.source}
                        </div>
                        <div className="flex items-center mr-3 text-sm text-gray-500">
                          <Clock className="w-4 h-4 ml-1" />
                          {formatTimestamp(error.timestamp)}
                        </div>
                      </div>
                      <p className="text-gray-800 mb-2">
                        {truncateMessage(error.message)}
                      </p>
                      {error.details && (
                        <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                          {error.details}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      {errors.length > 0 && (
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>آخر تحديث: {new Date().toLocaleString('en-US')}</span>
            <span>إجمالي الأخطاء: {errors.length}</span>
          </div>
        </div>
      )}
    </div>
  );
}
