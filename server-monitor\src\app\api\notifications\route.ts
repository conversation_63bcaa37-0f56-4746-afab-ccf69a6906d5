import { NextResponse } from 'next/server';
import { notificationService, createAlert } from '@/lib/notifications';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { type, title, message, serverName, severity } = body;

    // التحقق من صحة البيانات
    if (!type || !title || !message || !serverName) {
      return NextResponse.json(
        { error: 'بيانات غير مكتملة' },
        { status: 400 }
      );
    }

    // إنشاء التنبيه
    const alert = createAlert(type, title, message, serverName, severity);

    // إرسال التنبيه
    const success = await notificationService.sendAlert(alert);

    if (success) {
      return NextResponse.json({ 
        success: true, 
        message: 'تم إرسال التنبيه بنجاح',
        alertId: alert.id 
      });
    } else {
      return NextResponse.json(
        { error: 'فشل في إرسال التنبيه' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('خطأ في إرسال التنبيه:', error);
    return NextResponse.json(
      { error: 'خطأ في الخادم' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // إرجاع إعدادات التنبيهات
    return NextResponse.json({
      emailEnabled: false, // يجب تحديثها حسب الإعدادات الفعلية
      webhookEnabled: false,
      supportedTypes: ['info', 'warning', 'error'],
      supportedSeverities: ['low', 'medium', 'high', 'critical'],
    });
  } catch (error) {
    console.error('خطأ في جلب إعدادات التنبيهات:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الإعدادات' },
      { status: 500 }
    );
  }
}
