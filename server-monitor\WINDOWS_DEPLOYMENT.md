# دليل النشر على Windows Server - نظام مراقبة السيرفرات

## 📋 نظرة عامة

هذا الدليل يوضح كيفية نشر نظام مراقبة السيرفرات على Windows Server في بيئة الإنتاج مع إعداد المراقبة الحقيقية للسيرفرات.

## 🔧 المتطلبات الأساسية

### متطلبات الأجهزة
- **المعالج**: Intel/AMD 64-bit، 2 cores كحد أدنى
- **الذاكرة**: 4GB RAM كحد أدنى، 8GB مُوصى به
- **التخزين**: 20GB مساحة فارغة كحد أدنى
- **الشبكة**: اتصال إنترنت مستقر

### متطلبات البرامج
- **Windows Server 2016** أو أحدث
- **Node.js 18.x** أو أحدث
- **Git** لإدارة الكود
- **PowerShell 5.1** أو أحدث

## 🚀 خطوات النشر

### الخطوة 1: إعداد البيئة

#### تثبيت Node.js
```powershell
# تحميل وتثبيت Node.js LTS
$nodeUrl = "https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi"
$nodeInstaller = "$env:TEMP\nodejs.msi"
Invoke-WebRequest -Uri $nodeUrl -OutFile $nodeInstaller
Start-Process msiexec.exe -Wait -ArgumentList "/I $nodeInstaller /quiet"
Remove-Item $nodeInstaller
```

#### تثبيت Git
```powershell
# تحميل وتثبيت Git
$gitUrl = "https://github.com/git-for-windows/git/releases/download/v2.42.0.windows.2/Git-2.42.0.2-64-bit.exe"
$gitInstaller = "$env:TEMP\git-installer.exe"
Invoke-WebRequest -Uri $gitUrl -OutFile $gitInstaller
Start-Process $gitInstaller -Wait -ArgumentList "/SILENT"
Remove-Item $gitInstaller
```

### الخطوة 2: نشر التطبيق

#### إنشاء مجلد التطبيق
```powershell
# إنشاء مجلد التطبيق
$appPath = "C:\ServerMonitor"
New-Item -ItemType Directory -Path $appPath -Force
Set-Location $appPath

# نسخ ملفات المشروع
# إذا كان المشروع في Git repository:
# git clone <repository-url> .

# أو نسخ الملفات يدوياً من مجلد التطوير
```

#### تثبيت التبعيات
```powershell
# تثبيت تبعيات الإنتاج
npm ci --only=production

# بناء التطبيق
npm run build
```

### الخطوة 3: إعداد متغيرات البيئة

```powershell
# إنشاء ملف .env للإنتاج
@"
NODE_ENV=production
PORT=3000
DB_PATH=./data/monitoring.db

# إعدادات البريد الإلكتروني
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
ALERT_EMAIL=<EMAIL>

# إعدادات الأمان
SESSION_SECRET=your-very-secure-random-string-here
ENCRYPTION_KEY=your-32-character-encryption-key

# إعدادات المراقبة
MONITORING_INTERVAL=60
ALERT_COOLDOWN=300
"@ | Out-File -FilePath ".env" -Encoding UTF8
```

### الخطوة 4: إعداد قاعدة البيانات

```powershell
# إنشاء مجلد البيانات
New-Item -ItemType Directory -Path "$appPath\data" -Force

# تشغيل التطبيق مرة واحدة لإنشاء قاعدة البيانات
npm start
# اضغط Ctrl+C بعد بدء التشغيل لإيقافه
```

### الخطوة 5: إعداد الأمان

#### إعداد Windows Firewall
```powershell
# فتح المنفذ 3000
New-NetFirewallRule -DisplayName "Server Monitor HTTP" -Direction Inbound -Protocol TCP -LocalPort 3000 -Action Allow

# فتح منافذ إضافية حسب الحاجة
New-NetFirewallRule -DisplayName "Server Monitor HTTPS" -Direction Inbound -Protocol TCP -LocalPort 443 -Action Allow
```

#### إعداد صلاحيات WMI
```powershell
# إعطاء صلاحيات WMI للمستخدم
$user = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
Write-Host "إعداد صلاحيات WMI للمستخدم: $user"

# تشغيل wmimgmt.msc لإعداد الصلاحيات يدوياً
# أو استخدام PowerShell:
$namespace = "root/cimv2"
$computer = "."

# منح صلاحيات القراءة والتنفيذ
$acl = Get-WmiObject -Namespace $namespace -Class __SystemSecurity
# يتطلب إعداد يدوي إضافي
```

### الخطوة 6: تشغيل التطبيق كخدمة

#### تثبيت PM2
```powershell
# تثبيت PM2 عالمياً
npm install -g pm2
npm install -g pm2-windows-service
```

#### إنشاء ملف تكوين PM2
```powershell
# إنشاء ecosystem.config.js
@"
module.exports = {
  apps: [{
    name: 'server-monitor',
    script: 'npm',
    args: 'start',
    cwd: '$appPath',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
}
"@ | Out-File -FilePath "ecosystem.config.js" -Encoding UTF8
```

#### تشغيل الخدمة
```powershell
# إنشاء مجلد السجلات
New-Item -ItemType Directory -Path "$appPath\logs" -Force

# بدء التطبيق مع PM2
pm2 start ecosystem.config.js

# حفظ التكوين
pm2 save

# تثبيت كخدمة Windows
pm2-service-install

# بدء الخدمة
pm2-service-start
```

## 🔧 إعداد المراقبة الحقيقية

### إضافة السيرفرات للمراقبة

#### للسيرفرات Windows
```powershell
# استخدام واجهة الويب أو API لإضافة سيرفر Windows
$serverData = @{
    name = "Windows Server 01"
    ip = "***********00"
    os = "windows"
    credentials = @{
        username = "Administrator"
        password = "SecurePassword123"
    }
}

# إرسال طلب إضافة السيرفر
Invoke-RestMethod -Uri "http://localhost:3000/api/servers/manage" -Method POST -Body ($serverData | ConvertTo-Json) -ContentType "application/json"
```

#### للسيرفرات Linux
```powershell
# إضافة سيرفر Linux مع SSH
$linuxServer = @{
    name = "Linux Server 01"
    ip = "*************"
    os = "linux"
    credentials = @{
        username = "root"
        keyPath = "C:\keys\linux-server.pem"
        # أو استخدام كلمة مرور
        # password = "LinuxPassword123"
    }
}

Invoke-RestMethod -Uri "http://localhost:3000/api/servers/manage" -Method POST -Body ($linuxServer | ConvertTo-Json) -ContentType "application/json"
```

### اكتشاف السيرفرات تلقائياً
```powershell
# اكتشاف السيرفرات في الشبكة المحلية
Invoke-RestMethod -Uri "http://localhost:3000/api/servers/discover" -Method POST -Body '{"action":"discover_local"}' -ContentType "application/json"

# اكتشاف في نطاق محدد
$discoverRange = @{
    action = "discover_range"
    range = @{
        startIP = "***********"
        endIP = "*************"
    }
}

Invoke-RestMethod -Uri "http://localhost:3000/api/servers/discover" -Method POST -Body ($discoverRange | ConvertTo-Json) -ContentType "application/json"
```

## 🌐 إعداد Reverse Proxy (اختياري)

### استخدام IIS
```powershell
# تثبيت IIS وURL Rewrite
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServer
Enable-WindowsOptionalFeature -Online -FeatureName IIS-CommonHttpFeatures
Enable-WindowsOptionalFeature -Online -FeatureName IIS-HttpRedirect

# تحميل وتثبيت URL Rewrite Module
# يجب تحميله من موقع Microsoft

# إنشاء موقع IIS
Import-Module WebAdministration
New-Website -Name "ServerMonitor" -Port 80 -PhysicalPath "$appPath\public"

# إعداد Reverse Proxy في web.config
$webConfig = @"
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <rewrite>
            <rules>
                <rule name="ReverseProxyInboundRule1" stopProcessing="true">
                    <match url="(.*)" />
                    <action type="Rewrite" url="http://localhost:3000/{R:1}" />
                </rule>
            </rules>
        </rewrite>
    </system.webServer>
</configuration>
"@

$webConfig | Out-File -FilePath "$appPath\public\web.config" -Encoding UTF8
```

## 📊 المراقبة والصيانة

### مراقبة الأداء
```powershell
# مراقبة استخدام الموارد
Get-Process -Name "node" | Select-Object CPU, WorkingSet, VirtualMemorySize

# مراقبة PM2
pm2 status
pm2 logs server-monitor
pm2 monit
```

### النسخ الاحتياطي
```powershell
# إنشاء نسخة احتياطية من قاعدة البيانات
$backupDir = "C:\Backup\ServerMonitor"
New-Item -ItemType Directory -Path $backupDir -Force

$date = Get-Date -Format "yyyyMMdd_HHmmss"
Copy-Item "$appPath\data\monitoring.db" "$backupDir\monitoring_$date.db"

# نسخ احتياطي للتكوين
Copy-Item "$appPath\.env" "$backupDir\.env_$date"
Copy-Item "$appPath\ecosystem.config.js" "$backupDir\ecosystem.config_$date.js"
```

### التحديثات
```powershell
# إيقاف الخدمة
pm2 stop server-monitor

# تحديث التبعيات
npm update

# إعادة بناء التطبيق
npm run build

# إعادة تشغيل الخدمة
pm2 start server-monitor
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في الاتصال بقاعدة البيانات
```powershell
# التحقق من وجود مجلد البيانات
Test-Path "$appPath\data"

# التحقق من صلاحيات الملف
Get-Acl "$appPath\data\monitoring.db"
```

#### مشاكل WMI
```powershell
# اختبار صلاحيات WMI
Get-WmiObject -Class Win32_Processor
Get-WmiObject -Class Win32_OperatingSystem

# إعادة تشغيل خدمة WMI
Restart-Service Winmgmt -Force
```

#### مشاكل الشبكة
```powershell
# اختبار الاتصال
Test-NetConnection -ComputerName "***********00" -Port 135
Test-NetConnection -ComputerName "*************" -Port 22

# التحقق من Firewall
Get-NetFirewallRule -DisplayName "*Server Monitor*"
```

### السجلات
```powershell
# عرض سجلات التطبيق
pm2 logs server-monitor

# عرض سجلات Windows
Get-EventLog -LogName Application -Source "Node.js" -Newest 50

# عرض سجلات النظام
Get-EventLog -LogName System -Newest 50 | Where-Object {$_.Source -like "*WMI*"}
```

## 📞 الدعم والصيانة

### الصيانة الدورية
1. **يومياً**: مراجعة السجلات والتنبيهات
2. **أسبوعياً**: نسخ احتياطي لقاعدة البيانات
3. **شهرياً**: تحديث التبعيات وإعادة تشغيل الخدمة
4. **ربع سنوياً**: مراجعة شاملة للأمان والأداء

---

**ملاحظة**: هذا الدليل يغطي النشر الأساسي. للبيئات المعقدة، قد تحتاج إلى تخصيصات إضافية حسب متطلبات الشركة.
