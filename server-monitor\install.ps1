# سكريبت التثبيت التلقائي لنظام مراقبة السيرفرات على Windows Server
# PowerShell Installation Script for Server Monitoring System

param(
    [string]$InstallPath = "C:\ServerMonitor",
    [string]$Port = "3000",
    [string]$SmtpHost = "",
    [string]$SmtpUser = "",
    [string]$SmtpPass = "",
    [string]$AlertEmail = "",
    [switch]$SkipNodeInstall = $false,
    [switch]$SkipGitInstall = $false,
    [switch]$SkipFirewall = $false
)

# تحديد الألوان للرسائل
$ErrorColor = "Red"
$SuccessColor = "Green"
$InfoColor = "Cyan"
$WarningColor = "Yellow"

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Install-NodeJS {
    Write-ColorOutput "تثبيت Node.js..." $InfoColor
    
    try {
        # التحقق من وجود Node.js
        $nodeVersion = node --version 2>$null
        if ($nodeVersion) {
            Write-ColorOutput "Node.js موجود بالفعل: $nodeVersion" $SuccessColor
            return $true
        }
    }
    catch {
        # Node.js غير موجود، سيتم تثبيته
    }

    $nodeUrl = "https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi"
    $nodeInstaller = "$env:TEMP\nodejs.msi"
    
    try {
        Write-ColorOutput "تحميل Node.js..." $InfoColor
        Invoke-WebRequest -Uri $nodeUrl -OutFile $nodeInstaller -UseBasicParsing
        
        Write-ColorOutput "تثبيت Node.js..." $InfoColor
        Start-Process msiexec.exe -Wait -ArgumentList "/I `"$nodeInstaller`" /quiet" -NoNewWindow
        
        Remove-Item $nodeInstaller -Force
        
        # تحديث متغيرات البيئة
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
        
        # التحقق من التثبيت
        $nodeVersion = node --version 2>$null
        if ($nodeVersion) {
            Write-ColorOutput "تم تثبيت Node.js بنجاح: $nodeVersion" $SuccessColor
            return $true
        }
        else {
            Write-ColorOutput "فشل في تثبيت Node.js" $ErrorColor
            return $false
        }
    }
    catch {
        Write-ColorOutput "خطأ في تثبيت Node.js: $($_.Exception.Message)" $ErrorColor
        return $false
    }
}

function Install-Git {
    Write-ColorOutput "تثبيت Git..." $InfoColor
    
    try {
        # التحقق من وجود Git
        $gitVersion = git --version 2>$null
        if ($gitVersion) {
            Write-ColorOutput "Git موجود بالفعل: $gitVersion" $SuccessColor
            return $true
        }
    }
    catch {
        # Git غير موجود، سيتم تثبيته
    }

    $gitUrl = "https://github.com/git-for-windows/git/releases/download/v2.42.0.windows.2/Git-2.42.0.2-64-bit.exe"
    $gitInstaller = "$env:TEMP\git-installer.exe"
    
    try {
        Write-ColorOutput "تحميل Git..." $InfoColor
        Invoke-WebRequest -Uri $gitUrl -OutFile $gitInstaller -UseBasicParsing
        
        Write-ColorOutput "تثبيت Git..." $InfoColor
        Start-Process $gitInstaller -Wait -ArgumentList "/SILENT" -NoNewWindow
        
        Remove-Item $gitInstaller -Force
        
        # تحديث متغيرات البيئة
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
        
        # التحقق من التثبيت
        $gitVersion = git --version 2>$null
        if ($gitVersion) {
            Write-ColorOutput "تم تثبيت Git بنجاح: $gitVersion" $SuccessColor
            return $true
        }
        else {
            Write-ColorOutput "فشل في تثبيت Git" $ErrorColor
            return $false
        }
    }
    catch {
        Write-ColorOutput "خطأ في تثبيت Git: $($_.Exception.Message)" $ErrorColor
        return $false
    }
}

function Setup-Application {
    param([string]$Path)
    
    Write-ColorOutput "إعداد التطبيق في: $Path" $InfoColor
    
    try {
        # إنشاء مجلد التطبيق
        if (!(Test-Path $Path)) {
            New-Item -ItemType Directory -Path $Path -Force | Out-Null
            Write-ColorOutput "تم إنشاء مجلد التطبيق" $SuccessColor
        }
        
        Set-Location $Path
        
        # التحقق من وجود ملفات المشروع
        if (!(Test-Path "package.json")) {
            Write-ColorOutput "يرجى نسخ ملفات المشروع إلى: $Path" $WarningColor
            Write-ColorOutput "أو استخدام Git لاستنساخ المشروع" $WarningColor
            return $false
        }
        
        # تثبيت التبعيات
        Write-ColorOutput "تثبيت تبعيات المشروع..." $InfoColor
        npm install
        
        if ($LASTEXITCODE -ne 0) {
            Write-ColorOutput "فشل في تثبيت التبعيات" $ErrorColor
            return $false
        }
        
        # بناء التطبيق
        Write-ColorOutput "بناء التطبيق..." $InfoColor
        npm run build
        
        if ($LASTEXITCODE -ne 0) {
            Write-ColorOutput "فشل في بناء التطبيق" $ErrorColor
            return $false
        }
        
        Write-ColorOutput "تم إعداد التطبيق بنجاح" $SuccessColor
        return $true
    }
    catch {
        Write-ColorOutput "خطأ في إعداد التطبيق: $($_.Exception.Message)" $ErrorColor
        return $false
    }
}

function Create-EnvironmentFile {
    param(
        [string]$Path,
        [string]$Port,
        [string]$SmtpHost,
        [string]$SmtpUser,
        [string]$SmtpPass,
        [string]$AlertEmail
    )
    
    Write-ColorOutput "إنشاء ملف متغيرات البيئة..." $InfoColor
    
    $envContent = @"
NODE_ENV=production
PORT=$Port
DB_PATH=./data/monitoring.db

# إعدادات البريد الإلكتروني
SMTP_HOST=$SmtpHost
SMTP_PORT=587
SMTP_USER=$SmtpUser
SMTP_PASS=$SmtpPass
ALERT_EMAIL=$AlertEmail

# إعدادات الأمان
SESSION_SECRET=$(New-Guid)
ENCRYPTION_KEY=$((1..32 | ForEach-Object { Get-Random -Maximum 256 } | ForEach-Object { [char]$_ }) -join '')

# إعدادات المراقبة
MONITORING_INTERVAL=60
ALERT_COOLDOWN=300
"@

    try {
        $envContent | Out-File -FilePath "$Path\.env" -Encoding UTF8
        Write-ColorOutput "تم إنشاء ملف .env بنجاح" $SuccessColor
        return $true
    }
    catch {
        Write-ColorOutput "خطأ في إنشاء ملف .env: $($_.Exception.Message)" $ErrorColor
        return $false
    }
}

function Setup-Database {
    param([string]$Path)
    
    Write-ColorOutput "إعداد قاعدة البيانات..." $InfoColor
    
    try {
        # إنشاء مجلد البيانات
        $dataPath = "$Path\data"
        if (!(Test-Path $dataPath)) {
            New-Item -ItemType Directory -Path $dataPath -Force | Out-Null
        }
        
        # تشغيل التطبيق لإنشاء قاعدة البيانات
        Write-ColorOutput "تشغيل التطبيق لإنشاء قاعدة البيانات..." $InfoColor
        $process = Start-Process npm -ArgumentList "start" -WorkingDirectory $Path -PassThru -WindowStyle Hidden
        
        # انتظار 10 ثوان لإنشاء قاعدة البيانات
        Start-Sleep -Seconds 10
        
        # إيقاف العملية
        if (!$process.HasExited) {
            $process.Kill()
        }
        
        # التحقق من إنشاء قاعدة البيانات
        if (Test-Path "$dataPath\monitoring.db") {
            Write-ColorOutput "تم إنشاء قاعدة البيانات بنجاح" $SuccessColor
            return $true
        }
        else {
            Write-ColorOutput "فشل في إنشاء قاعدة البيانات" $ErrorColor
            return $false
        }
    }
    catch {
        Write-ColorOutput "خطأ في إعداد قاعدة البيانات: $($_.Exception.Message)" $ErrorColor
        return $false
    }
}

function Setup-Firewall {
    Write-ColorOutput "إعداد Windows Firewall..." $InfoColor
    
    try {
        # فتح المنفذ للتطبيق
        New-NetFirewallRule -DisplayName "Server Monitor HTTP" -Direction Inbound -Protocol TCP -LocalPort $Port -Action Allow -ErrorAction SilentlyContinue
        New-NetFirewallRule -DisplayName "Server Monitor HTTPS" -Direction Inbound -Protocol TCP -LocalPort 443 -Action Allow -ErrorAction SilentlyContinue
        
        Write-ColorOutput "تم إعداد Firewall بنجاح" $SuccessColor
        return $true
    }
    catch {
        Write-ColorOutput "خطأ في إعداد Firewall: $($_.Exception.Message)" $ErrorColor
        return $false
    }
}

function Install-PM2 {
    Write-ColorOutput "تثبيت PM2..." $InfoColor
    
    try {
        # تثبيت PM2
        npm install -g pm2
        npm install -g pm2-windows-service
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "تم تثبيت PM2 بنجاح" $SuccessColor
            return $true
        }
        else {
            Write-ColorOutput "فشل في تثبيت PM2" $ErrorColor
            return $false
        }
    }
    catch {
        Write-ColorOutput "خطأ في تثبيت PM2: $($_.Exception.Message)" $ErrorColor
        return $false
    }
}

function Setup-PM2Service {
    param([string]$Path, [string]$Port)
    
    Write-ColorOutput "إعداد خدمة PM2..." $InfoColor
    
    try {
        # إنشاء ملف تكوين PM2
        $ecosystemConfig = @"
module.exports = {
  apps: [{
    name: 'server-monitor',
    script: 'npm',
    args: 'start',
    cwd: '$Path',
    env: {
      NODE_ENV: 'production',
      PORT: $Port
    },
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
}
"@

        $ecosystemConfig | Out-File -FilePath "$Path\ecosystem.config.js" -Encoding UTF8
        
        # إنشاء مجلد السجلات
        $logsPath = "$Path\logs"
        if (!(Test-Path $logsPath)) {
            New-Item -ItemType Directory -Path $logsPath -Force | Out-Null
        }
        
        Set-Location $Path
        
        # بدء التطبيق مع PM2
        pm2 start ecosystem.config.js
        pm2 save
        
        # تثبيت كخدمة Windows
        pm2-service-install
        pm2-service-start
        
        Write-ColorOutput "تم إعداد خدمة PM2 بنجاح" $SuccessColor
        return $true
    }
    catch {
        Write-ColorOutput "خطأ في إعداد خدمة PM2: $($_.Exception.Message)" $ErrorColor
        return $false
    }
}

# البدء في عملية التثبيت
Write-ColorOutput "=== بدء تثبيت نظام مراقبة السيرفرات ===" $InfoColor

# التحقق من صلاحيات المدير
if (!(Test-Administrator)) {
    Write-ColorOutput "يجب تشغيل هذا السكريبت بصلاحيات المدير" $ErrorColor
    Write-ColorOutput "يرجى تشغيل PowerShell كمدير وإعادة تشغيل السكريبت" $ErrorColor
    exit 1
}

# تثبيت Node.js
if (!$SkipNodeInstall) {
    if (!(Install-NodeJS)) {
        Write-ColorOutput "فشل في تثبيت Node.js. إيقاف التثبيت." $ErrorColor
        exit 1
    }
}

# تثبيت Git
if (!$SkipGitInstall) {
    if (!(Install-Git)) {
        Write-ColorOutput "فشل في تثبيت Git. إيقاف التثبيت." $ErrorColor
        exit 1
    }
}

# إعداد التطبيق
if (!(Setup-Application -Path $InstallPath)) {
    Write-ColorOutput "فشل في إعداد التطبيق. إيقاف التثبيت." $ErrorColor
    exit 1
}

# إنشاء ملف متغيرات البيئة
if (!(Create-EnvironmentFile -Path $InstallPath -Port $Port -SmtpHost $SmtpHost -SmtpUser $SmtpUser -SmtpPass $SmtpPass -AlertEmail $AlertEmail)) {
    Write-ColorOutput "فشل في إنشاء ملف متغيرات البيئة. إيقاف التثبيت." $ErrorColor
    exit 1
}

# إعداد قاعدة البيانات
if (!(Setup-Database -Path $InstallPath)) {
    Write-ColorOutput "فشل في إعداد قاعدة البيانات. إيقاف التثبيت." $ErrorColor
    exit 1
}

# إعداد Firewall
if (!$SkipFirewall) {
    Setup-Firewall | Out-Null
}

# تثبيت PM2
if (!(Install-PM2)) {
    Write-ColorOutput "فشل في تثبيت PM2. إيقاف التثبيت." $ErrorColor
    exit 1
}

# إعداد خدمة PM2
if (!(Setup-PM2Service -Path $InstallPath -Port $Port)) {
    Write-ColorOutput "فشل في إعداد خدمة PM2. إيقاف التثبيت." $ErrorColor
    exit 1
}

Write-ColorOutput "=== تم التثبيت بنجاح ===" $SuccessColor
Write-ColorOutput "يمكنك الوصول للنظام عبر: http://localhost:$Port" $InfoColor
Write-ColorOutput "مجلد التثبيت: $InstallPath" $InfoColor
Write-ColorOutput "لمراقبة الخدمة: pm2 status" $InfoColor
Write-ColorOutput "لعرض السجلات: pm2 logs server-monitor" $InfoColor
