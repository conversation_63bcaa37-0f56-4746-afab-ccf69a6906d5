import jsPDF from 'jspdf';

export interface ReportData {
  servers: Array<{
    id: string;
    name: string;
    status: string;
    cpu: number;
    memory: number;
    diskUsage: number;
    disks: Array<{
      name: string;
      health: string;
      usage: number;
      size: string;
      badSectors?: number;
    }>;
    uptime?: number;
  }>;
  alerts: Array<{
    type: string;
    title: string;
    message: string;
    serverName: string;
    timestamp: string;
    severity: string;
  }>;
  period: {
    from: string;
    to: string;
  };
}

export class ReportGenerator {
  private doc: jsPDF;
  private pageHeight: number;
  private currentY: number;
  private margin: number;

  constructor() {
    this.doc = new jsPDF();
    this.pageHeight = this.doc.internal.pageSize.height;
    this.currentY = 20;
    this.margin = 20;
  }

  async generateReport(data: ReportData): Promise<Blob> {
    // إعداد الخط العربي (إذا كان متاحاً)
    this.setupArabicFont();

    // إضافة العنوان الرئيسي
    this.addTitle('Server Monitoring Report');

    // إضافة معلومات التقرير
    this.addReportInfo(data.period);

    // إضافة ملخص عام
    this.addSummary(data.servers, data.alerts);

    // إضافة تفاصيل السيرفرات
    this.addServersDetails(data.servers);

    // إضافة التنبيهات
    this.addAlertsSection(data.alerts);

    // إضافة التوصيات
    this.addRecommendations(data.servers, data.alerts);

    // إضافة تذييل
    this.addFooter();

    return this.doc.output('blob');
  }

  private setupArabicFont() {
    // إعداد الخط الافتراضي
    this.doc.setFont('helvetica');
  }

  private addTitle(title: string) {
    this.doc.setFontSize(20);
    this.doc.setFont('helvetica', 'bold');

    // حساب عرض النص لتوسيطه
    const textWidth = this.doc.getTextWidth(title);
    const pageWidth = this.doc.internal.pageSize.width;
    const x = (pageWidth - textWidth) / 2;

    this.doc.text(title, x, this.currentY);
    this.currentY += 15;

    // إضافة خط تحت العنوان
    this.doc.setLineWidth(0.5);
    this.doc.line(this.margin, this.currentY, pageWidth - this.margin, this.currentY);
    this.currentY += 10;
  }

  private addReportInfo(period: { from: string; to: string }) {
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'normal');

    const fromDate = new Date(period.from).toLocaleDateString('en-US');
    const toDate = new Date(period.to).toLocaleDateString('en-US');
    const generatedDate = new Date().toLocaleDateString('en-US');

    this.doc.text(`Report Period: ${fromDate} - ${toDate}`, this.margin, this.currentY);
    this.currentY += 8;
    this.doc.text(`Generated on: ${generatedDate}`, this.margin, this.currentY);
    this.currentY += 15;
  }

  private addSummary(servers: any[], alerts: any[]) {
    this.addSectionTitle('Executive Summary');

    const totalServers = servers.length;
    const onlineServers = servers.filter(s => s.status === 'online').length;
    const warningServers = servers.filter(s => s.status === 'warning').length;
    const offlineServers = servers.filter(s => s.status === 'offline').length;

    const criticalAlerts = alerts.filter(a => a.severity === 'critical').length;
    const highAlerts = alerts.filter(a => a.severity === 'high').length;

    this.doc.setFontSize(10);
    this.doc.text(`Total Servers: ${totalServers}`, this.margin, this.currentY);
    this.currentY += 6;
    this.doc.text(`Online: ${onlineServers}, Warning: ${warningServers}, Offline: ${offlineServers}`, this.margin, this.currentY);
    this.currentY += 6;
    this.doc.text(`Critical Alerts: ${criticalAlerts}, High Priority: ${highAlerts}`, this.margin, this.currentY);
    this.currentY += 15;
  }

  private addServersDetails(servers: any[]) {
    this.addSectionTitle('Server Details');

    servers.forEach((server, index) => {
      if (this.currentY > this.pageHeight - 50) {
        this.doc.addPage();
        this.currentY = 20;
      }

      this.doc.setFontSize(12);
      this.doc.setFont('helvetica', 'bold');
      this.doc.text(`${index + 1}. ${server.name}`, this.margin, this.currentY);
      this.currentY += 8;

      this.doc.setFontSize(10);
      this.doc.setFont('helvetica', 'normal');
      this.doc.text(`Status: ${server.status}`, this.margin + 10, this.currentY);
      this.currentY += 6;
      this.doc.text(`CPU: ${server.cpu}%, Memory: ${server.memory}%, Disk: ${server.diskUsage}%`, this.margin + 10, this.currentY);
      this.currentY += 6;

      // تفاصيل الأقراص
      if (server.disks && server.disks.length > 0) {
        this.doc.text('Disks:', this.margin + 10, this.currentY);
        this.currentY += 6;

        server.disks.forEach((disk: any) => {
          const diskInfo = `  ${disk.name} (${disk.size}): ${disk.usage}% - ${disk.health}`;
          this.doc.text(diskInfo, this.margin + 15, this.currentY);
          this.currentY += 5;

          if (disk.badSectors && disk.badSectors > 0) {
            this.doc.text(`    Bad Sectors: ${disk.badSectors}`, this.margin + 20, this.currentY);
            this.currentY += 5;
          }
        });
      }

      this.currentY += 5;
    });
  }

  private addAlertsSection(alerts: any[]) {
    if (alerts.length === 0) return;

    this.addSectionTitle('Active Alerts');

    alerts.forEach((alert, index) => {
      if (this.currentY > this.pageHeight - 30) {
        this.doc.addPage();
        this.currentY = 20;
      }

      this.doc.setFontSize(10);
      this.doc.setFont('helvetica', 'bold');
      this.doc.text(`${index + 1}. ${alert.title}`, this.margin, this.currentY);
      this.currentY += 6;

      this.doc.setFont('helvetica', 'normal');
      this.doc.text(`Server: ${alert.serverName}`, this.margin + 10, this.currentY);
      this.currentY += 5;
      this.doc.text(`Severity: ${alert.severity}`, this.margin + 10, this.currentY);
      this.currentY += 5;
      this.doc.text(`Time: ${new Date(alert.timestamp).toLocaleString()}`, this.margin + 10, this.currentY);
      this.currentY += 5;

      // تقسيم الرسالة إلى أسطر متعددة إذا كانت طويلة
      const lines = this.doc.splitTextToSize(alert.message, 160);
      this.doc.text(lines, this.margin + 10, this.currentY);
      this.currentY += lines.length * 5 + 5;
    });
  }

  private addRecommendations(servers: any[], alerts: any[]) {
    this.addSectionTitle('Recommendations');

    const recommendations: string[] = [];

    // تحليل السيرفرات وإضافة توصيات
    servers.forEach(server => {
      if (server.cpu > 80) {
        recommendations.push(`Consider upgrading CPU for ${server.name}`);
      }
      if (server.memory > 85) {
        recommendations.push(`Consider adding more RAM to ${server.name}`);
      }
      if (server.diskUsage > 90) {
        recommendations.push(`Urgent: Free up disk space on ${server.name}`);
      }

      server.disks?.forEach((disk: any) => {
        if (disk.badSectors && disk.badSectors > 0) {
          recommendations.push(`Replace disk ${disk.name} on ${server.name} due to bad sectors`);
        }
      });
    });

    // تحليل التنبيهات وإضافة توصيات
    const criticalAlerts = alerts.filter(a => a.severity === 'critical').length;
    if (criticalAlerts > 0) {
      recommendations.push('Immediate attention required for critical alerts');
    }

    if (recommendations.length === 0) {
      recommendations.push('All systems are operating within normal parameters');
    }

    this.doc.setFontSize(10);
    recommendations.forEach((rec, index) => {
      if (this.currentY > this.pageHeight - 20) {
        this.doc.addPage();
        this.currentY = 20;
      }

      this.doc.text(`${index + 1}. ${rec}`, this.margin, this.currentY);
      this.currentY += 6;
    });
  }

  private addSectionTitle(title: string) {
    if (this.currentY > this.pageHeight - 30) {
      this.doc.addPage();
      this.currentY = 20;
    }

    this.doc.setFontSize(14);
    this.doc.setFont('helvetica', 'bold');

    this.doc.text(title, this.margin, this.currentY);
    this.currentY += 10;

    // خط تحت العنوان
    this.doc.setLineWidth(0.3);
    this.doc.line(this.margin, this.currentY, this.margin + 60, this.currentY);
    this.currentY += 8;
  }

  private addFooter() {
    const pageCount = this.doc.getNumberOfPages();

    for (let i = 1; i <= pageCount; i++) {
      this.doc.setPage(i);
      this.doc.setFontSize(8);
      this.doc.setFont('helvetica', 'normal');

      const footerText = `Server Monitoring System - Page ${i} of ${pageCount}`;
      const pageWidth = this.doc.internal.pageSize.width;
      const textWidth = this.doc.getTextWidth(footerText);
      const x = (pageWidth - textWidth) / 2;

      this.doc.text(footerText, x, this.pageHeight - 10);
    }
  }
}

export async function generateServerReport(period?: { from: string; to: string }): Promise<Blob> {
  try {
    // جلب بيانات السيرفرات
    const serversResponse = await fetch('/api/servers');
    const servers = await serversResponse.json();

    // جلب التنبيهات
    const alertsResponse = await fetch('/api/alerts');
    const alerts = await alertsResponse.json();

    const reportData: ReportData = {
      servers,
      alerts,
      period: period || {
        from: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // آخر 24 ساعة
        to: new Date().toISOString(),
      },
    };

    const generator = new ReportGenerator();
    return await generator.generateReport(reportData);
  } catch (error) {
    console.error('خطأ في إنتاج التقرير:', error);
    throw error;
  }
}
