# دليل تثبيت نظام مراقبة السيرفرات على Windows Server

## 📋 المتطلبات الأساسية

### متطلبات النظام
- **Windows Server 2016** أو أحدث
- **RAM**: 4GB كحد أدنى، 8GB مُوصى به
- **مساحة التخزين**: 10GB مساحة فارغة
- **المعالج**: Intel/AMD 64-bit
- **الشبكة**: اتصال إنترنت للتحديثات

### البرامج المطلوبة
1. **Node.js 18.x** أو أحدث
2. **Git** لإدارة الكود
3. **PowerShell 5.1** أو أحدث
4. **IIS** (اختياري للنشر)

## 🔧 خطوات التثبيت

### الخطوة 1: تثبيت Node.js
```powershell
# تحميل وتثبيت Node.js
Invoke-WebRequest -Uri "https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi" -OutFile "nodejs.msi"
Start-Process msiexec.exe -Wait -ArgumentList '/I nodejs.msi /quiet'
```

### الخطوة 2: تثبيت Git
```powershell
# تحميل وتثبيت Git
Invoke-WebRequest -Uri "https://github.com/git-for-windows/git/releases/download/v2.42.0.windows.2/Git-2.42.0.2-64-bit.exe" -OutFile "git-installer.exe"
Start-Process "git-installer.exe" -Wait -ArgumentList "/SILENT"
```

### الخطوة 3: إعداد المشروع
```powershell
# إنشاء مجلد المشروع
New-Item -ItemType Directory -Path "C:\ServerMonitor" -Force
Set-Location "C:\ServerMonitor"

# نسخ ملفات المشروع (إذا كان متوفراً في Git)
# git clone <repository-url> .

# تثبيت التبعيات
npm install
```

### الخطوة 4: إعداد متغيرات البيئة
```powershell
# إنشاء ملف .env
@"
NODE_ENV=production
PORT=3000
DB_PATH=./data/monitoring.db
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
ALERT_EMAIL=<EMAIL>
"@ | Out-File -FilePath ".env" -Encoding UTF8
```

## 🔐 إعداد الأمان

### إعداد Windows Firewall
```powershell
# فتح المنفذ 3000 للنظام
New-NetFirewallRule -DisplayName "Server Monitor" -Direction Inbound -Protocol TCP -LocalPort 3000 -Action Allow
```

### إعداد صلاحيات WMI
```powershell
# إعطاء صلاحيات WMI للمستخدم الحالي
$user = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
$namespace = "root/cimv2"
$computer = "."

# تشغيل wmimgmt.msc لإعداد الصلاحيات يدوياً
wmimgmt.msc
```

## 🚀 تشغيل النظام

### التشغيل في وضع التطوير
```powershell
npm run dev
```

### التشغيل في وضع الإنتاج
```powershell
npm run build
npm start
```

### تشغيل النظام كخدمة Windows
```powershell
# تثبيت PM2 لإدارة العمليات
npm install -g pm2
npm install -g pm2-windows-service

# إنشاء ملف ecosystem.config.js
@"
module.exports = {
  apps: [{
    name: 'server-monitor',
    script: 'npm',
    args: 'start',
    cwd: 'C:\\ServerMonitor',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G'
  }]
}
"@ | Out-File -FilePath "ecosystem.config.js" -Encoding UTF8

# تشغيل النظام كخدمة
pm2 start ecosystem.config.js
pm2 save
pm2-service-install
```

## 🌐 إعداد IIS (اختياري)

### إعداد Reverse Proxy مع IIS
```powershell
# تثبيت URL Rewrite و Application Request Routing
# يجب تحميلهما من موقع Microsoft

# إنشاء موقع جديد في IIS
Import-Module WebAdministration
New-Website -Name "ServerMonitor" -Port 80 -PhysicalPath "C:\ServerMonitor\public"
```

## 📊 التحقق من التثبيت

### اختبار النظام
```powershell
# اختبار الاتصال بالنظام
Invoke-WebRequest -Uri "http://localhost:3000" -UseBasicParsing

# اختبار APIs
Invoke-WebRequest -Uri "http://localhost:3000/api/servers" -UseBasicParsing
Invoke-WebRequest -Uri "http://localhost:3000/api/alerts" -UseBasicParsing
```

### مراقبة الأداء
```powershell
# مراقبة استخدام الموارد
Get-Process -Name "node" | Select-Object CPU, WorkingSet, VirtualMemorySize

# مراقبة السجلات
Get-EventLog -LogName Application -Source "Node.js" -Newest 10
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ في الاتصال بقاعدة البيانات
```powershell
# التحقق من وجود مجلد البيانات
Test-Path "C:\ServerMonitor\data"
# إنشاء المجلد إذا لم يكن موجوداً
New-Item -ItemType Directory -Path "C:\ServerMonitor\data" -Force
```

#### خطأ في صلاحيات WMI
```powershell
# اختبار صلاحيات WMI
Get-WmiObject -Class Win32_Processor
Get-WmiObject -Class Win32_OperatingSystem
```

#### مشاكل في المنافذ
```powershell
# التحقق من المنافذ المستخدمة
netstat -an | findstr :3000
```

## 📝 الصيانة الدورية

### النسخ الاحتياطي
```powershell
# نسخ احتياطي لقاعدة البيانات
Copy-Item "C:\ServerMonitor\data\monitoring.db" "C:\Backup\monitoring_$(Get-Date -Format 'yyyyMMdd').db"
```

### التحديثات
```powershell
# تحديث التبعيات
npm update

# إعادة تشغيل الخدمة
pm2 restart server-monitor
```

## 📞 الدعم الفني

في حالة مواجهة مشاكل:
1. تحقق من سجلات النظام
2. تأكد من صلاحيات المستخدم
3. تحقق من إعدادات Firewall
4. راجع متغيرات البيئة

---

**ملاحظة**: هذا الدليل يفترض تثبيت النظام على Windows Server في بيئة إنتاج. للتطوير، يمكن تبسيط بعض الخطوات.
