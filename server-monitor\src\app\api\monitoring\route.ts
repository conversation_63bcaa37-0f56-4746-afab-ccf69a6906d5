import { NextResponse } from 'next/server';
import { realTimeAlerts } from '@/lib/realTimeAlerts';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { action, intervalMinutes } = body;

    switch (action) {
      case 'start':
        // بدء مراقبة التنبيهات
        const interval = intervalMinutes || 1; // افتراضي: كل دقيقة
        realTimeAlerts.startMonitoring(interval);
        
        return NextResponse.json({
          success: true,
          message: `تم بدء مراقبة التنبيهات كل ${interval} دقيقة`,
          intervalMinutes: interval
        });

      case 'stop':
        // إيقاف مراقبة التنبيهات
        realTimeAlerts.stopMonitoring();
        
        return NextResponse.json({
          success: true,
          message: 'تم إيقاف مراقبة التنبيهات'
        });

      case 'status':
        // الحصول على حالة المراقبة
        const activeAlerts = realTimeAlerts.getActiveAlerts();
        const totalAlerts = realTimeAlerts.getAlerts();
        
        return NextResponse.json({
          success: true,
          monitoring: {
            isActive: true, // يمكن إضافة خاصية للتحقق من حالة المراقبة
            activeAlertsCount: activeAlerts.length,
            totalAlertsCount: totalAlerts.length,
            lastCheck: new Date().toISOString()
          }
        });

      case 'force_check':
        // فحص فوري لجميع السيرفرات
        // هذا سيتم تنفيذه في الخلفية
        setTimeout(async () => {
          try {
            // سيتم استدعاء checkAllServers داخلياً
            console.log('تنفيذ فحص فوري للسيرفرات...');
          } catch (error) {
            console.error('خطأ في الفحص الفوري:', error);
          }
        }, 0);
        
        return NextResponse.json({
          success: true,
          message: 'تم بدء الفحص الفوري للسيرفرات'
        });

      default:
        return NextResponse.json(
          { error: 'إجراء غير مدعوم' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('خطأ في إدارة المراقبة:', error);
    return NextResponse.json(
      { error: 'فشل في إدارة المراقبة', details: error instanceof Error ? error.message : 'خطأ غير معروف' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const activeAlerts = realTimeAlerts.getActiveAlerts();
    const totalAlerts = realTimeAlerts.getAlerts();
    const alertRules = realTimeAlerts.getAlertRules();
    
    return NextResponse.json({
      success: true,
      monitoring: {
        isActive: true,
        activeAlertsCount: activeAlerts.length,
        totalAlertsCount: totalAlerts.length,
        alertRulesCount: alertRules.length,
        lastCheck: new Date().toISOString()
      },
      alertRules: alertRules.map(rule => ({
        id: rule.id,
        name: rule.name,
        category: rule.category,
        condition: rule.condition,
        threshold: rule.threshold,
        severity: rule.severity,
        enabled: rule.enabled,
        cooldown: rule.cooldown,
        lastTriggered: rule.lastTriggered?.toISOString()
      }))
    });
  } catch (error) {
    console.error('خطأ في جلب حالة المراقبة:', error);
    return NextResponse.json(
      { error: 'فشل في جلب حالة المراقبة' },
      { status: 500 }
    );
  }
}
