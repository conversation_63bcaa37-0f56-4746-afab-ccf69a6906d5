# دليل استكشاف الأخطاء وحلها - نظام مراقبة السيرفرات

## 🚨 الأخطاء الشائعة وحلولها

### 1. أخطاء التثبيت والإعداد

#### خطأ: "npm ERR! peer dep missing"
```bash
# الحل: تثبيت التبعيات المفقودة
npm install --legacy-peer-deps

# أو استخدام yarn
yarn install
```

#### خطأ: "Module not found: Can't resolve 'systeminformation'"
```bash
# الحل: إعادة تثبيت المكتبة
npm uninstall systeminformation
npm install systeminformation

# في حالة Windows، قد تحتاج إلى:
npm install --global windows-build-tools
```

#### خطأ: "Python executable not found"
```bash
# Windows
npm install --global windows-build-tools

# macOS
xcode-select --install

# Linux (Ubuntu/Debian)
sudo apt-get install build-essential python3-dev

# Linux (CentOS/RHEL)
sudo yum groupinstall "Development Tools"
sudo yum install python3-devel
```

### 2. أخطاء وقت التشغيل

#### خطأ: "EADDRINUSE: address already in use :::3000"
```bash
# العثور على العملية التي تستخدم المنفذ
# Windows
netstat -ano | findstr :3000
taskkill /PID <PID> /F

# macOS/Linux
lsof -ti:3000 | xargs kill -9

# أو تغيير المنفذ
npm run dev -- -p 3001
```

#### خطأ: "Cannot read properties of undefined"
```typescript
// المشكلة: عدم التحقق من وجود البيانات
// الحل الخطأ:
const cpuUsage = server.cpu;

// الحل الصحيح:
const cpuUsage = server?.cpu ?? 0;

// أو استخدام التحقق التقليدي:
const cpuUsage = server && server.cpu ? server.cpu : 0;
```

#### خطأ: "Failed to fetch" في المتصفح
```typescript
// المشكلة: CORS أو مسار API خاطئ
// التحقق من:
1. تأكد من أن الخادم يعمل
2. تحقق من مسار API في Network tab
3. تأكد من أن API route موجود

// إضافة معالجة الأخطاء:
try {
  const response = await fetch('/api/servers');
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  const data = await response.json();
} catch (error) {
  console.error('خطأ في جلب البيانات:', error);
  // عرض رسالة خطأ للمستخدم
}
```

### 3. أخطاء قاعدة البيانات

#### خطأ: "SQLITE_CANTOPEN: unable to open database file"
```bash
# إنشاء مجلد البيانات
mkdir -p data

# إعطاء صلاحيات الكتابة
chmod 755 data

# التحقق من المسار
ls -la data/
```

#### خطأ: "database is locked"
```typescript
// الحل: إغلاق الاتصالات بشكل صحيح
export async function closeDatabase(db: any) {
  try {
    await db.close();
  } catch (error) {
    console.error('خطأ في إغلاق قاعدة البيانات:', error);
  }
}

// استخدام try/finally
let db;
try {
  db = await openDatabase();
  // العمليات...
} finally {
  if (db) {
    await closeDatabase(db);
  }
}
```

### 4. أخطاء الإيميل والإشعارات

#### خطأ: "Invalid login: 535-5.7.8 Username and Password not accepted"
```typescript
// المشكلة: إعدادات SMTP خاطئة
// الحل لـ Gmail:
const transporter = nodemailer.createTransporter({
  service: 'gmail',
  auth: {
    user: '<EMAIL>',
    pass: 'your-app-password' // ليس كلمة المرور العادية!
  }
});

// خطوات إنشاء App Password:
// 1. تفعيل 2FA في حساب Google
// 2. الذهاب إلى Google Account Settings
// 3. Security → App passwords
// 4. إنشاء كلمة مرور جديدة للتطبيق
```

#### خطأ: "Connection timeout"
```typescript
// إضافة timeout وإعادة المحاولة
const transporter = nodemailer.createTransporter({
  host: 'smtp.gmail.com',
  port: 587,
  secure: false,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
  connectionTimeout: 60000, // 60 ثانية
  greetingTimeout: 30000,   // 30 ثانية
  socketTimeout: 60000,     // 60 ثانية
});
```

### 5. أخطاء مراقبة النظام

#### خطأ: "Permission denied" عند قراءة معلومات النظام
```bash
# Linux: إعطاء صلاحيات للمستخدم
sudo usermod -a -G disk $USER
sudo usermod -a -G sys $USER

# إعادة تسجيل الدخول أو:
newgrp disk
newgrp sys
```

#### خطأ: "Command not found: sensors"
```bash
# تثبيت lm-sensors
# Ubuntu/Debian
sudo apt-get install lm-sensors

# CentOS/RHEL
sudo yum install lm_sensors

# تكوين المستشعرات
sudo sensors-detect
```

#### خطأ في قراءة معلومات الأقراص
```typescript
// إضافة معالجة أخطاء شاملة
async function getDiskInfo(): Promise<DiskInfo[]> {
  try {
    const disks = await si.diskLayout();
    return disks.map(disk => ({
      name: disk.device || 'Unknown',
      size: disk.size || 0,
      type: disk.type || 'Unknown',
      health: 'unknown' // سيتم تحديثه لاحقاً
    }));
  } catch (error) {
    console.error('خطأ في قراءة معلومات الأقراص:', error);
    return [{
      name: 'System Disk',
      size: 0,
      type: 'Unknown',
      health: 'unknown'
    }];
  }
}
```

### 6. أخطاء الواجهة والتصميم

#### خطأ: "Hydration failed" في Next.js
```typescript
// المشكلة: اختلاف بين Server-side و Client-side rendering
// الحل: استخدام useEffect للبيانات الديناميكية

// خطأ:
const currentTime = new Date().toLocaleString();

// صحيح:
const [currentTime, setCurrentTime] = useState('');

useEffect(() => {
  setCurrentTime(new Date().toLocaleString());
}, []);
```

#### خطأ: الخطوط العربية لا تظهر بشكل صحيح
```css
/* إضافة في globals.css */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap');

body {
  font-family: 'Noto Sans Arabic', sans-serif;
  direction: rtl;
}

/* أو استخدام خط محلي */
@font-face {
  font-family: 'Arabic Font';
  src: url('/fonts/arabic-font.woff2') format('woff2');
  font-display: swap;
}
```

### 7. أخطاء الأداء

#### بطء في تحميل البيانات
```typescript
// إضافة caching
const cache = new Map();
const CACHE_DURATION = 30000; // 30 ثانية

export async function getCachedData(key: string, fetcher: () => Promise<any>) {
  const cached = cache.get(key);
  
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  
  const data = await fetcher();
  cache.set(key, { data, timestamp: Date.now() });
  return data;
}

// الاستخدام:
const serverData = await getCachedData('servers', getServersData);
```

#### استهلاك ذاكرة مرتفع
```typescript
// تنظيف المؤقتات
useEffect(() => {
  const interval = setInterval(() => {
    fetchData();
  }, 30000);

  // تنظيف عند إلغاء المكون
  return () => clearInterval(interval);
}, []);

// تحديد حجم البيانات المحفوظة
const MAX_HISTORY_POINTS = 100;

function addDataPoint(newPoint: DataPoint) {
  setHistoryData(prev => {
    const updated = [...prev, newPoint];
    return updated.slice(-MAX_HISTORY_POINTS);
  });
}
```

## 🔍 أدوات التشخيص

### 1. فحص حالة النظام
```bash
# إنشاء سكريبت للفحص السريع
# check-system.sh

#!/bin/bash
echo "=== فحص حالة نظام مراقبة السيرفرات ==="

echo "1. فحص Node.js:"
node --version

echo "2. فحص npm:"
npm --version

echo "3. فحص المنفذ 3000:"
lsof -i :3000

echo "4. فحص مساحة القرص:"
df -h

echo "5. فحص الذاكرة:"
free -h

echo "6. فحص العمليات:"
ps aux | grep node

echo "=== انتهى الفحص ==="
```

### 2. سكريبت تشخيص الأخطاء
```javascript
// diagnostic.js
const os = require('os');
const fs = require('fs');
const path = require('path');

function runDiagnostics() {
  console.log('=== تشخيص نظام مراقبة السيرفرات ===\n');
  
  // معلومات النظام
  console.log('معلومات النظام:');
  console.log(`- النظام: ${os.type()} ${os.release()}`);
  console.log(`- المعمارية: ${os.arch()}`);
  console.log(`- الذاكرة الإجمالية: ${(os.totalmem() / 1024 / 1024 / 1024).toFixed(2)} GB`);
  console.log(`- الذاكرة المتاحة: ${(os.freemem() / 1024 / 1024 / 1024).toFixed(2)} GB\n`);
  
  // فحص الملفات المطلوبة
  console.log('فحص الملفات:');
  const requiredFiles = [
    'package.json',
    'src/app/page.tsx',
    'src/app/api/servers/route.ts',
    'data'
  ];
  
  requiredFiles.forEach(file => {
    const exists = fs.existsSync(file);
    console.log(`- ${file}: ${exists ? '✓ موجود' : '✗ مفقود'}`);
  });
  
  console.log('\n=== انتهى التشخيص ===');
}

runDiagnostics();
```

### 3. مراقبة الأداء
```typescript
// performance-monitor.ts
export class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();

  startTimer(label: string): () => void {
    const start = performance.now();
    
    return () => {
      const duration = performance.now() - start;
      this.recordMetric(label, duration);
      
      if (duration > 1000) { // أكثر من ثانية
        console.warn(`⚠️ عملية بطيئة: ${label} استغرقت ${duration.toFixed(2)}ms`);
      }
    };
  }

  private recordMetric(label: string, value: number) {
    if (!this.metrics.has(label)) {
      this.metrics.set(label, []);
    }
    
    const values = this.metrics.get(label)!;
    values.push(value);
    
    // الاحتفاظ بآخر 100 قياس فقط
    if (values.length > 100) {
      values.shift();
    }
  }

  getAverageTime(label: string): number {
    const values = this.metrics.get(label) || [];
    if (values.length === 0) return 0;
    
    return values.reduce((a, b) => a + b, 0) / values.length;
  }

  generateReport(): string {
    let report = '=== تقرير الأداء ===\n';
    
    for (const [label, values] of this.metrics) {
      const avg = this.getAverageTime(label);
      const max = Math.max(...values);
      const min = Math.min(...values);
      
      report += `${label}:\n`;
      report += `  متوسط: ${avg.toFixed(2)}ms\n`;
      report += `  أقصى: ${max.toFixed(2)}ms\n`;
      report += `  أدنى: ${min.toFixed(2)}ms\n\n`;
    }
    
    return report;
  }
}

// الاستخدام:
const monitor = new PerformanceMonitor();

async function fetchServers() {
  const endTimer = monitor.startTimer('fetch-servers');
  try {
    const response = await fetch('/api/servers');
    return await response.json();
  } finally {
    endTimer();
  }
}
```

## 📞 الحصول على المساعدة

### قنوات الدعم
1. **الوثائق الرسمية**: راجع ملف `تعليمات_المطور.md`
2. **GitHub Issues**: للإبلاغ عن الأخطاء
3. **Stack Overflow**: للأسئلة التقنية
4. **Discord/Slack**: للدعم المباشر

### معلومات مفيدة عند طلب المساعدة
- إصدار Node.js: `node --version`
- إصدار npm: `npm --version`
- نظام التشغيل
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة
- لقطة شاشة إن أمكن

### قالب الإبلاغ عن خطأ
```markdown
## وصف المشكلة
[وصف مختصر للمشكلة]

## خطوات إعادة الإنتاج
1. 
2. 
3. 

## السلوك المتوقع
[ما كان يجب أن يحدث]

## السلوك الفعلي
[ما حدث بالفعل]

## البيئة
- نظام التشغيل: 
- إصدار Node.js: 
- إصدار npm: 
- المتصفح: 

## رسالة الخطأ
```
[نسخ رسالة الخطأ هنا]
```

## ملفات إضافية
[إرفاق لقطات شاشة أو ملفات سجل إن أمكن]
```
