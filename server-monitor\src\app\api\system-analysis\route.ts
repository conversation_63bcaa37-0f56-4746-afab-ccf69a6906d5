import { NextRequest, NextResponse } from 'next/server';
import { systemAnalyzer } from '@/lib/systemAnalyzer';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');

    switch (type) {
      case 'errors':
        const errors = await systemAnalyzer.getSystemErrors();
        return NextResponse.json({
          success: true,
          data: errors
        });

      case 'temp-files':
        const tempFiles = await systemAnalyzer.getTempFiles();
        const totalTempSize = tempFiles.reduce((total, file) => total + file.size, 0);
        return NextResponse.json({
          success: true,
          data: {
            files: tempFiles,
            totalSize: totalTempSize,
            totalSizeFormatted: formatFileSize(totalTempSize),
            count: tempFiles.length
          }
        });

      case 'large-files':
        const minSizeMB = parseInt(searchParams.get('minSize') || '100');
        const largeFiles = await systemAnalyzer.getLargeFiles(minSizeMB);
        return NextResponse.json({
          success: true,
          data: {
            files: largeFiles,
            count: largeFiles.length,
            minSizeMB
          }
        });

      case 'full-analysis':
      default:
        const analysis = await systemAnalyzer.analyzeSystem();
        return NextResponse.json({
          success: true,
          data: analysis
        });
    }

  } catch (error) {
    console.error('خطأ في تحليل النظام:', error);
    return NextResponse.json({
      success: false,
      error: 'فشل في تحليل النظام'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, filePaths } = body;

    if (action === 'delete-temp-files' && Array.isArray(filePaths)) {
      // التحقق من صحة المسارات
      if (filePaths.length === 0) {
        return NextResponse.json({
          success: false,
          error: 'لا توجد ملفات للحذف'
        }, { status: 400 });
      }

      // التحقق من أن جميع المسارات صالحة
      const invalidPaths = filePaths.filter(path =>
        typeof path !== 'string' || path.trim() === ''
      );

      if (invalidPaths.length > 0) {
        return NextResponse.json({
          success: false,
          error: 'مسارات ملفات غير صحيحة'
        }, { status: 400 });
      }

      console.log(`بدء حذف ${filePaths.length} ملف...`);

      const result = await systemAnalyzer.deleteTempFiles(filePaths);

      console.log(`انتهى الحذف: ${result.success.length} نجح، ${result.failed.length} فشل`);

      return NextResponse.json({
        success: true,
        data: result,
        message: `تم حذف ${result.success.length} ملف بنجاح${result.failed.length > 0 ? `، فشل في حذف ${result.failed.length} ملف` : ''}`
      });
    }

    return NextResponse.json({
      success: false,
      error: 'إجراء غير صحيح'
    }, { status: 400 });

  } catch (error) {
    console.error('خطأ في معالجة الطلب:', error);

    const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';

    return NextResponse.json({
      success: false,
      error: 'فشل في معالجة الطلب',
      details: errorMessage
    }, { status: 500 });
  }
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
