'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowRight, Settings, Mail, Webhook, Save, TestTube } from 'lucide-react';

export default function SettingsPage() {
  const router = useRouter();
  const [settings, setSettings] = useState({
    email: {
      enabled: false,
      smtpHost: '',
      smtpPort: 587,
      smtpUser: '',
      smtpPass: '',
      fromEmail: '',
      toEmails: '',
    },
    webhook: {
      enabled: false,
      url: '',
    },
    monitoring: {
      interval: 30,
      cpuThreshold: 80,
      memoryThreshold: 85,
      diskThreshold: 90,
    },
  });
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await fetch('/api/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      }
    } catch (error) {
      console.error('خطأ في تحميل الإعدادات:', error);
    }
  };

  const saveSettings = async () => {
    setSaving(true);
    try {
      const response = await fetch('/api/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        alert('تم حفظ الإعدادات بنجاح');
      } else {
        alert('فشل في حفظ الإعدادات');
      }
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error);
      alert('خطأ في حفظ الإعدادات');
    } finally {
      setSaving(false);
    }
  };

  const testNotification = async () => {
    setTesting(true);
    try {
      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'info',
          title: 'اختبار النظام',
          message: 'هذا تنبيه تجريبي للتأكد من عمل النظام بشكل صحيح',
          serverName: 'نظام الاختبار',
          severity: 'low',
        }),
      });

      if (response.ok) {
        alert('تم إرسال التنبيه التجريبي بنجاح');
      } else {
        alert('فشل في إرسال التنبيه التجريبي');
      }
    } catch (error) {
      console.error('خطأ في إرسال التنبيه التجريبي:', error);
      alert('خطأ في إرسال التنبيه التجريبي');
    } finally {
      setTesting(false);
    }
  };

  const updateEmailSettings = (field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      email: {
        ...prev.email,
        [field]: value,
      },
    }));
  };

  const updateWebhookSettings = (field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      webhook: {
        ...prev.webhook,
        [field]: value,
      },
    }));
  };

  const updateMonitoringSettings = (field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      monitoring: {
        ...prev.monitoring,
        [field]: value,
      },
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center">
              <button
                onClick={() => router.push('/')}
                className="ml-4 p-2 text-gray-400 hover:text-gray-600"
              >
                <ArrowRight className="w-6 h-6" />
              </button>
              <Settings className="w-8 h-8 text-blue-600 ml-3" />
              <h1 className="text-2xl font-bold text-gray-900">إعدادات النظام</h1>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <button
                onClick={testNotification}
                disabled={testing}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
              >
                <TestTube className="w-4 h-4 ml-2" />
                {testing ? 'جاري الاختبار...' : 'اختبار التنبيه'}
              </button>
              <button
                onClick={saveSettings}
                disabled={saving}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                <Save className="w-4 h-4 ml-2" />
                {saving ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Email Settings */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <div className="flex items-center mb-6">
              <Mail className="w-6 h-6 text-blue-600 ml-3" />
              <h2 className="text-lg font-semibold text-gray-900">إعدادات الإيميل</h2>
            </div>

            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="emailEnabled"
                  checked={settings.email.enabled}
                  onChange={(e) => updateEmailSettings('enabled', e.target.checked)}
                  className="ml-2"
                />
                <label htmlFor="emailEnabled" className="text-sm font-medium text-gray-700">
                  تفعيل التنبيهات عبر الإيميل
                </label>
              </div>

              {settings.email.enabled && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      خادم SMTP
                    </label>
                    <input
                      type="text"
                      value={settings.email.smtpHost}
                      onChange={(e) => updateEmailSettings('smtpHost', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="smtp.gmail.com"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      منفذ SMTP
                    </label>
                    <input
                      type="number"
                      value={settings.email.smtpPort}
                      onChange={(e) => updateEmailSettings('smtpPort', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      اسم المستخدم
                    </label>
                    <input
                      type="email"
                      value={settings.email.smtpUser}
                      onChange={(e) => updateEmailSettings('smtpUser', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      كلمة المرور
                    </label>
                    <input
                      type="password"
                      value={settings.email.smtpPass}
                      onChange={(e) => updateEmailSettings('smtpPass', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      إيميل المرسل
                    </label>
                    <input
                      type="email"
                      value={settings.email.fromEmail}
                      onChange={(e) => updateEmailSettings('fromEmail', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      إيميلات المستقبلين (مفصولة بفاصلة)
                    </label>
                    <input
                      type="text"
                      value={settings.email.toEmails}
                      onChange={(e) => updateEmailSettings('toEmails', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="<EMAIL>, <EMAIL>"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Webhook Settings */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <div className="flex items-center mb-6">
              <Webhook className="w-6 h-6 text-green-600 ml-3" />
              <h2 className="text-lg font-semibold text-gray-900">إعدادات Webhook</h2>
            </div>

            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="webhookEnabled"
                  checked={settings.webhook.enabled}
                  onChange={(e) => updateWebhookSettings('enabled', e.target.checked)}
                  className="ml-2"
                />
                <label htmlFor="webhookEnabled" className="text-sm font-medium text-gray-700">
                  تفعيل Webhook للتنبيهات
                </label>
              </div>

              {settings.webhook.enabled && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    رابط Webhook
                  </label>
                  <input
                    type="url"
                    value={settings.webhook.url}
                    onChange={(e) => updateWebhookSettings('url', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="https://hooks.slack.com/services/..."
                  />
                </div>
              )}
            </div>
          </div>

          {/* Monitoring Settings */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <div className="flex items-center mb-6">
              <Settings className="w-6 h-6 text-purple-600 ml-3" />
              <h2 className="text-lg font-semibold text-gray-900">إعدادات المراقبة</h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  فترة التحديث (ثانية)
                </label>
                <input
                  type="number"
                  value={settings.monitoring.interval}
                  onChange={(e) => updateMonitoringSettings('interval', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="10"
                  max="300"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  حد تنبيه المعالج (%)
                </label>
                <input
                  type="number"
                  value={settings.monitoring.cpuThreshold}
                  onChange={(e) => updateMonitoringSettings('cpuThreshold', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="50"
                  max="95"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  حد تنبيه الذاكرة (%)
                </label>
                <input
                  type="number"
                  value={settings.monitoring.memoryThreshold}
                  onChange={(e) => updateMonitoringSettings('memoryThreshold', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="50"
                  max="95"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  حد تنبيه التخزين (%)
                </label>
                <input
                  type="number"
                  value={settings.monitoring.diskThreshold}
                  onChange={(e) => updateMonitoringSettings('diskThreshold', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="70"
                  max="98"
                />
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
