'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';

interface VirtualizedListProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number;
}

export default function VirtualizedList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5
}: VirtualizedListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  // حساب العناصر المرئية
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );

  const visibleItems = items.slice(startIndex, endIndex + 1);

  // حساب الارتفاع الإجمالي والإزاحة
  const totalHeight = items.length * itemHeight;
  const offsetY = startIndex * itemHeight;

  return (
    <div
      ref={containerRef}
      style={{ height: containerHeight, overflow: 'auto' }}
      onScroll={handleScroll}
      className="relative"
    >
      {/* المساحة الإجمالية */}
      <div style={{ height: totalHeight, position: 'relative' }}>
        {/* العناصر المرئية */}
        <div
          style={{
            transform: `translateY(${offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
          }}
        >
          {visibleItems.map((item, index) => (
            <div key={startIndex + index} style={{ height: itemHeight }}>
              {renderItem(item, startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Hook للتحميل المؤجل
export function useInfiniteScroll<T>(
  fetchMore: () => Promise<T[]>,
  hasMore: boolean,
  threshold = 100
) {
  const [loading, setLoading] = useState(false);
  const observerRef = useRef<IntersectionObserver>();
  const loadingRef = useRef<HTMLDivElement>(null);

  const lastElementRef = useCallback(
    (node: HTMLDivElement) => {
      if (loading) return;
      if (observerRef.current) observerRef.current.disconnect();
      
      observerRef.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasMore) {
            setLoading(true);
            fetchMore().finally(() => setLoading(false));
          }
        },
        { threshold: 0.1 }
      );
      
      if (node) observerRef.current.observe(node);
    },
    [loading, hasMore, fetchMore]
  );

  return { loading, lastElementRef };
}

// مكون محسن للقوائم الطويلة
interface OptimizedListProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  itemHeight?: number;
  maxHeight?: number;
  searchTerm?: string;
  onSearch?: (term: string) => void;
  loading?: boolean;
}

export function OptimizedList<T>({
  items,
  renderItem,
  itemHeight = 80,
  maxHeight = 400,
  searchTerm = '',
  onSearch,
  loading = false
}: OptimizedListProps<T>) {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = Math.max(10, Math.floor(maxHeight / itemHeight));
  
  const totalPages = Math.ceil(items.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedItems = items.slice(startIndex, startIndex + itemsPerPage);

  return (
    <div className="space-y-4">
      {/* شريط البحث */}
      {onSearch && (
        <div className="relative">
          <input
            type="text"
            placeholder="البحث..."
            value={searchTerm}
            onChange={(e) => onSearch(e.target.value)}
            className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
          />
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        </div>
      )}

      {/* القائمة */}
      <div style={{ maxHeight, overflow: 'auto' }} className="space-y-2">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
            <span className="mr-2 text-gray-600">جاري التحميل...</span>
          </div>
        ) : paginatedItems.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            لا توجد عناصر للعرض
          </div>
        ) : (
          paginatedItems.map((item, index) => (
            <div key={startIndex + index}>
              {renderItem(item, startIndex + index)}
            </div>
          ))
        )}
      </div>

      {/* التصفح */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center gap-2">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50"
          >
            السابق
          </button>
          
          <span className="px-3 py-1 text-sm text-gray-600">
            {currentPage} من {totalPages}
          </span>
          
          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50"
          >
            التالي
          </button>
        </div>
      )}
    </div>
  );
}

// Hook للتحسين التلقائي للأداء
export function usePerformanceOptimization() {
  const [isSlowDevice, setIsSlowDevice] = useState(false);
  
  useEffect(() => {
    // فحص أداء الجهاز
    const checkPerformance = () => {
      const start = performance.now();
      
      // محاكاة عملية حسابية
      for (let i = 0; i < 100000; i++) {
        Math.random();
      }
      
      const duration = performance.now() - start;
      setIsSlowDevice(duration > 10); // إذا استغرق أكثر من 10ms
    };
    
    checkPerformance();
  }, []);
  
  return {
    isSlowDevice,
    recommendedItemsPerPage: isSlowDevice ? 10 : 25,
    recommendedRefreshInterval: isSlowDevice ? 10000 : 5000
  };
}
