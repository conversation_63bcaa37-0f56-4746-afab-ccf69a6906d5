import { NextResponse } from 'next/server';
import { realTimeAlerts } from '@/lib/realTimeAlerts';

export async function GET() {
  try {
    const alerts = realTimeAlerts.getAlerts();

    // تحويل التنبيهات إلى تنسيق API
    const formattedAlerts = alerts.map(alert => ({
      id: alert.id,
      type: alert.type,
      title: alert.title,
      message: alert.message,
      serverName: alert.serverName,
      serverId: alert.serverId,
      severity: alert.severity,
      timestamp: alert.timestamp.toISOString(),
      acknowledged: alert.acknowledged,
      resolved: alert.resolved,
      category: alert.category,
      threshold: alert.threshold
    }));

    return NextResponse.json(formattedAlerts);
  } catch (error) {
    console.error('خطأ في جلب التنبيهات:', error);
    return NextResponse.json(
      { error: 'فشل في جلب التنبيهات' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { action, alertId } = body;

    switch (action) {
      case 'acknowledge':
        if (!alertId) {
          return NextResponse.json(
            { error: 'معرف التنبيه مطلوب' },
            { status: 400 }
          );
        }

        const acknowledged = realTimeAlerts.acknowledgeAlert(alertId);
        if (!acknowledged) {
          return NextResponse.json(
            { error: 'التنبيه غير موجود' },
            { status: 404 }
          );
        }

        return NextResponse.json({
          success: true,
          message: 'تم تأكيد التنبيه'
        });

      case 'resolve':
        if (!alertId) {
          return NextResponse.json(
            { error: 'معرف التنبيه مطلوب' },
            { status: 400 }
          );
        }

        const resolved = realTimeAlerts.resolveAlert(alertId);
        if (!resolved) {
          return NextResponse.json(
            { error: 'التنبيه غير موجود' },
            { status: 404 }
          );
        }

        return NextResponse.json({
          success: true,
          message: 'تم حل التنبيه'
        });

      case 'get_active':
        const activeAlerts = realTimeAlerts.getActiveAlerts();
        return NextResponse.json({
          success: true,
          alerts: activeAlerts.map(alert => ({
            id: alert.id,
            type: alert.type,
            title: alert.title,
            message: alert.message,
            serverName: alert.serverName,
            serverId: alert.serverId,
            severity: alert.severity,
            timestamp: alert.timestamp.toISOString(),
            acknowledged: alert.acknowledged,
            category: alert.category,
            threshold: alert.threshold
          })),
          count: activeAlerts.length
        });

      default:
        return NextResponse.json(
          { error: 'إجراء غير مدعوم' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('خطأ في معالجة التنبيهات:', error);
    return NextResponse.json(
      { error: 'فشل في معالجة التنبيهات' },
      { status: 500 }
    );
  }
}
