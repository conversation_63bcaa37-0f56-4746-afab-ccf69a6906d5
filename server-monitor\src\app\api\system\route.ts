import { NextResponse } from 'next/server';
import { getSystemInfo, checkSystemHealth } from '@/lib/systemMonitor';

export async function GET() {
  try {
    const systemInfo = await getSystemInfo();
    const healthCheck = await checkSystemHealth();
    
    return NextResponse.json({
      ...systemInfo,
      health: healthCheck,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('خطأ في جلب معلومات النظام:', error);
    return NextResponse.json(
      { error: 'فشل في جلب معلومات النظام' },
      { status: 500 }
    );
  }
}
