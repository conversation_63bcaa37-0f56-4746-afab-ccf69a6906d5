'use client';

import { Server, HardDrive, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Circle } from 'lucide-react';

interface DashboardStatsProps {
  servers: Array<{
    id: string;
    status: 'online' | 'offline' | 'warning';
    cpu: number;
    memory: number;
    diskUsage: number;
    disks: Array<{
      health: 'good' | 'warning' | 'critical';
    }>;
  }>;
}

export default function DashboardStats({ servers }: DashboardStatsProps) {
  const totalServers = servers.length;
  const onlineServers = servers.filter(s => s.status === 'online').length;
  const warningServers = servers.filter(s => s.status === 'warning').length;
  const offlineServers = servers.filter(s => s.status === 'offline').length;

  const totalDisks = servers.reduce((acc, server) => acc + server.disks.length, 0);
  const healthyDisks = servers.reduce((acc, server) => 
    acc + server.disks.filter(disk => disk.health === 'good').length, 0);
  const warningDisks = servers.reduce((acc, server) => 
    acc + server.disks.filter(disk => disk.health === 'warning').length, 0);
  const criticalDisks = servers.reduce((acc, server) => 
    acc + server.disks.filter(disk => disk.health === 'critical').length, 0);

  const avgCpu = totalServers > 0 ? 
    Math.round(servers.reduce((acc, server) => acc + server.cpu, 0) / totalServers) : 0;
  const avgMemory = totalServers > 0 ? 
    Math.round(servers.reduce((acc, server) => acc + server.memory, 0) / totalServers) : 0;
  const avgDisk = totalServers > 0 ? 
    Math.round(servers.reduce((acc, server) => acc + server.diskUsage, 0) / totalServers) : 0;

  const stats = [
    {
      title: 'إجمالي السيرفرات',
      value: totalServers,
      icon: Server,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      details: [
        { label: 'متصل', value: onlineServers, color: 'text-green-600' },
        { label: 'تحذير', value: warningServers, color: 'text-yellow-600' },
        { label: 'غير متصل', value: offlineServers, color: 'text-red-600' },
      ]
    },
    {
      title: 'إجمالي الهاردات',
      value: totalDisks,
      icon: HardDrive,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      details: [
        { label: 'جيد', value: healthyDisks, color: 'text-green-600' },
        { label: 'تحذير', value: warningDisks, color: 'text-yellow-600' },
        { label: 'خطر', value: criticalDisks, color: 'text-red-600' },
      ]
    },
    {
      title: 'متوسط استخدام المعالج',
      value: `${avgCpu}%`,
      icon: CheckCircle,
      color: avgCpu > 80 ? 'text-red-600' : avgCpu > 60 ? 'text-yellow-600' : 'text-green-600',
      bgColor: avgCpu > 80 ? 'bg-red-100' : avgCpu > 60 ? 'bg-yellow-100' : 'bg-green-100',
    },
    {
      title: 'متوسط استخدام الذاكرة',
      value: `${avgMemory}%`,
      icon: CheckCircle,
      color: avgMemory > 80 ? 'text-red-600' : avgMemory > 60 ? 'text-yellow-600' : 'text-green-600',
      bgColor: avgMemory > 80 ? 'bg-red-100' : avgMemory > 60 ? 'bg-yellow-100' : 'bg-green-100',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {stats.map((stat, index) => (
        <div key={index} className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
          <div className="flex items-center">
            <div className={`p-3 rounded-lg ${stat.bgColor}`}>
              <stat.icon className={`w-6 h-6 ${stat.color}`} />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">{stat.title}</p>
              <p className={`text-2xl font-bold ${stat.color}`}>{stat.value}</p>
            </div>
          </div>
          
          {stat.details && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-3 gap-2 text-xs">
                {stat.details.map((detail, idx) => (
                  <div key={idx} className="text-center">
                    <div className={`font-semibold ${detail.color}`}>{detail.value}</div>
                    <div className="text-gray-500">{detail.label}</div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
