/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/system-analysis/page"],{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/Icon.js":
/*!****************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/Icon.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Icon)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaultAttributes.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js\");\n/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js\");\n/**\n * @license lucide-react v0.526.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\nconst Icon = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(_c = (param, ref)=>{\n    let { color = \"currentColor\", size = 24, strokeWidth = 2, absoluteStrokeWidth, className = \"\", children, iconNode, ...rest } = param;\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"svg\", {\n        ref,\n        ..._defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n        className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)(\"lucide\", className),\n        ...!children && !(0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.hasA11yProp)(rest) && {\n            \"aria-hidden\": \"true\"\n        },\n        ...rest\n    }, [\n        ...iconNode.map((param)=>{\n            let [tag, attrs] = param;\n            return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(tag, attrs);\n        }),\n        ...Array.isArray(children) ? children : [\n            children\n        ]\n    ]);\n});\n_c1 = Icon;\n //# sourceMappingURL=Icon.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Icon$forwardRef\");\n$RefreshReg$(_c1, \"Icon\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/Icon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/createLucideIcon.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createLucideIcon)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js\");\n/* harmony import */ var _Icon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Icon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/Icon.js\");\n/**\n * @license lucide-react v0.526.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\nconst createLucideIcon = (iconName, iconNode)=>{\n    const Component = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((param, ref)=>{\n        let { className, ...props } = param;\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Icon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            ref,\n            iconNode,\n            className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)(\"lucide-\".concat((0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toKebabCase)((0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toPascalCase)(iconName))), \"lucide-\".concat(iconName), className),\n            ...props\n        });\n    });\n    Component.displayName = (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toPascalCase)(iconName);\n    return Component;\n};\n //# sourceMappingURL=createLucideIcon.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vY3JlYXRlTHVjaWRlSWNvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBV00sdUJBQW1CLEdBQUMsVUFBa0IsUUFBdUI7SUFDakUsTUFBTSxDQUFZLDJFQUF1QyxRQUEwQjtZQUF6QixFQUFFLENBQVcsV0FBRyxRQUFTOzZCQUNqRixvREFBYSxDQUFDLGdEQUFNO1lBQ2xCO1lBQ0E7WUFDQSxTQUFXLHFFQUNDLFVBQW1DLE9BQW5DLGtFQUFZLGtFQUFhLEVBQVEsUUFBQyxDQUFDLEdBQzdDLFFBQVUsRUFBUSxPQUFSLFFBQVEsR0FDbEI7WUFFRixDQUFHO1FBQ0o7O0lBR08sd0JBQWMsa0VBQVksQ0FBQyxRQUFRO0lBRXRDO0FBQ1QiLCJzb3VyY2VzIjpbIkQ6XFxtb25lZXJcXGZvbGRlcnNcXGNvcHkgd29ya2luZ1xcc3JjXFxjcmVhdGVMdWNpZGVJY29uLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUVsZW1lbnQsIGZvcndhcmRSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBtZXJnZUNsYXNzZXMsIHRvS2ViYWJDYXNlLCB0b1Bhc2NhbENhc2UgfSBmcm9tICdAbHVjaWRlL3NoYXJlZCc7XG5pbXBvcnQgeyBJY29uTm9kZSwgTHVjaWRlUHJvcHMgfSBmcm9tICcuL3R5cGVzJztcbmltcG9ydCBJY29uIGZyb20gJy4vSWNvbic7XG5cbi8qKlxuICogQ3JlYXRlIGEgTHVjaWRlIGljb24gY29tcG9uZW50XG4gKiBAcGFyYW0ge3N0cmluZ30gaWNvbk5hbWVcbiAqIEBwYXJhbSB7YXJyYXl9IGljb25Ob2RlXG4gKiBAcmV0dXJucyB7Rm9yd2FyZFJlZkV4b3RpY0NvbXBvbmVudH0gTHVjaWRlSWNvblxuICovXG5jb25zdCBjcmVhdGVMdWNpZGVJY29uID0gKGljb25OYW1lOiBzdHJpbmcsIGljb25Ob2RlOiBJY29uTm9kZSkgPT4ge1xuICBjb25zdCBDb21wb25lbnQgPSBmb3J3YXJkUmVmPFNWR1NWR0VsZW1lbnQsIEx1Y2lkZVByb3BzPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT5cbiAgICBjcmVhdGVFbGVtZW50KEljb24sIHtcbiAgICAgIHJlZixcbiAgICAgIGljb25Ob2RlLFxuICAgICAgY2xhc3NOYW1lOiBtZXJnZUNsYXNzZXMoXG4gICAgICAgIGBsdWNpZGUtJHt0b0tlYmFiQ2FzZSh0b1Bhc2NhbENhc2UoaWNvbk5hbWUpKX1gLFxuICAgICAgICBgbHVjaWRlLSR7aWNvbk5hbWV9YCxcbiAgICAgICAgY2xhc3NOYW1lLFxuICAgICAgKSxcbiAgICAgIC4uLnByb3BzLFxuICAgIH0pLFxuICApO1xuXG4gIENvbXBvbmVudC5kaXNwbGF5TmFtZSA9IHRvUGFzY2FsQ2FzZShpY29uTmFtZSk7XG5cbiAgcmV0dXJuIENvbXBvbmVudDtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGNyZWF0ZUx1Y2lkZUljb247XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/defaultAttributes.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defaultAttributes)\n/* harmony export */ });\n/**\n * @license lucide-react v0.526.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ var defaultAttributes = {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 24,\n    height: 24,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: 2,\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n};\n //# sourceMappingURL=defaultAttributes.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vZGVmYXVsdEF0dHJpYnV0ZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0lBQUEsQ0FBZTtJQUNiLEtBQU87SUFDUCxLQUFPO0lBQ1AsTUFBUTtJQUNSLE9BQVM7SUFDVCxJQUFNO0lBQ04sTUFBUTtJQUNSLFdBQWE7SUFDYixhQUFlO0lBQ2YsY0FBZ0I7QUFDbEIiLCJzb3VyY2VzIjpbIkQ6XFxtb25lZXJcXGZvbGRlcnNcXGNvcHkgd29ya2luZ1xcc3JjXFxkZWZhdWx0QXR0cmlidXRlcy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XG4gIHhtbG5zOiAnaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnLFxuICB3aWR0aDogMjQsXG4gIGhlaWdodDogMjQsXG4gIHZpZXdCb3g6ICcwIDAgMjQgMjQnLFxuICBmaWxsOiAnbm9uZScsXG4gIHN0cm9rZTogJ2N1cnJlbnRDb2xvcicsXG4gIHN0cm9rZVdpZHRoOiAyLFxuICBzdHJva2VMaW5lY2FwOiAncm91bmQnLFxuICBzdHJva2VMaW5lam9pbjogJ3JvdW5kJyxcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chart-column.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChartColumn)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.526.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M3 3v16a2 2 0 0 0 2 2h16\",\n            key: \"c24i48\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18 17V9\",\n            key: \"2bz60n\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M13 17V5\",\n            key: \"1frdt8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 17v-3\",\n            key: \"17ska0\"\n        }\n    ]\n];\nconst ChartColumn = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chart-column\", __iconNode);\n //# sourceMappingURL=chart-column.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-alert.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ CircleAlert)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.526.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"8\",\n            y2: \"12\",\n            key: \"1pkeuh\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12.01\",\n            y1: \"16\",\n            y2: \"16\",\n            key: \"4dfq90\"\n        }\n    ]\n];\nconst CircleAlert = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"circle-alert\", __iconNode);\n //# sourceMappingURL=circle-alert.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/clock.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Clock)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.526.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 6v6l4 2\",\n            key: \"mmk7yg\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ]\n];\nconst Clock = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"clock\", __iconNode);\n //# sourceMappingURL=clock.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/file.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ File)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.526.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n            key: \"1rqfz7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n            key: \"tnqrlb\"\n        }\n    ]\n];\nconst File = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"file\", __iconNode);\n //# sourceMappingURL=file.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/folder-open.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ FolderOpen)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.526.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2\",\n            key: \"usdka0\"\n        }\n    ]\n];\nconst FolderOpen = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"folder-open\", __iconNode);\n //# sourceMappingURL=folder-open.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvZm9sZGVyLW9wZW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR08sTUFBTSxVQUF1QjtJQUNsQztRQUNFO1FBQ0E7WUFDRSxDQUFHO1lBQ0gsR0FBSztRQUFBO0tBQ1A7Q0FFSjtBQWFNLGlCQUFhLGtFQUFpQixnQkFBZSxDQUFVIiwic291cmNlcyI6WyJEOlxcbW9uZWVyXFxmb2xkZXJzXFxzcmNcXGljb25zXFxmb2xkZXItb3Blbi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbXG4gIFtcbiAgICAncGF0aCcsXG4gICAge1xuICAgICAgZDogJ202IDE0IDEuNS0yLjlBMiAyIDAgMCAxIDkuMjQgMTBIMjBhMiAyIDAgMCAxIDEuOTQgMi41bC0xLjU0IDZhMiAyIDAgMCAxLTEuOTUgMS41SDRhMiAyIDAgMCAxLTItMlY1YTIgMiAwIDAgMSAyLTJoMy45YTIgMiAwIDAgMSAxLjY5LjlsLjgxIDEuMmEyIDIgMCAwIDAgMS42Ny45SDE4YTIgMiAwIDAgMSAyIDJ2MicsXG4gICAgICBrZXk6ICd1c2RrYTAnLFxuICAgIH0sXG4gIF0sXG5dO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgRm9sZGVyT3BlblxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0TmlBeE5DQXhMalV0TWk0NVFUSWdNaUF3SURBZ01TQTVMakkwSURFd1NESXdZVElnTWlBd0lEQWdNU0F4TGprMElESXVOV3d0TVM0MU5DQTJZVElnTWlBd0lEQWdNUzB4TGprMUlERXVOVWcwWVRJZ01pQXdJREFnTVMweUxUSldOV0V5SURJZ01DQXdJREVnTWkweWFETXVPV0V5SURJZ01DQXdJREVnTVM0Mk9TNDViQzQ0TVNBeExqSmhNaUF5SURBZ01DQXdJREV1TmpjdU9VZ3hPR0V5SURJZ01DQXdJREVnTWlBeWRqSWlJQzgrQ2p3dmMzWm5QZ289KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9mb2xkZXItb3BlblxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IEZvbGRlck9wZW4gPSBjcmVhdGVMdWNpZGVJY29uKCdmb2xkZXItb3BlbicsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgZGVmYXVsdCBGb2xkZXJPcGVuO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/funnel.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Funnel)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.526.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z\",\n            key: \"sc7q7i\"\n        }\n    ]\n];\nconst Funnel = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"funnel\", __iconNode);\n //# sourceMappingURL=funnel.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvZnVubmVsLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUdPLE1BQU0sVUFBdUI7SUFDbEM7UUFDRTtRQUNBO1lBQ0UsQ0FBRztZQUNILEdBQUs7UUFBQTtLQUNQO0NBRUo7QUFhTSxhQUFTLGtFQUFpQixXQUFVLENBQVUiLCJzb3VyY2VzIjpbIkQ6XFxtb25lZXJcXGZvbGRlcnNcXHNyY1xcaWNvbnNcXGZ1bm5lbC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbXG4gIFtcbiAgICAncGF0aCcsXG4gICAge1xuICAgICAgZDogJ00xMCAyMGExIDEgMCAwIDAgLjU1My44OTVsMiAxQTEgMSAwIDAgMCAxNCAyMXYtN2EyIDIgMCAwIDEgLjUxNy0xLjM0MUwyMS43NCA0LjY3QTEgMSAwIDAgMCAyMSAzSDNhMSAxIDAgMCAwLS43NDIgMS42N2w3LjIyNSA3Ljk4OUEyIDIgMCAwIDEgMTAgMTR6JyxcbiAgICAgIGtleTogJ3NjN3E3aScsXG4gICAgfSxcbiAgXSxcbl07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBGdW5uZWxcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKTk1UQWdNakJoTVNBeElEQWdNQ0F3SUM0MU5UTXVPRGsxYkRJZ01VRXhJREVnTUNBd0lEQWdNVFFnTWpGMkxUZGhNaUF5SURBZ01DQXhJQzQxTVRjdE1TNHpOREZNTWpFdU56UWdOQzQyTjBFeElERWdNQ0F3SURBZ01qRWdNMGd6WVRFZ01TQXdJREFnTUMwdU56UXlJREV1Tmpkc055NHlNalVnTnk0NU9EbEJNaUF5SURBZ01DQXhJREV3SURFMGVpSWdMejRLUEM5emRtYytDZz09KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9mdW5uZWxcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBGdW5uZWwgPSBjcmVhdGVMdWNpZGVJY29uKCdmdW5uZWwnLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgRnVubmVsO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/hard-drive.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ HardDrive)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.526.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"line\",\n        {\n            x1: \"22\",\n            x2: \"2\",\n            y1: \"12\",\n            y2: \"12\",\n            key: \"1y58io\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z\",\n            key: \"oot6mr\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"6\",\n            x2: \"6.01\",\n            y1: \"16\",\n            y2: \"16\",\n            key: \"sgf278\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"10\",\n            x2: \"10.01\",\n            y1: \"16\",\n            y2: \"16\",\n            key: \"1l4acy\"\n        }\n    ]\n];\nconst HardDrive = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"hard-drive\", __iconNode);\n //# sourceMappingURL=hard-drive.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/loader-circle.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ LoaderCircle)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.526.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21 12a9 9 0 1 1-6.219-8.56\",\n            key: \"13zald\"\n        }\n    ]\n];\nconst LoaderCircle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"loader-circle\", __iconNode);\n //# sourceMappingURL=loader-circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbG9hZGVyLWNpcmNsZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHYSxtQkFBdUI7SUFBQztRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsNkJBQStCO1lBQUEsSUFBSyxTQUFTO1FBQUEsQ0FBQztLQUFDO0NBQUE7QUFhNUYsbUJBQWUsa0VBQWlCLGtCQUFpQixDQUFVIiwic291cmNlcyI6WyJEOlxcbW9uZWVyXFxmb2xkZXJzXFxzcmNcXGljb25zXFxsb2FkZXItY2lyY2xlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuaW1wb3J0IHsgSWNvbk5vZGUgfSBmcm9tICcuLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBfX2ljb25Ob2RlOiBJY29uTm9kZSA9IFtbJ3BhdGgnLCB7IGQ6ICdNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYnLCBrZXk6ICcxM3phbGQnIH1dXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIExvYWRlckNpcmNsZVxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0pOTWpFZ01USmhPU0E1SURBZ01TQXhMVFl1TWpFNUxUZ3VOVFlpSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvbG9hZGVyLWNpcmNsZVxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IExvYWRlckNpcmNsZSA9IGNyZWF0ZUx1Y2lkZUljb24oJ2xvYWRlci1jaXJjbGUnLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgTG9hZGVyQ2lyY2xlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/refresh-cw.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ RefreshCw)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.526.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8\",\n            key: \"v9h5vc\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 3v5h-5\",\n            key: \"1q7to0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16\",\n            key: \"3uifl3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 16H3v5\",\n            key: \"1cv678\"\n        }\n    ]\n];\nconst RefreshCw = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"refresh-cw\", __iconNode);\n //# sourceMappingURL=refresh-cw.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/search.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Search)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.526.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m21 21-4.34-4.34\",\n            key: \"14j7rj\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"11\",\n            cy: \"11\",\n            r: \"8\",\n            key: \"4ej97u\"\n        }\n    ]\n];\nconst Search = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"search\", __iconNode);\n //# sourceMappingURL=search.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/server.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Server)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.526.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"20\",\n            height: \"8\",\n            x: \"2\",\n            y: \"2\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"ngkwjq\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            width: \"20\",\n            height: \"8\",\n            x: \"2\",\n            y: \"14\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"iecqi9\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"6\",\n            x2: \"6.01\",\n            y1: \"6\",\n            y2: \"6\",\n            key: \"16zg32\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"6\",\n            x2: \"6.01\",\n            y1: \"18\",\n            y2: \"18\",\n            key: \"nzw8ys\"\n        }\n    ]\n];\nconst Server = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"server\", __iconNode);\n //# sourceMappingURL=server.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js":
/*!**********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/square-check-big.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ SquareCheckBig)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.526.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21 10.656V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.344\",\n            key: \"2acyp4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 11 3 3L22 4\",\n            key: \"1pflzl\"\n        }\n    ]\n];\nconst SquareCheckBig = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"square-check-big\", __iconNode);\n //# sourceMappingURL=square-check-big.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/square.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Square)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.526.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"18\",\n            height: \"18\",\n            x: \"3\",\n            y: \"3\",\n            rx: \"2\",\n            key: \"afitv7\"\n        }\n    ]\n];\nconst Square = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"square\", __iconNode);\n //# sourceMappingURL=square.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trash-2.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Trash2)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.526.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M10 11v6\",\n            key: \"nco0om\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 11v6\",\n            key: \"outv1u\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6\",\n            key: \"miytrc\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 6h18\",\n            key: \"d0wm0j\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2\",\n            key: \"e791ji\"\n        }\n    ]\n];\nconst Trash2 = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"trash-2\", __iconNode);\n //# sourceMappingURL=trash-2.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js":
/*!********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/triangle-alert.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ TriangleAlert)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.526.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3\",\n            key: \"wmoenq\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 9v4\",\n            key: \"juzpu7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 17h.01\",\n            key: \"p32p05\"\n        }\n    ]\n];\nconst TriangleAlert = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"triangle-alert\", __iconNode);\n //# sourceMappingURL=triangle-alert.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvdHJpYW5nbGUtYWxlcnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR08sTUFBTSxVQUF1QjtJQUNsQztRQUNFO1FBQ0E7WUFDRSxDQUFHO1lBQ0gsR0FBSztRQUFBO0tBRVQ7SUFDQTtRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBVztZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7SUFDeEM7UUFBQyxDQUFRO1FBQUEsQ0FBRTtZQUFBLEVBQUcsYUFBYztZQUFBLElBQUs7UUFBVTtLQUFBO0NBQzdDO0FBYU0sb0JBQWdCLGtFQUFpQixtQkFBa0IsQ0FBVSIsInNvdXJjZXMiOlsiRDpcXG1vbmVlclxcZm9sZGVyc1xcc3JjXFxpY29uc1xcdHJpYW5nbGUtYWxlcnQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5pbXBvcnQgeyBJY29uTm9kZSB9IGZyb20gJy4uL3R5cGVzJztcblxuZXhwb3J0IGNvbnN0IF9faWNvbk5vZGU6IEljb25Ob2RlID0gW1xuICBbXG4gICAgJ3BhdGgnLFxuICAgIHtcbiAgICAgIGQ6ICdtMjEuNzMgMTgtOC0xNGEyIDIgMCAwIDAtMy40OCAwbC04IDE0QTIgMiAwIDAgMCA0IDIxaDE2YTIgMiAwIDAgMCAxLjczLTMnLFxuICAgICAga2V5OiAnd21vZW5xJyxcbiAgICB9LFxuICBdLFxuICBbJ3BhdGgnLCB7IGQ6ICdNMTIgOXY0Jywga2V5OiAnanV6cHU3JyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTEyIDE3aC4wMScsIGtleTogJ3AzMnAwNScgfV0sXG5dO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgVHJpYW5nbGVBbGVydFxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0TWpFdU56TWdNVGd0T0MweE5HRXlJRElnTUNBd0lEQXRNeTQwT0NBd2JDMDRJREUwUVRJZ01pQXdJREFnTUNBMElESXhhREUyWVRJZ01pQXdJREFnTUNBeExqY3pMVE1pSUM4K0NpQWdQSEJoZEdnZ1pEMGlUVEV5SURsMk5DSWdMejRLSUNBOGNHRjBhQ0JrUFNKTk1USWdNVGRvTGpBeElpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL3RyaWFuZ2xlLWFsZXJ0XG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgVHJpYW5nbGVBbGVydCA9IGNyZWF0ZUx1Y2lkZUljb24oJ3RyaWFuZ2xlLWFsZXJ0JywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IFRyaWFuZ2xlQWxlcnQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/shared/src/utils.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasA11yProp: () => (/* binding */ hasA11yProp),\n/* harmony export */   mergeClasses: () => (/* binding */ mergeClasses),\n/* harmony export */   toCamelCase: () => (/* binding */ toCamelCase),\n/* harmony export */   toKebabCase: () => (/* binding */ toKebabCase),\n/* harmony export */   toPascalCase: () => (/* binding */ toPascalCase)\n/* harmony export */ });\n/**\n * @license lucide-react v0.526.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ const toKebabCase = (string)=>string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst toCamelCase = (string)=>string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2)=>p2 ? p2.toUpperCase() : p1.toLowerCase());\nconst toPascalCase = (string)=>{\n    const camelCase = toCamelCase(string);\n    return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);\n};\nconst mergeClasses = function() {\n    for(var _len = arguments.length, classes = new Array(_len), _key = 0; _key < _len; _key++){\n        classes[_key] = arguments[_key];\n    }\n    return classes.filter((className, index, array)=>{\n        return Boolean(className) && className.trim() !== \"\" && array.indexOf(className) === index;\n    }).join(\" \").trim();\n};\nconst hasA11yProp = (props)=>{\n    for(const prop in props){\n        if (prop.startsWith(\"aria-\") || prop === \"role\" || prop === \"title\") {\n            return true;\n        }\n    }\n};\n //# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vc2hhcmVkL3NyYy91dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0lBUWEsb0JBQWMsQ0FBQyxNQUMxQixVQUFPLFFBQVEsQ0FBc0Isc0JBQU8sUUFBRSxDQUFZO0FBUS9DLGtCQUFjLEdBQW1CLE9BQzVDLFFBQU8sU0FBUSx5QkFBeUIsQ0FBQyxPQUFPLENBQUksTUFDbEQsS0FBSyxJQUFHLGNBQWdCLE1BQUcsQ0FBWTtBQVM5QixxQkFBZSxDQUFtQixNQUFnQztJQUN2RSxrQkFBWSxZQUFZLE1BQU07SUFFNUIsaUJBQVUsT0FBTyxDQUFDLEVBQUUsYUFBZ0IsYUFBVSxNQUFNLENBQUM7QUFDL0Q7QUFRYSxtQkFBZSxJQUEyQzs7UUFBQSxPQUNyRTs7V0FBQSxRQUNHLE9BQU8sQ0FBQyxXQUFXLE9BQU8sS0FBVTtRQUVqQyxlQUFRLFNBQVMsRUFDaEIsWUFBcUIsWUFBVyxDQUNqQyxXQUFNLE9BQVEsRUFBUyxTQUFNO0lBRWpDLENBQUMsQ0FDQSxNQUFLLENBQUcsSUFDUixDQUFLOztBQVFHLG9CQUFjLENBQUMsS0FBK0I7SUFDekQsVUFBVyxRQUFRLEtBQU87UUFDeEIsQ0FBSSxRQUFLLFVBQVcsRUFBTyxRQUFLLE9BQVMsZUFBVSxTQUFTLE9BQVM7WUFDNUQ7UUFBQTtJQUNUO0FBRUoiLCJzb3VyY2VzIjpbIkQ6XFxzaGFyZWRcXHNyY1xcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ2FtZWxUb1Bhc2NhbCB9IGZyb20gJy4vdXRpbGl0eS10eXBlcyc7XG5cbi8qKlxuICogQ29udmVydHMgc3RyaW5nIHRvIGtlYmFiIGNhc2VcbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gc3RyaW5nXG4gKiBAcmV0dXJucyB7c3RyaW5nfSBBIGtlYmFiaXplZCBzdHJpbmdcbiAqL1xuZXhwb3J0IGNvbnN0IHRvS2ViYWJDYXNlID0gKHN0cmluZzogc3RyaW5nKSA9PlxuICBzdHJpbmcucmVwbGFjZSgvKFthLXowLTldKShbQS1aXSkvZywgJyQxLSQyJykudG9Mb3dlckNhc2UoKTtcblxuLyoqXG4gKiBDb252ZXJ0cyBzdHJpbmcgdG8gY2FtZWwgY2FzZVxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBzdHJpbmdcbiAqIEByZXR1cm5zIHtzdHJpbmd9IEEgY2FtZWxpemVkIHN0cmluZ1xuICovXG5leHBvcnQgY29uc3QgdG9DYW1lbENhc2UgPSA8VCBleHRlbmRzIHN0cmluZz4oc3RyaW5nOiBUKSA9PlxuICBzdHJpbmcucmVwbGFjZSgvXihbQS1aXSl8W1xccy1fXSsoXFx3KS9nLCAobWF0Y2gsIHAxLCBwMikgPT5cbiAgICBwMiA/IHAyLnRvVXBwZXJDYXNlKCkgOiBwMS50b0xvd2VyQ2FzZSgpLFxuICApO1xuXG4vKipcbiAqIENvbnZlcnRzIHN0cmluZyB0byBwYXNjYWwgY2FzZVxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBzdHJpbmdcbiAqIEByZXR1cm5zIHtzdHJpbmd9IEEgcGFzY2FsaXplZCBzdHJpbmdcbiAqL1xuZXhwb3J0IGNvbnN0IHRvUGFzY2FsQ2FzZSA9IDxUIGV4dGVuZHMgc3RyaW5nPihzdHJpbmc6IFQpOiBDYW1lbFRvUGFzY2FsPFQ+ID0+IHtcbiAgY29uc3QgY2FtZWxDYXNlID0gdG9DYW1lbENhc2Uoc3RyaW5nKTtcblxuICByZXR1cm4gKGNhbWVsQ2FzZS5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIGNhbWVsQ2FzZS5zbGljZSgxKSkgYXMgQ2FtZWxUb1Bhc2NhbDxUPjtcbn07XG5cbi8qKlxuICogTWVyZ2VzIGNsYXNzZXMgaW50byBhIHNpbmdsZSBzdHJpbmdcbiAqXG4gKiBAcGFyYW0ge2FycmF5fSBjbGFzc2VzXG4gKiBAcmV0dXJucyB7c3RyaW5nfSBBIHN0cmluZyBvZiBjbGFzc2VzXG4gKi9cbmV4cG9ydCBjb25zdCBtZXJnZUNsYXNzZXMgPSA8Q2xhc3NUeXBlID0gc3RyaW5nIHwgdW5kZWZpbmVkIHwgbnVsbD4oLi4uY2xhc3NlczogQ2xhc3NUeXBlW10pID0+XG4gIGNsYXNzZXNcbiAgICAuZmlsdGVyKChjbGFzc05hbWUsIGluZGV4LCBhcnJheSkgPT4ge1xuICAgICAgcmV0dXJuIChcbiAgICAgICAgQm9vbGVhbihjbGFzc05hbWUpICYmXG4gICAgICAgIChjbGFzc05hbWUgYXMgc3RyaW5nKS50cmltKCkgIT09ICcnICYmXG4gICAgICAgIGFycmF5LmluZGV4T2YoY2xhc3NOYW1lKSA9PT0gaW5kZXhcbiAgICAgICk7XG4gICAgfSlcbiAgICAuam9pbignICcpXG4gICAgLnRyaW0oKTtcblxuLyoqXG4gKiBDaGVjayBpZiBhIGNvbXBvbmVudCBoYXMgYW4gYWNjZXNzaWJpbGl0eSBwcm9wXG4gKlxuICogQHBhcmFtIHtvYmplY3R9IHByb3BzXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn0gV2hldGhlciB0aGUgY29tcG9uZW50IGhhcyBhbiBhY2Nlc3NpYmlsaXR5IHByb3BcbiAqL1xuZXhwb3J0IGNvbnN0IGhhc0ExMXlQcm9wID0gKHByb3BzOiBSZWNvcmQ8c3RyaW5nLCBhbnk+KSA9PiB7XG4gIGZvciAoY29uc3QgcHJvcCBpbiBwcm9wcykge1xuICAgIGlmIChwcm9wLnN0YXJ0c1dpdGgoJ2FyaWEtJykgfHwgcHJvcCA9PT0gJ3JvbGUnIHx8IHByb3AgPT09ICd0aXRsZScpIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgfVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmoneer%5C%5Cfolders%5C%5Ccopy%20working%5C%5Cserver_control%5C%5Cserver-monitor%5C%5Csrc%5C%5Capp%5C%5Csystem-analysis%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmoneer%5C%5Cfolders%5C%5Ccopy%20working%5C%5Cserver_control%5C%5Cserver-monitor%5C%5Csrc%5C%5Capp%5C%5Csystem-analysis%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/system-analysis/page.tsx */ \"(app-pages-browser)/./src/app/system-analysis/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q21vbmVlciU1QyU1Q2ZvbGRlcnMlNUMlNUNjb3B5JTIwd29ya2luZyU1QyU1Q3NlcnZlcl9jb250cm9sJTVDJTVDc2VydmVyLW1vbml0b3IlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNzeXN0ZW0tYW5hbHlzaXMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDhMQUEySSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcbW9uZWVyXFxcXGZvbGRlcnNcXFxcY29weSB3b3JraW5nXFxcXHNlcnZlcl9jb250cm9sXFxcXHNlcnZlci1tb25pdG9yXFxcXHNyY1xcXFxhcHBcXFxcc3lzdGVtLWFuYWx5c2lzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmoneer%5C%5Cfolders%5C%5Ccopy%20working%5C%5Cserver_control%5C%5Cserver-monitor%5C%5Csrc%5C%5Capp%5C%5Csystem-analysis%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxtb25lZXJcXGZvbGRlcnNcXGNvcHkgd29ya2luZ1xcc2VydmVyX2NvbnRyb2xcXHNlcnZlci1tb25pdG9yXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/system-analysis/page.tsx":
/*!******************************************!*\
  !*** ./src/app/system-analysis/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SystemAnalysisPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_FolderOpen_HardDrive_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,FolderOpen,HardDrive!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_FolderOpen_HardDrive_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,FolderOpen,HardDrive!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_FolderOpen_HardDrive_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,FolderOpen,HardDrive!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_FolderOpen_HardDrive_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,FolderOpen,HardDrive!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _components_SystemErrorsPanel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SystemErrorsPanel */ \"(app-pages-browser)/./src/components/SystemErrorsPanel.tsx\");\n/* harmony import */ var _components_TempFilesPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/TempFilesPanel */ \"(app-pages-browser)/./src/components/TempFilesPanel.tsx\");\n/* harmony import */ var _components_LargeFilesPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/LargeFilesPanel */ \"(app-pages-browser)/./src/components/LargeFilesPanel.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nfunction SystemAnalysisPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const tabs = [\n        {\n            id: 'overview',\n            name: 'نظرة عامة',\n            icon: _barrel_optimize_names_AlertTriangle_BarChart3_FolderOpen_HardDrive_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: 'text-blue-500'\n        },\n        {\n            id: 'errors',\n            name: 'أخطاء النظام',\n            icon: _barrel_optimize_names_AlertTriangle_BarChart3_FolderOpen_HardDrive_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: 'text-red-500'\n        },\n        {\n            id: 'temp-files',\n            name: 'الملفات المؤقتة',\n            icon: _barrel_optimize_names_AlertTriangle_BarChart3_FolderOpen_HardDrive_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: 'text-orange-500'\n        },\n        {\n            id: 'large-files',\n            name: 'الملفات الكبيرة',\n            icon: _barrel_optimize_names_AlertTriangle_BarChart3_FolderOpen_HardDrive_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            color: 'text-purple-500'\n        }\n    ];\n    const renderTabContent = ()=>{\n        switch(activeTab){\n            case 'errors':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SystemErrorsPanel__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 16\n                }, this);\n            case 'temp-files':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TempFilesPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 16\n                }, this);\n            case 'large-files':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LargeFilesPanel__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 16\n                }, this);\n            case 'overview':\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SystemOverview, {}, void 0, false, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-800 mb-2\",\n                            children: \"تحليل النظام المتقدم\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"مراقبة أخطاء النظام والملفات المؤقتة والملفات الكبيرة لتحسين الأداء\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex space-x-8 px-6\",\n                            \"aria-label\": \"Tabs\",\n                            children: tabs.map((tab)=>{\n                                const Icon = tab.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab.id),\n                                    className: \"flex items-center py-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-blue-500 \".concat(tab.color) : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"w-5 h-5 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 21\n                                        }, this),\n                                        tab.name\n                                    ]\n                                }, tab.id, true, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: renderTabContent()\n                }, void 0, false, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(SystemAnalysisPage, \"chU/96YDf6tpLdqvgqS2ZL+A4Bo=\");\n_c = SystemAnalysisPage;\n// مكون النظرة العامة\nfunction SystemOverview() {\n    var _overviewData_errors, _overviewData_errors1;\n    _s1();\n    const [overviewData, setOverviewData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"SystemOverview.useEffect\": ()=>{\n            const fetchOverview = {\n                \"SystemOverview.useEffect.fetchOverview\": async ()=>{\n                    try {\n                        const response = await fetch('/api/system-analysis?type=full-analysis');\n                        const result = await response.json();\n                        if (result.success) {\n                            setOverviewData(result.data);\n                        }\n                    } catch (error) {\n                        console.error('خطأ في جلب نظرة عامة:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"SystemOverview.useEffect.fetchOverview\"];\n            fetchOverview();\n        }\n    }[\"SystemOverview.useEffect\"], []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"mr-3 text-gray-600\",\n                        children: \"جاري تحميل البيانات...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-md p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-red-100 rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_FolderOpen_HardDrive_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-6 h-6 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mr-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-800\",\n                                            children: \"أخطاء النظام\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-red-600\",\n                                            children: (overviewData === null || overviewData === void 0 ? void 0 : (_overviewData_errors = overviewData.errors) === null || _overviewData_errors === void 0 ? void 0 : _overviewData_errors.length) || 0\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"خطأ مكتشف\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-md p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-orange-100 rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_FolderOpen_HardDrive_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-6 h-6 text-orange-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mr-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-800\",\n                                            children: \"الملفات المؤقتة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-orange-600\",\n                                            children: (overviewData === null || overviewData === void 0 ? void 0 : overviewData.tempFileCount) || 0\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: (overviewData === null || overviewData === void 0 ? void 0 : overviewData.totalTempSizeFormatted) || '0 B'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-md p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-purple-100 rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_FolderOpen_HardDrive_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-6 h-6 text-purple-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mr-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-800\",\n                                            children: \"الملفات الكبيرة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-purple-600\",\n                                            children: (overviewData === null || overviewData === void 0 ? void 0 : overviewData.largeFileCount) || 0\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"ملف كبير\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            (overviewData === null || overviewData === void 0 ? void 0 : overviewData.errors) && overviewData.errors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-md p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800 mb-4\",\n                        children: \"أحدث الأخطاء\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: overviewData.errors.slice(0, 5).map((error, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center p-3 bg-red-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_FolderOpen_HardDrive_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5 text-red-500 ml-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-gray-800\",\n                                                children: error.source\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600 truncate\",\n                                                children: [\n                                                    error.message.substring(0, 100),\n                                                    \"...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: new Date(error.timestamp).toLocaleString('en-US')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 9\n            }, this),\n            (overviewData === null || overviewData === void 0 ? void 0 : overviewData.largeFiles) && overviewData.largeFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-md p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800 mb-4\",\n                        children: \"أكبر الملفات\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: overviewData.largeFiles.slice(0, 5).map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center p-3 bg-purple-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_FolderOpen_HardDrive_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-5 h-5 text-purple-500 ml-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-gray-800\",\n                                                children: file.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600 truncate\",\n                                                children: file.directory\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-bold text-purple-600\",\n                                                children: file.sizeFormatted\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: new Date(file.lastModified).toLocaleDateString('en-US')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                lineNumber: 212,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-md p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800 mb-4\",\n                        children: \"التوصيات\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            (overviewData === null || overviewData === void 0 ? void 0 : overviewData.tempFileCount) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center p-3 bg-yellow-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_FolderOpen_HardDrive_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5 text-yellow-500 ml-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-gray-800\",\n                                                children: \"تنظيف الملفات المؤقتة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    \"يمكنك توفير \",\n                                                    overviewData.totalTempSizeFormatted,\n                                                    \" من المساحة بحذف الملفات المؤقتة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this),\n                            (overviewData === null || overviewData === void 0 ? void 0 : (_overviewData_errors1 = overviewData.errors) === null || _overviewData_errors1 === void 0 ? void 0 : _overviewData_errors1.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center p-3 bg-red-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_FolderOpen_HardDrive_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5 text-red-500 ml-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-gray-800\",\n                                                children: \"مراجعة أخطاء النظام\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    \"هناك \",\n                                                    overviewData.errors.length,\n                                                    \" خطأ يحتاج إلى مراجعة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, this),\n                            (overviewData === null || overviewData === void 0 ? void 0 : overviewData.largeFileCount) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center p-3 bg-blue-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_FolderOpen_HardDrive_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-500 ml-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-gray-800\",\n                                                children: \"مراجعة الملفات الكبيرة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    \"تم العثور على \",\n                                                    overviewData.largeFileCount,\n                                                    \" ملف كبير قد يحتاج إلى مراجعة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\system-analysis\\\\page.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, this);\n}\n_s1(SystemOverview, \"EgC9Jy+EWLUO+kfRCcIqKeIlO+I=\");\n_c1 = SystemOverview;\nvar _c, _c1;\n$RefreshReg$(_c, \"SystemAnalysisPage\");\n$RefreshReg$(_c1, \"SystemOverview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/system-analysis/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/LargeFilesPanel.tsx":
/*!********************************************!*\
  !*** ./src/components/LargeFilesPanel.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LargeFilesPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_File_Filter_FolderOpen_HardDrive_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=File,Filter,FolderOpen,HardDrive,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_File_Filter_FolderOpen_HardDrive_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=File,Filter,FolderOpen,HardDrive,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_File_Filter_FolderOpen_HardDrive_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=File,Filter,FolderOpen,HardDrive,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_File_Filter_FolderOpen_HardDrive_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=File,Filter,FolderOpen,HardDrive,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_File_Filter_FolderOpen_HardDrive_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=File,Filter,FolderOpen,HardDrive,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_File_Filter_FolderOpen_HardDrive_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=File,Filter,FolderOpen,HardDrive,RefreshCw,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction LargeFilesPanel() {\n    _s();\n    const [largeData, setLargeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [minSizeMB, setMinSizeMB] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('size');\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('desc');\n    const fetchLargeFiles = async function() {\n        let minSize = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : minSizeMB;\n        try {\n            setRefreshing(true);\n            const response = await fetch(\"/api/system-analysis?type=large-files&minSize=\".concat(minSize));\n            const result = await response.json();\n            if (result.success) {\n                // تحويل التواريخ من string إلى Date\n                const filesWithDates = result.data.files.map((file)=>({\n                        ...file,\n                        lastModified: new Date(file.lastModified)\n                    }));\n                setLargeData({\n                    ...result.data,\n                    files: filesWithDates\n                });\n            }\n        } catch (error) {\n            console.error('خطأ في جلب الملفات الكبيرة:', error);\n        } finally{\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LargeFilesPanel.useEffect\": ()=>{\n            fetchLargeFiles();\n        }\n    }[\"LargeFilesPanel.useEffect\"], []);\n    const handleMinSizeChange = (newMinSize)=>{\n        setMinSizeMB(newMinSize);\n        fetchLargeFiles(newMinSize);\n    };\n    const formatTimestamp = (timestamp)=>{\n        return timestamp.toLocaleString('en-US', {\n            year: 'numeric',\n            month: '2-digit',\n            day: '2-digit',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    const getFilteredAndSortedFiles = ()=>{\n        if (!largeData) return [];\n        let filtered = largeData.files;\n        // تطبيق البحث\n        if (searchTerm) {\n            filtered = filtered.filter((file)=>file.name.toLowerCase().includes(searchTerm.toLowerCase()) || file.directory.toLowerCase().includes(searchTerm.toLowerCase()));\n        }\n        // تطبيق الترتيب\n        filtered.sort((a, b)=>{\n            let comparison = 0;\n            switch(sortBy){\n                case 'size':\n                    comparison = a.size - b.size;\n                    break;\n                case 'name':\n                    comparison = a.name.localeCompare(b.name);\n                    break;\n                case 'date':\n                    comparison = a.lastModified.getTime() - b.lastModified.getTime();\n                    break;\n            }\n            return sortOrder === 'desc' ? -comparison : comparison;\n        });\n        return filtered;\n    };\n    const getTotalSize = ()=>{\n        if (!largeData) return 0;\n        return largeData.files.reduce((total, file)=>total + file.size, 0);\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 B';\n        const k = 1024;\n        const sizes = [\n            'B',\n            'KB',\n            'MB',\n            'GB',\n            'TB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    const filteredFiles = getFilteredAndSortedFiles();\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Filter_FolderOpen_HardDrive_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"mr-3 text-gray-600\",\n                        children: \"جاري تحميل الملفات الكبيرة...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n            lineNumber: 129,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Filter_FolderOpen_HardDrive_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-6 h-6 text-purple-500 ml-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-800\",\n                                        children: \"الملفات الكبيرة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this),\n                                    largeData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-3 px-2 py-1 bg-purple-100 text-purple-800 text-sm rounded-full\",\n                                        children: [\n                                            largeData.count,\n                                            \" ملف\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>fetchLargeFiles(),\n                                disabled: refreshing,\n                                className: \"flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Filter_FolderOpen_HardDrive_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-4 h-4 ml-2 \".concat(refreshing ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"تحديث\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    largeData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-purple-600\",\n                                        children: \"إجمالي الحجم\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-800\",\n                                        children: formatFileSize(getTotalSize())\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-blue-600\",\n                                        children: \"عدد الملفات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-800\",\n                                        children: largeData.count\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-green-600\",\n                                        children: \"الحد الأدنى\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-800\",\n                                        children: [\n                                            largeData.minSizeMB,\n                                            \" MB\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Filter_FolderOpen_HardDrive_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-4 h-4 text-gray-500 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm text-gray-600 ml-2\",\n                                        children: \"الحد الأدنى (MB):\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: minSizeMB,\n                                        onChange: (e)=>handleMinSizeChange(Number(e.target.value)),\n                                        className: \"px-3 py-1 border border-gray-300 rounded-lg text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: 50,\n                                                children: \"50 MB\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: 100,\n                                                children: \"100 MB\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: 500,\n                                                children: \"500 MB\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: 1000,\n                                                children: \"1 GB\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: 5000,\n                                                children: \"5 GB\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Filter_FolderOpen_HardDrive_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4 text-gray-500 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"البحث في أسماء الملفات أو المجلدات...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"flex-1 px-3 py-1 border border-gray-300 rounded-lg text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"ترتيب:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: sortBy,\n                                        onChange: (e)=>setSortBy(e.target.value),\n                                        className: \"px-3 py-1 border border-gray-300 rounded-lg text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"size\",\n                                                children: \"الحجم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"name\",\n                                                children: \"الاسم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"date\",\n                                                children: \"التاريخ\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc'),\n                                        className: \"px-2 py-1 bg-gray-100 rounded text-sm hover:bg-gray-200\",\n                                        children: sortOrder === 'desc' ? '↓' : '↑'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: filteredFiles.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Filter_FolderOpen_HardDrive_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-lg\",\n                            children: searchTerm ? 'لا توجد ملفات تطابق البحث' : 'لا توجد ملفات كبيرة'\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: searchTerm ? 'جرب تغيير مصطلح البحث' : \"لا توجد ملفات أكبر من \".concat(minSizeMB, \" MB\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: filteredFiles.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Filter_FolderOpen_HardDrive_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-400 ml-3\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-800\",\n                                                        children: file.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center text-sm text-gray-500 mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Filter_FolderOpen_HardDrive_RefreshCw_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"w-4 h-4 ml-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"truncate max-w-md\",\n                                                                children: file.directory\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            \"آخر تعديل: \",\n                                                            formatTimestamp(file.lastModified)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-bold text-purple-600\",\n                                                children: file.sizeFormatted\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    (file.size / (1024 * 1024)).toFixed(1),\n                                                    \" MB\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 17\n                            }, this)\n                        }, \"\".concat(file.path, \"-\").concat(index), false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, this),\n            filteredFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-sm text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"عرض \",\n                                filteredFiles.length,\n                                \" من \",\n                                (largeData === null || largeData === void 0 ? void 0 : largeData.count) || 0,\n                                \" ملف\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"إجمالي الحجم المعروض: \",\n                                formatFileSize(filteredFiles.reduce((total, file)=>total + file.size, 0))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n                lineNumber: 283,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\LargeFilesPanel.tsx\",\n        lineNumber: 139,\n        columnNumber: 5\n    }, this);\n}\n_s(LargeFilesPanel, \"8x3LQz+AzJ8nIJutcQVugMHP+QY=\");\n_c = LargeFilesPanel;\nvar _c;\n$RefreshReg$(_c, \"LargeFilesPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0xhcmdlRmlsZXNQYW5lbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBRXlFO0FBQ3NCO0FBaUJoRixTQUFTUzs7SUFDdEIsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUdWLCtDQUFRQSxDQUF3QjtJQUNsRSxNQUFNLENBQUNXLFNBQVNDLFdBQVcsR0FBR1osK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDYSxZQUFZQyxjQUFjLEdBQUdkLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2UsV0FBV0MsYUFBYSxHQUFHaEIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDaUIsWUFBWUMsY0FBYyxHQUFHbEIsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDbUIsUUFBUUMsVUFBVSxHQUFHcEIsK0NBQVFBLENBQTJCO0lBQy9ELE1BQU0sQ0FBQ3FCLFdBQVdDLGFBQWEsR0FBR3RCLCtDQUFRQSxDQUFpQjtJQUUzRCxNQUFNdUIsa0JBQWtCO1lBQU9DLDJFQUFrQlQ7UUFDL0MsSUFBSTtZQUNGRCxjQUFjO1lBQ2QsTUFBTVcsV0FBVyxNQUFNQyxNQUFNLGlEQUF5RCxPQUFSRjtZQUM5RSxNQUFNRyxTQUFTLE1BQU1GLFNBQVNHLElBQUk7WUFFbEMsSUFBSUQsT0FBT0UsT0FBTyxFQUFFO2dCQUNsQixvQ0FBb0M7Z0JBQ3BDLE1BQU1DLGlCQUFpQkgsT0FBT0ksSUFBSSxDQUFDQyxLQUFLLENBQUNDLEdBQUcsQ0FBQyxDQUFDQyxPQUFlO3dCQUMzRCxHQUFHQSxJQUFJO3dCQUNQQyxjQUFjLElBQUlDLEtBQUtGLEtBQUtDLFlBQVk7b0JBQzFDO2dCQUVBekIsYUFBYTtvQkFDWCxHQUFHaUIsT0FBT0ksSUFBSTtvQkFDZEMsT0FBT0Y7Z0JBQ1Q7WUFDRjtRQUNGLEVBQUUsT0FBT08sT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsK0JBQStCQTtRQUMvQyxTQUFVO1lBQ1J6QixXQUFXO1lBQ1hFLGNBQWM7UUFDaEI7SUFDRjtJQUVBYixnREFBU0E7cUNBQUM7WUFDUnNCO1FBQ0Y7b0NBQUcsRUFBRTtJQUVMLE1BQU1nQixzQkFBc0IsQ0FBQ0M7UUFDM0J4QixhQUFhd0I7UUFDYmpCLGdCQUFnQmlCO0lBQ2xCO0lBRUEsTUFBTUMsa0JBQWtCLENBQUNDO1FBQ3ZCLE9BQU9BLFVBQVVDLGNBQWMsQ0FBQyxTQUFTO1lBQ3ZDQyxNQUFNO1lBQ05DLE9BQU87WUFDUEMsS0FBSztZQUNMQyxNQUFNO1lBQ05DLFFBQVE7UUFDVjtJQUNGO0lBRUEsTUFBTUMsNEJBQTRCO1FBQ2hDLElBQUksQ0FBQ3hDLFdBQVcsT0FBTyxFQUFFO1FBRXpCLElBQUl5QyxXQUFXekMsVUFBVXVCLEtBQUs7UUFFOUIsY0FBYztRQUNkLElBQUlmLFlBQVk7WUFDZGlDLFdBQVdBLFNBQVNDLE1BQU0sQ0FBQ2pCLENBQUFBLE9BQ3pCQSxLQUFLa0IsSUFBSSxDQUFDQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQ3JDLFdBQVdvQyxXQUFXLE9BQ3ZEbkIsS0FBS3FCLFNBQVMsQ0FBQ0YsV0FBVyxHQUFHQyxRQUFRLENBQUNyQyxXQUFXb0MsV0FBVztRQUVoRTtRQUVBLGdCQUFnQjtRQUNoQkgsU0FBU00sSUFBSSxDQUFDLENBQUNDLEdBQUdDO1lBQ2hCLElBQUlDLGFBQWE7WUFFakIsT0FBUXhDO2dCQUNOLEtBQUs7b0JBQ0h3QyxhQUFhRixFQUFFRyxJQUFJLEdBQUdGLEVBQUVFLElBQUk7b0JBQzVCO2dCQUNGLEtBQUs7b0JBQ0hELGFBQWFGLEVBQUVMLElBQUksQ0FBQ1MsYUFBYSxDQUFDSCxFQUFFTixJQUFJO29CQUN4QztnQkFDRixLQUFLO29CQUNITyxhQUFhRixFQUFFdEIsWUFBWSxDQUFDMkIsT0FBTyxLQUFLSixFQUFFdkIsWUFBWSxDQUFDMkIsT0FBTztvQkFDOUQ7WUFDSjtZQUVBLE9BQU96QyxjQUFjLFNBQVMsQ0FBQ3NDLGFBQWFBO1FBQzlDO1FBRUEsT0FBT1Q7SUFDVDtJQUVBLE1BQU1hLGVBQWU7UUFDbkIsSUFBSSxDQUFDdEQsV0FBVyxPQUFPO1FBQ3ZCLE9BQU9BLFVBQVV1QixLQUFLLENBQUNnQyxNQUFNLENBQUMsQ0FBQ0MsT0FBTy9CLE9BQVMrQixRQUFRL0IsS0FBSzBCLElBQUksRUFBRTtJQUNwRTtJQUVBLE1BQU1NLGlCQUFpQixDQUFDQztRQUN0QixJQUFJQSxVQUFVLEdBQUcsT0FBTztRQUV4QixNQUFNQyxJQUFJO1FBQ1YsTUFBTUMsUUFBUTtZQUFDO1lBQUs7WUFBTTtZQUFNO1lBQU07U0FBSztRQUMzQyxNQUFNQyxJQUFJQyxLQUFLQyxLQUFLLENBQUNELEtBQUtFLEdBQUcsQ0FBQ04sU0FBU0ksS0FBS0UsR0FBRyxDQUFDTDtRQUVoRCxPQUFPTSxXQUFXLENBQUNQLFFBQVFJLEtBQUtJLEdBQUcsQ0FBQ1AsR0FBR0UsRUFBQyxFQUFHTSxPQUFPLENBQUMsTUFBTSxNQUFNUCxLQUFLLENBQUNDLEVBQUU7SUFDekU7SUFFQSxNQUFNTyxnQkFBZ0I1QjtJQUV0QixJQUFJdEMsU0FBUztRQUNYLHFCQUNFLDhEQUFDbUU7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDNUUsNkhBQVNBO3dCQUFDNEUsV0FBVTs7Ozs7O2tDQUNyQiw4REFBQ0M7d0JBQUtELFdBQVU7a0NBQXFCOzs7Ozs7Ozs7Ozs7Ozs7OztJQUk3QztJQUVBLHFCQUNFLDhEQUFDRDtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQzdFLDZIQUFTQTt3Q0FBQzZFLFdBQVU7Ozs7OztrREFDckIsOERBQUNFO3dDQUFHRixXQUFVO2tEQUFrQzs7Ozs7O29DQUMvQ3RFLDJCQUNDLDhEQUFDdUU7d0NBQUtELFdBQVU7OzRDQUNidEUsVUFBVXlFLEtBQUs7NENBQUM7Ozs7Ozs7Ozs7Ozs7MENBSXZCLDhEQUFDQztnQ0FDQ0MsU0FBUyxJQUFNN0Q7Z0NBQ2Y4RCxVQUFVeEU7Z0NBQ1ZrRSxXQUFVOztrREFFViw4REFBQzVFLDZIQUFTQTt3Q0FBQzRFLFdBQVcsZ0JBQWlELE9BQWpDbEUsYUFBYSxpQkFBaUI7Ozs7OztvQ0FBUTs7Ozs7Ozs7Ozs7OztvQkFNL0VKLDJCQUNDLDhEQUFDcUU7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUEwQjs7Ozs7O2tEQUN6Qyw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQXNDYixlQUFlSDs7Ozs7Ozs7Ozs7OzBDQUV0RSw4REFBQ2U7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFBd0I7Ozs7OztrREFDdkMsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUFvQ3RFLFVBQVV5RSxLQUFLOzs7Ozs7Ozs7Ozs7MENBRXBFLDhEQUFDSjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUF5Qjs7Ozs7O2tEQUN4Qyw4REFBQ0Q7d0NBQUlDLFdBQVU7OzRDQUFxQ3RFLFVBQVVNLFNBQVM7NENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTTlFLDhEQUFDK0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUN4RSw2SEFBTUE7d0NBQUN3RSxXQUFVOzs7Ozs7a0RBQ2xCLDhEQUFDTzt3Q0FBTVAsV0FBVTtrREFBNkI7Ozs7OztrREFDOUMsOERBQUNRO3dDQUNDQyxPQUFPekU7d0NBQ1AwRSxVQUFVLENBQUNDLElBQU1uRCxvQkFBb0JvRCxPQUFPRCxFQUFFRSxNQUFNLENBQUNKLEtBQUs7d0NBQzFEVCxXQUFVOzswREFFViw4REFBQ2M7Z0RBQU9MLE9BQU87MERBQUk7Ozs7OzswREFDbkIsOERBQUNLO2dEQUFPTCxPQUFPOzBEQUFLOzs7Ozs7MERBQ3BCLDhEQUFDSztnREFBT0wsT0FBTzswREFBSzs7Ozs7OzBEQUNwQiw4REFBQ0s7Z0RBQU9MLE9BQU87MERBQU07Ozs7OzswREFDckIsOERBQUNLO2dEQUFPTCxPQUFPOzBEQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBS3pCLDhEQUFDVjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUN6RSw2SEFBTUE7d0NBQUN5RSxXQUFVOzs7Ozs7a0RBQ2xCLDhEQUFDZTt3Q0FDQ0MsTUFBSzt3Q0FDTEMsYUFBWTt3Q0FDWlIsT0FBT3ZFO3dDQUNQd0UsVUFBVSxDQUFDQyxJQUFNeEUsY0FBY3dFLEVBQUVFLE1BQU0sQ0FBQ0osS0FBSzt3Q0FDN0NULFdBQVU7Ozs7Ozs7Ozs7OzswQ0FLZCw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDTzt3Q0FBTVAsV0FBVTtrREFBd0I7Ozs7OztrREFDekMsOERBQUNRO3dDQUNDQyxPQUFPckU7d0NBQ1BzRSxVQUFVLENBQUNDLElBQU10RSxVQUFVc0UsRUFBRUUsTUFBTSxDQUFDSixLQUFLO3dDQUN6Q1QsV0FBVTs7MERBRVYsOERBQUNjO2dEQUFPTCxPQUFNOzBEQUFPOzs7Ozs7MERBQ3JCLDhEQUFDSztnREFBT0wsT0FBTTswREFBTzs7Ozs7OzBEQUNyQiw4REFBQ0s7Z0RBQU9MLE9BQU07MERBQU87Ozs7Ozs7Ozs7OztrREFFdkIsOERBQUNMO3dDQUNDQyxTQUFTLElBQU05RCxhQUFhRCxjQUFjLFFBQVEsU0FBUzt3Q0FDM0QwRCxXQUFVO2tEQUVUMUQsY0FBYyxTQUFTLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPdEMsOERBQUN5RDtnQkFBSUMsV0FBVTswQkFDWkYsY0FBY29CLE1BQU0sS0FBSyxrQkFDeEIsOERBQUNuQjtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUMzRSw2SEFBSUE7NEJBQUMyRSxXQUFVOzs7Ozs7c0NBQ2hCLDhEQUFDbUI7NEJBQUVuQixXQUFVO3NDQUNWOUQsYUFBYSw4QkFBOEI7Ozs7OztzQ0FFOUMsOERBQUNpRjs0QkFBRW5CLFdBQVU7c0NBQ1Y5RCxhQUFhLDBCQUEwQix5QkFBbUMsT0FBVkYsV0FBVTs7Ozs7Ozs7Ozs7eUNBSS9FLDhEQUFDK0Q7b0JBQUlDLFdBQVU7OEJBQ1pGLGNBQWM1QyxHQUFHLENBQUMsQ0FBQ0MsTUFBTWlFLHNCQUN4Qiw4REFBQ3JCOzRCQUVDQyxXQUFVO3NDQUVWLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQzNFLDZIQUFJQTtnREFBQzJFLFdBQVU7Ozs7OzswREFDaEIsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQTZCN0MsS0FBS2tCLElBQUk7Ozs7OztrRUFDckQsOERBQUMwQjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUMxRSw2SEFBVUE7Z0VBQUMwRSxXQUFVOzs7Ozs7MEVBQ3RCLDhEQUFDQztnRUFBS0QsV0FBVTswRUFBcUI3QyxLQUFLcUIsU0FBUzs7Ozs7Ozs7Ozs7O2tFQUVyRCw4REFBQ3VCO3dEQUFJQyxXQUFVOzs0REFBNkI7NERBQzlCdEMsZ0JBQWdCUCxLQUFLQyxZQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUluRCw4REFBQzJDO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ1o3QyxLQUFLa0UsYUFBYTs7Ozs7OzBEQUVyQiw4REFBQ3RCO2dEQUFJQyxXQUFVOztvREFDWDdDLENBQUFBLEtBQUswQixJQUFJLEdBQUksUUFBTyxJQUFHLENBQUMsRUFBR2dCLE9BQU8sQ0FBQztvREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsyQkF0QnpDLEdBQWdCdUIsT0FBYmpFLEtBQUttRSxJQUFJLEVBQUMsS0FBUyxPQUFORjs7Ozs7Ozs7Ozs7Ozs7O1lBaUM5QnRCLGNBQWNvQixNQUFNLEdBQUcsbUJBQ3RCLDhEQUFDbkI7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0M7O2dDQUFLO2dDQUFLSCxjQUFjb0IsTUFBTTtnQ0FBQztnQ0FBS3hGLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBV3lFLEtBQUssS0FBSTtnQ0FBRTs7Ozs7OztzQ0FDM0QsOERBQUNGOztnQ0FBSztnQ0FBdUJkLGVBQWVXLGNBQWNiLE1BQU0sQ0FBQyxDQUFDQyxPQUFPL0IsT0FBUytCLFFBQVEvQixLQUFLMEIsSUFBSSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNakg7R0EvUXdCcEQ7S0FBQUEiLCJzb3VyY2VzIjpbIkQ6XFxtb25lZXJcXGZvbGRlcnNcXGNvcHkgd29ya2luZ1xcc2VydmVyX2NvbnRyb2xcXHNlcnZlci1tb25pdG9yXFxzcmNcXGNvbXBvbmVudHNcXExhcmdlRmlsZXNQYW5lbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2ssIHVzZU1lbW8gfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBIYXJkRHJpdmUsIFJlZnJlc2hDdywgRmlsZSwgRm9sZGVyT3BlbiwgU2VhcmNoLCBGaWx0ZXIsIExvYWRlcjIgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuXG5pbnRlcmZhY2UgTGFyZ2VGaWxlIHtcbiAgcGF0aDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIHNpemU6IG51bWJlcjtcbiAgc2l6ZUZvcm1hdHRlZDogc3RyaW5nO1xuICBsYXN0TW9kaWZpZWQ6IERhdGU7XG4gIGRpcmVjdG9yeTogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgTGFyZ2VGaWxlc0RhdGEge1xuICBmaWxlczogTGFyZ2VGaWxlW107XG4gIGNvdW50OiBudW1iZXI7XG4gIG1pblNpemVNQjogbnVtYmVyO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMYXJnZUZpbGVzUGFuZWwoKSB7XG4gIGNvbnN0IFtsYXJnZURhdGEsIHNldExhcmdlRGF0YV0gPSB1c2VTdGF0ZTxMYXJnZUZpbGVzRGF0YSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW3JlZnJlc2hpbmcsIHNldFJlZnJlc2hpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbbWluU2l6ZU1CLCBzZXRNaW5TaXplTUJdID0gdXNlU3RhdGUoMTAwKTtcbiAgY29uc3QgW3NlYXJjaFRlcm0sIHNldFNlYXJjaFRlcm1dID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbc29ydEJ5LCBzZXRTb3J0QnldID0gdXNlU3RhdGU8J3NpemUnIHwgJ25hbWUnIHwgJ2RhdGUnPignc2l6ZScpO1xuICBjb25zdCBbc29ydE9yZGVyLCBzZXRTb3J0T3JkZXJdID0gdXNlU3RhdGU8J2FzYycgfCAnZGVzYyc+KCdkZXNjJyk7XG5cbiAgY29uc3QgZmV0Y2hMYXJnZUZpbGVzID0gYXN5bmMgKG1pblNpemU6IG51bWJlciA9IG1pblNpemVNQikgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRSZWZyZXNoaW5nKHRydWUpO1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9zeXN0ZW0tYW5hbHlzaXM/dHlwZT1sYXJnZS1maWxlcyZtaW5TaXplPSR7bWluU2l6ZX1gKTtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcblxuICAgICAgaWYgKHJlc3VsdC5zdWNjZXNzKSB7XG4gICAgICAgIC8vINiq2K3ZiNmK2YQg2KfZhNiq2YjYp9ix2YrYriDZhdmGIHN0cmluZyDYpdmE2YkgRGF0ZVxuICAgICAgICBjb25zdCBmaWxlc1dpdGhEYXRlcyA9IHJlc3VsdC5kYXRhLmZpbGVzLm1hcCgoZmlsZTogYW55KSA9PiAoe1xuICAgICAgICAgIC4uLmZpbGUsXG4gICAgICAgICAgbGFzdE1vZGlmaWVkOiBuZXcgRGF0ZShmaWxlLmxhc3RNb2RpZmllZClcbiAgICAgICAgfSkpO1xuXG4gICAgICAgIHNldExhcmdlRGF0YSh7XG4gICAgICAgICAgLi4ucmVzdWx0LmRhdGEsXG4gICAgICAgICAgZmlsZXM6IGZpbGVzV2l0aERhdGVzXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfYrti32KMg2YHZiiDYrNmE2Kgg2KfZhNmF2YTZgdin2Kog2KfZhNmD2KjZitix2Kk6JywgZXJyb3IpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgIHNldFJlZnJlc2hpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGZldGNoTGFyZ2VGaWxlcygpO1xuICB9LCBbXSk7XG5cbiAgY29uc3QgaGFuZGxlTWluU2l6ZUNoYW5nZSA9IChuZXdNaW5TaXplOiBudW1iZXIpID0+IHtcbiAgICBzZXRNaW5TaXplTUIobmV3TWluU2l6ZSk7XG4gICAgZmV0Y2hMYXJnZUZpbGVzKG5ld01pblNpemUpO1xuICB9O1xuXG4gIGNvbnN0IGZvcm1hdFRpbWVzdGFtcCA9ICh0aW1lc3RhbXA6IERhdGUpID0+IHtcbiAgICByZXR1cm4gdGltZXN0YW1wLnRvTG9jYWxlU3RyaW5nKCdlbi1VUycsIHtcbiAgICAgIHllYXI6ICdudW1lcmljJyxcbiAgICAgIG1vbnRoOiAnMi1kaWdpdCcsXG4gICAgICBkYXk6ICcyLWRpZ2l0JyxcbiAgICAgIGhvdXI6ICcyLWRpZ2l0JyxcbiAgICAgIG1pbnV0ZTogJzItZGlnaXQnXG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3QgZ2V0RmlsdGVyZWRBbmRTb3J0ZWRGaWxlcyA9ICgpID0+IHtcbiAgICBpZiAoIWxhcmdlRGF0YSkgcmV0dXJuIFtdO1xuXG4gICAgbGV0IGZpbHRlcmVkID0gbGFyZ2VEYXRhLmZpbGVzO1xuXG4gICAgLy8g2KrYt9io2YrZgiDYp9mE2KjYrdirXG4gICAgaWYgKHNlYXJjaFRlcm0pIHtcbiAgICAgIGZpbHRlcmVkID0gZmlsdGVyZWQuZmlsdGVyKGZpbGUgPT5cbiAgICAgICAgZmlsZS5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybS50b0xvd2VyQ2FzZSgpKSB8fFxuICAgICAgICBmaWxlLmRpcmVjdG9yeS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0udG9Mb3dlckNhc2UoKSlcbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8g2KrYt9io2YrZgiDYp9mE2KrYsdiq2YrYqFxuICAgIGZpbHRlcmVkLnNvcnQoKGEsIGIpID0+IHtcbiAgICAgIGxldCBjb21wYXJpc29uID0gMDtcblxuICAgICAgc3dpdGNoIChzb3J0QnkpIHtcbiAgICAgICAgY2FzZSAnc2l6ZSc6XG4gICAgICAgICAgY29tcGFyaXNvbiA9IGEuc2l6ZSAtIGIuc2l6ZTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnbmFtZSc6XG4gICAgICAgICAgY29tcGFyaXNvbiA9IGEubmFtZS5sb2NhbGVDb21wYXJlKGIubmFtZSk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ2RhdGUnOlxuICAgICAgICAgIGNvbXBhcmlzb24gPSBhLmxhc3RNb2RpZmllZC5nZXRUaW1lKCkgLSBiLmxhc3RNb2RpZmllZC5nZXRUaW1lKCk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBzb3J0T3JkZXIgPT09ICdkZXNjJyA/IC1jb21wYXJpc29uIDogY29tcGFyaXNvbjtcbiAgICB9KTtcblxuICAgIHJldHVybiBmaWx0ZXJlZDtcbiAgfTtcblxuICBjb25zdCBnZXRUb3RhbFNpemUgPSAoKSA9PiB7XG4gICAgaWYgKCFsYXJnZURhdGEpIHJldHVybiAwO1xuICAgIHJldHVybiBsYXJnZURhdGEuZmlsZXMucmVkdWNlKCh0b3RhbCwgZmlsZSkgPT4gdG90YWwgKyBmaWxlLnNpemUsIDApO1xuICB9O1xuXG4gIGNvbnN0IGZvcm1hdEZpbGVTaXplID0gKGJ5dGVzOiBudW1iZXIpOiBzdHJpbmcgPT4ge1xuICAgIGlmIChieXRlcyA9PT0gMCkgcmV0dXJuICcwIEInO1xuXG4gICAgY29uc3QgayA9IDEwMjQ7XG4gICAgY29uc3Qgc2l6ZXMgPSBbJ0InLCAnS0InLCAnTUInLCAnR0InLCAnVEInXTtcbiAgICBjb25zdCBpID0gTWF0aC5mbG9vcihNYXRoLmxvZyhieXRlcykgLyBNYXRoLmxvZyhrKSk7XG5cbiAgICByZXR1cm4gcGFyc2VGbG9hdCgoYnl0ZXMgLyBNYXRoLnBvdyhrLCBpKSkudG9GaXhlZCgyKSkgKyAnICcgKyBzaXplc1tpXTtcbiAgfTtcblxuICBjb25zdCBmaWx0ZXJlZEZpbGVzID0gZ2V0RmlsdGVyZWRBbmRTb3J0ZWRGaWxlcygpO1xuXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbWQgcC02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC0zMlwiPlxuICAgICAgICAgIDxSZWZyZXNoQ3cgY2xhc3NOYW1lPVwidy04IGgtOCBhbmltYXRlLXNwaW4gdGV4dC1ibHVlLTUwMFwiIC8+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXItMyB0ZXh0LWdyYXktNjAwXCI+2KzYp9ix2Yog2KrYrdmF2YrZhCDYp9mE2YXZhNmB2KfYqiDYp9mE2YPYqNmK2LHYqS4uLjwvc3Bhbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LW1kXCI+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICA8SGFyZERyaXZlIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1wdXJwbGUtNTAwIG1sLTNcIiAvPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtZ3JheS04MDBcIj7Yp9mE2YXZhNmB2KfYqiDYp9mE2YPYqNmK2LHYqTwvaDI+XG4gICAgICAgICAgICB7bGFyZ2VEYXRhICYmIChcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXItMyBweC0yIHB5LTEgYmctcHVycGxlLTEwMCB0ZXh0LXB1cnBsZS04MDAgdGV4dC1zbSByb3VuZGVkLWZ1bGxcIj5cbiAgICAgICAgICAgICAgICB7bGFyZ2VEYXRhLmNvdW50fSDZhdmE2YFcbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBmZXRjaExhcmdlRmlsZXMoKX1cbiAgICAgICAgICAgIGRpc2FibGVkPXtyZWZyZXNoaW5nfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcHgtNCBweS0yIGJnLWJsdWUtNTAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTYwMCBkaXNhYmxlZDpvcGFjaXR5LTUwXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT17YHctNCBoLTQgbWwtMiAke3JlZnJlc2hpbmcgPyAnYW5pbWF0ZS1zcGluJyA6ICcnfWB9IC8+XG4gICAgICAgICAgICDYqtit2K/ZitirXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBTdGF0aXN0aWNzICovfVxuICAgICAgICB7bGFyZ2VEYXRhICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTQgbWItNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1wdXJwbGUtNTAgcC00IHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcHVycGxlLTYwMFwiPtil2KzZhdin2YTZiiDYp9mE2K3YrNmFPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtcHVycGxlLTgwMFwiPntmb3JtYXRGaWxlU2l6ZShnZXRUb3RhbFNpemUoKSl9PC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS01MCBwLTQgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTYwMFwiPti52K/YryDYp9mE2YXZhNmB2KfYqjwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtODAwXCI+e2xhcmdlRGF0YS5jb3VudH08L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi01MCBwLTQgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmVlbi02MDBcIj7Yp9mE2K3YryDYp9mE2KPYr9mG2Yk8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmVlbi04MDBcIj57bGFyZ2VEYXRhLm1pblNpemVNQn0gTUI8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBGaWx0ZXJzIGFuZCBTZWFyY2ggKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBtZDpmbGV4LXJvdyBnYXAtNFwiPlxuICAgICAgICAgIHsvKiBTaXplIEZpbHRlciAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICA8RmlsdGVyIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmF5LTUwMCBtbC0yXCIgLz5cbiAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbWwtMlwiPtin2YTYrdivINin2YTYo9iv2YbZiSAoTUIpOjwvbGFiZWw+XG4gICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgIHZhbHVlPXttaW5TaXplTUJ9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlTWluU2l6ZUNoYW5nZShOdW1iZXIoZS50YXJnZXQudmFsdWUpKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0xIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyB0ZXh0LXNtXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT17NTB9PjUwIE1CPC9vcHRpb24+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9ezEwMH0+MTAwIE1CPC9vcHRpb24+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9ezUwMH0+NTAwIE1CPC9vcHRpb24+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9ezEwMDB9PjEgR0I8L29wdGlvbj5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT17NTAwMH0+NSBHQjwvb3B0aW9uPlxuICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogU2VhcmNoICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZmxleC0xXCI+XG4gICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmF5LTUwMCBtbC0yXCIgLz5cbiAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi2KfZhNio2K3YqyDZgdmKINij2LPZhdin2KEg2KfZhNmF2YTZgdin2Kog2KPZiCDYp9mE2YXYrNmE2K/Yp9iqLi4uXCJcbiAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFRlcm19XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VhcmNoVGVybShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBweC0zIHB5LTEgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIHRleHQtc21cIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBTb3J0ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj7Yqtix2KrZitioOjwvbGFiZWw+XG4gICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgIHZhbHVlPXtzb3J0Qnl9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U29ydEJ5KGUudGFyZ2V0LnZhbHVlIGFzICdzaXplJyB8ICduYW1lJyB8ICdkYXRlJyl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgdGV4dC1zbVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJzaXplXCI+2KfZhNit2KzZhTwvb3B0aW9uPlxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwibmFtZVwiPtin2YTYp9iz2YU8L29wdGlvbj5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImRhdGVcIj7Yp9mE2KrYp9ix2YrYrjwvb3B0aW9uPlxuICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNvcnRPcmRlcihzb3J0T3JkZXIgPT09ICdhc2MnID8gJ2Rlc2MnIDogJ2FzYycpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0yIHB5LTEgYmctZ3JheS0xMDAgcm91bmRlZCB0ZXh0LXNtIGhvdmVyOmJnLWdyYXktMjAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge3NvcnRPcmRlciA9PT0gJ2Rlc2MnID8gJ+KGkycgOiAn4oaRJ31cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogQ29udGVudCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgIHtmaWx0ZXJlZEZpbGVzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LThcIj5cbiAgICAgICAgICAgIDxGaWxlIGNsYXNzTmFtZT1cInctMTYgaC0xNiB0ZXh0LWdyYXktMzAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQtbGdcIj5cbiAgICAgICAgICAgICAge3NlYXJjaFRlcm0gPyAn2YTYpyDYqtmI2KzYryDZhdmE2YHYp9iqINiq2LfYp9io2YIg2KfZhNio2K3YqycgOiAn2YTYpyDYqtmI2KzYryDZhdmE2YHYp9iqINmD2KjZitix2KknfVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgIHtzZWFyY2hUZXJtID8gJ9is2LHYqCDYqti62YrZitixINmF2LXYt9mE2K0g2KfZhNio2K3YqycgOiBg2YTYpyDYqtmI2KzYryDZhdmE2YHYp9iqINij2YPYqNixINmF2YYgJHttaW5TaXplTUJ9IE1CYH1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAge2ZpbHRlcmVkRmlsZXMubWFwKChmaWxlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAga2V5PXtgJHtmaWxlLnBhdGh9LSR7aW5kZXh9YH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGcgcC00IGhvdmVyOnNoYWRvdy1tZCB0cmFuc2l0aW9uLXNoYWRvd1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPEZpbGUgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LWdyYXktNDAwIG1sLTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTgwMFwiPntmaWxlLm5hbWV9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LXNtIHRleHQtZ3JheS01MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEZvbGRlck9wZW4gY2xhc3NOYW1lPVwidy00IGgtNCBtbC0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRydW5jYXRlIG1heC13LW1kXCI+e2ZpbGUuZGlyZWN0b3J5fTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICDYotiu2LEg2KrYudiv2YrZhDoge2Zvcm1hdFRpbWVzdGFtcChmaWxlLmxhc3RNb2RpZmllZCl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtbGVmdFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtcHVycGxlLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtmaWxlLnNpemVGb3JtYXR0ZWR9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHsoZmlsZS5zaXplIC8gKDEwMjQgKiAxMDI0KSkudG9GaXhlZCgxKX0gTUJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRm9vdGVyICovfVxuICAgICAge2ZpbHRlcmVkRmlsZXMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNiBweS00IGJnLWdyYXktNTAgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtYi1sZ1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgPHNwYW4+2LnYsdi2IHtmaWx0ZXJlZEZpbGVzLmxlbmd0aH0g2YXZhiB7bGFyZ2VEYXRhPy5jb3VudCB8fCAwfSDZhdmE2YE8L3NwYW4+XG4gICAgICAgICAgICA8c3Bhbj7Ypdis2YXYp9mE2Yog2KfZhNit2KzZhSDYp9mE2YXYudix2YjYtjoge2Zvcm1hdEZpbGVTaXplKGZpbHRlcmVkRmlsZXMucmVkdWNlKCh0b3RhbCwgZmlsZSkgPT4gdG90YWwgKyBmaWxlLnNpemUsIDApKX08L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiSGFyZERyaXZlIiwiUmVmcmVzaEN3IiwiRmlsZSIsIkZvbGRlck9wZW4iLCJTZWFyY2giLCJGaWx0ZXIiLCJMYXJnZUZpbGVzUGFuZWwiLCJsYXJnZURhdGEiLCJzZXRMYXJnZURhdGEiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInJlZnJlc2hpbmciLCJzZXRSZWZyZXNoaW5nIiwibWluU2l6ZU1CIiwic2V0TWluU2l6ZU1CIiwic2VhcmNoVGVybSIsInNldFNlYXJjaFRlcm0iLCJzb3J0QnkiLCJzZXRTb3J0QnkiLCJzb3J0T3JkZXIiLCJzZXRTb3J0T3JkZXIiLCJmZXRjaExhcmdlRmlsZXMiLCJtaW5TaXplIiwicmVzcG9uc2UiLCJmZXRjaCIsInJlc3VsdCIsImpzb24iLCJzdWNjZXNzIiwiZmlsZXNXaXRoRGF0ZXMiLCJkYXRhIiwiZmlsZXMiLCJtYXAiLCJmaWxlIiwibGFzdE1vZGlmaWVkIiwiRGF0ZSIsImVycm9yIiwiY29uc29sZSIsImhhbmRsZU1pblNpemVDaGFuZ2UiLCJuZXdNaW5TaXplIiwiZm9ybWF0VGltZXN0YW1wIiwidGltZXN0YW1wIiwidG9Mb2NhbGVTdHJpbmciLCJ5ZWFyIiwibW9udGgiLCJkYXkiLCJob3VyIiwibWludXRlIiwiZ2V0RmlsdGVyZWRBbmRTb3J0ZWRGaWxlcyIsImZpbHRlcmVkIiwiZmlsdGVyIiwibmFtZSIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJkaXJlY3RvcnkiLCJzb3J0IiwiYSIsImIiLCJjb21wYXJpc29uIiwic2l6ZSIsImxvY2FsZUNvbXBhcmUiLCJnZXRUaW1lIiwiZ2V0VG90YWxTaXplIiwicmVkdWNlIiwidG90YWwiLCJmb3JtYXRGaWxlU2l6ZSIsImJ5dGVzIiwiayIsInNpemVzIiwiaSIsIk1hdGgiLCJmbG9vciIsImxvZyIsInBhcnNlRmxvYXQiLCJwb3ciLCJ0b0ZpeGVkIiwiZmlsdGVyZWRGaWxlcyIsImRpdiIsImNsYXNzTmFtZSIsInNwYW4iLCJoMiIsImNvdW50IiwiYnV0dG9uIiwib25DbGljayIsImRpc2FibGVkIiwibGFiZWwiLCJzZWxlY3QiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsIk51bWJlciIsInRhcmdldCIsIm9wdGlvbiIsImlucHV0IiwidHlwZSIsInBsYWNlaG9sZGVyIiwibGVuZ3RoIiwicCIsImluZGV4Iiwic2l6ZUZvcm1hdHRlZCIsInBhdGgiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LargeFilesPanel.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/SystemErrorsPanel.tsx":
/*!**********************************************!*\
  !*** ./src/components/SystemErrorsPanel.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SystemErrorsPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Clock_RefreshCw_Server_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Clock,RefreshCw,Server!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Clock_RefreshCw_Server_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Clock,RefreshCw,Server!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Clock_RefreshCw_Server_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Clock,RefreshCw,Server!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Clock_RefreshCw_Server_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Clock,RefreshCw,Server!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Clock_RefreshCw_Server_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Clock,RefreshCw,Server!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SystemErrorsPanel() {\n    _s();\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fetchErrors = async ()=>{\n        try {\n            setRefreshing(true);\n            const response = await fetch('/api/system-analysis?type=errors');\n            const result = await response.json();\n            if (result.success) {\n                // تحويل التواريخ من string إلى Date\n                const errorsWithDates = result.data.map((error)=>({\n                        ...error,\n                        timestamp: new Date(error.timestamp)\n                    }));\n                setErrors(errorsWithDates);\n            }\n        } catch (error) {\n            console.error('خطأ في جلب أخطاء النظام:', error);\n        } finally{\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SystemErrorsPanel.useEffect\": ()=>{\n            fetchErrors();\n            // تحديث كل 5 دقائق\n            const interval = setInterval(fetchErrors, 5 * 60 * 1000);\n            return ({\n                \"SystemErrorsPanel.useEffect\": ()=>clearInterval(interval)\n            })[\"SystemErrorsPanel.useEffect\"];\n        }\n    }[\"SystemErrorsPanel.useEffect\"], []);\n    const getErrorIcon = (level)=>{\n        switch(level){\n            case 'critical':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Clock_RefreshCw_Server_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-5 h-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Clock_RefreshCw_Server_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-5 h-5 text-orange-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 16\n                }, this);\n            case 'warning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Clock_RefreshCw_Server_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-5 h-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Clock_RefreshCw_Server_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-5 h-5 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getErrorBadgeColor = (level)=>{\n        switch(level){\n            case 'critical':\n                return 'bg-red-100 text-red-800 border-red-200';\n            case 'error':\n                return 'bg-orange-100 text-orange-800 border-orange-200';\n            case 'warning':\n                return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n            default:\n                return 'bg-gray-100 text-gray-800 border-gray-200';\n        }\n    };\n    const formatTimestamp = (timestamp)=>{\n        return timestamp.toLocaleString('en-US', {\n            year: 'numeric',\n            month: '2-digit',\n            day: '2-digit',\n            hour: '2-digit',\n            minute: '2-digit',\n            second: '2-digit'\n        });\n    };\n    const truncateMessage = function(message) {\n        let maxLength = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 100;\n        if (message.length <= maxLength) return message;\n        return message.substring(0, maxLength) + '...';\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Clock_RefreshCw_Server_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"mr-3 text-gray-600\",\n                        children: \"جاري تحميل أخطاء النظام...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Clock_RefreshCw_Server_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-6 h-6 text-red-500 ml-3\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-gray-800\",\n                                    children: \"أخطاء النظام\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-3 px-2 py-1 bg-red-100 text-red-800 text-sm rounded-full\",\n                                    children: [\n                                        errors.length,\n                                        \" خطأ\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: fetchErrors,\n                            disabled: refreshing,\n                            className: \"flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Clock_RefreshCw_Server_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4 ml-2 \".concat(refreshing ? 'animate-spin' : '')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                \"تحديث\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: errors.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Clock_RefreshCw_Server_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-lg\",\n                            children: \"لا توجد أخطاء في النظام\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: \"هذا أمر جيد! النظام يعمل بشكل طبيعي\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: errors.map((error)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 mt-1\",\n                                            children: getErrorIcon(error.level)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 text-xs font-medium rounded-full border \".concat(getErrorBadgeColor(error.level)),\n                                                            children: error.level === 'critical' ? 'حرج' : error.level === 'error' ? 'خطأ' : 'تحذير'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center mr-3 text-sm text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Clock_RefreshCw_Server_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"w-4 h-4 ml-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                                                                    lineNumber: 152,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                error.source\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center mr-3 text-sm text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Clock_RefreshCw_Server_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"w-4 h-4 ml-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                formatTimestamp(error.timestamp)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-800 mb-2\",\n                                                    children: truncateMessage(error.message)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 23\n                                                }, this),\n                                                error.details && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 bg-gray-50 p-2 rounded\",\n                                                    children: error.details\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 17\n                            }, this)\n                        }, error.id, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            errors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-sm text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"آخر تحديث: \",\n                                new Date().toLocaleString('en-US')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"إجمالي الأخطاء: \",\n                                errors.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n                lineNumber: 179,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\SystemErrorsPanel.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n_s(SystemErrorsPanel, \"Aov0QkFHGWK2XihMb+0SeeHxblo=\");\n_c = SystemErrorsPanel;\nvar _c;\n$RefreshReg$(_c, \"SystemErrorsPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SystemErrorsPanel.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/TempFilesPanel.tsx":
/*!*******************************************!*\
  !*** ./src/components/TempFilesPanel.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TempFilesPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction TempFilesPanel() {\n    _s();\n    const [tempData, setTempData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFiles, setSelectedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [deleting, setDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('size');\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('desc');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [itemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(25); // تقليل عدد العناصر لتحسين الأداء\n    const [showOnlyDeletable, setShowOnlyDeletable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchTempFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TempFilesPanel.useCallback[fetchTempFiles]\": async ()=>{\n            try {\n                setRefreshing(true);\n                const response = await fetch('/api/system-analysis?type=temp-files');\n                const result = await response.json();\n                if (result.success) {\n                    // تحويل التواريخ من string إلى Date\n                    const filesWithDates = result.data.files.map({\n                        \"TempFilesPanel.useCallback[fetchTempFiles].filesWithDates\": (file)=>({\n                                ...file,\n                                lastModified: new Date(file.lastModified)\n                            })\n                    }[\"TempFilesPanel.useCallback[fetchTempFiles].filesWithDates\"]);\n                    setTempData({\n                        ...result.data,\n                        files: filesWithDates\n                    });\n                    setSelectedFiles(new Set()); // مسح التحديد عند التحديث\n                }\n            } catch (error) {\n                console.error('خطأ في جلب الملفات المؤقتة:', error);\n            } finally{\n                setLoading(false);\n                setRefreshing(false);\n            }\n        }\n    }[\"TempFilesPanel.useCallback[fetchTempFiles]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TempFilesPanel.useEffect\": ()=>{\n            fetchTempFiles();\n        }\n    }[\"TempFilesPanel.useEffect\"], [\n        fetchTempFiles\n    ]);\n    // تحسين التحديد باستخدام useCallback لمنع إعادة الرندر غير الضرورية\n    const toggleFileSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TempFilesPanel.useCallback[toggleFileSelection]\": (filePath)=>{\n            setSelectedFiles({\n                \"TempFilesPanel.useCallback[toggleFileSelection]\": (prev)=>{\n                    const newSelected = new Set(prev);\n                    if (newSelected.has(filePath)) {\n                        newSelected.delete(filePath);\n                    } else {\n                        newSelected.add(filePath);\n                    }\n                    return newSelected;\n                }\n            }[\"TempFilesPanel.useCallback[toggleFileSelection]\"]);\n        }\n    }[\"TempFilesPanel.useCallback[toggleFileSelection]\"], []);\n    // تحسين الفلترة والترتيب باستخدام useMemo\n    const filteredAndSortedFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TempFilesPanel.useMemo[filteredAndSortedFiles]\": ()=>{\n            if (!tempData) return [];\n            let filtered = tempData.files;\n            // فلترة الملفات القابلة للحذف فقط\n            if (showOnlyDeletable) {\n                filtered = filtered.filter({\n                    \"TempFilesPanel.useMemo[filteredAndSortedFiles]\": (file)=>file.canDelete\n                }[\"TempFilesPanel.useMemo[filteredAndSortedFiles]\"]);\n            }\n            // البحث النصي\n            if (searchTerm) {\n                const lowerSearchTerm = searchTerm.toLowerCase();\n                filtered = filtered.filter({\n                    \"TempFilesPanel.useMemo[filteredAndSortedFiles]\": (file)=>file.name.toLowerCase().includes(lowerSearchTerm) || file.path.toLowerCase().includes(lowerSearchTerm)\n                }[\"TempFilesPanel.useMemo[filteredAndSortedFiles]\"]);\n            }\n            // الترتيب\n            filtered.sort({\n                \"TempFilesPanel.useMemo[filteredAndSortedFiles]\": (a, b)=>{\n                    let comparison = 0;\n                    switch(sortBy){\n                        case 'name':\n                            comparison = a.name.localeCompare(b.name);\n                            break;\n                        case 'size':\n                            comparison = a.size - b.size;\n                            break;\n                        case 'date':\n                            comparison = a.lastModified.getTime() - b.lastModified.getTime();\n                            break;\n                    }\n                    return sortOrder === 'desc' ? -comparison : comparison;\n                }\n            }[\"TempFilesPanel.useMemo[filteredAndSortedFiles]\"]);\n            return filtered;\n        }\n    }[\"TempFilesPanel.useMemo[filteredAndSortedFiles]\"], [\n        tempData,\n        searchTerm,\n        sortBy,\n        sortOrder,\n        showOnlyDeletable\n    ]);\n    // حساب الصفحات\n    const totalPages = Math.ceil(filteredAndSortedFiles.length / itemsPerPage);\n    const paginatedFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TempFilesPanel.useMemo[paginatedFiles]\": ()=>{\n            const startIndex = (currentPage - 1) * itemsPerPage;\n            return filteredAndSortedFiles.slice(startIndex, startIndex + itemsPerPage);\n        }\n    }[\"TempFilesPanel.useMemo[paginatedFiles]\"], [\n        filteredAndSortedFiles,\n        currentPage,\n        itemsPerPage\n    ]);\n    const selectAllVisibleFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TempFilesPanel.useCallback[selectAllVisibleFiles]\": ()=>{\n            const visiblePaths = paginatedFiles.filter({\n                \"TempFilesPanel.useCallback[selectAllVisibleFiles].visiblePaths\": (file)=>file.canDelete\n            }[\"TempFilesPanel.useCallback[selectAllVisibleFiles].visiblePaths\"]).map({\n                \"TempFilesPanel.useCallback[selectAllVisibleFiles].visiblePaths\": (file)=>file.path\n            }[\"TempFilesPanel.useCallback[selectAllVisibleFiles].visiblePaths\"]);\n            setSelectedFiles({\n                \"TempFilesPanel.useCallback[selectAllVisibleFiles]\": (prev)=>{\n                    const newSelected = new Set(prev);\n                    const allVisibleSelected = visiblePaths.every({\n                        \"TempFilesPanel.useCallback[selectAllVisibleFiles].allVisibleSelected\": (path)=>newSelected.has(path)\n                    }[\"TempFilesPanel.useCallback[selectAllVisibleFiles].allVisibleSelected\"]);\n                    if (allVisibleSelected) {\n                        // إلغاء تحديد الملفات المرئية\n                        visiblePaths.forEach({\n                            \"TempFilesPanel.useCallback[selectAllVisibleFiles]\": (path)=>newSelected.delete(path)\n                        }[\"TempFilesPanel.useCallback[selectAllVisibleFiles]\"]);\n                    } else {\n                        // تحديد الملفات المرئية\n                        visiblePaths.forEach({\n                            \"TempFilesPanel.useCallback[selectAllVisibleFiles]\": (path)=>newSelected.add(path)\n                        }[\"TempFilesPanel.useCallback[selectAllVisibleFiles]\"]);\n                    }\n                    return newSelected;\n                }\n            }[\"TempFilesPanel.useCallback[selectAllVisibleFiles]\"]);\n        }\n    }[\"TempFilesPanel.useCallback[selectAllVisibleFiles]\"], [\n        paginatedFiles\n    ]);\n    const selectAllDeletableFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TempFilesPanel.useCallback[selectAllDeletableFiles]\": ()=>{\n            if (!tempData) return;\n            const deletableFiles = filteredAndSortedFiles.filter({\n                \"TempFilesPanel.useCallback[selectAllDeletableFiles].deletableFiles\": (file)=>file.canDelete\n            }[\"TempFilesPanel.useCallback[selectAllDeletableFiles].deletableFiles\"]).map({\n                \"TempFilesPanel.useCallback[selectAllDeletableFiles].deletableFiles\": (file)=>file.path\n            }[\"TempFilesPanel.useCallback[selectAllDeletableFiles].deletableFiles\"]);\n            setSelectedFiles(new Set(deletableFiles));\n        }\n    }[\"TempFilesPanel.useCallback[selectAllDeletableFiles]\"], [\n        filteredAndSortedFiles,\n        tempData\n    ]);\n    const clearSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TempFilesPanel.useCallback[clearSelection]\": ()=>{\n            setSelectedFiles(new Set());\n        }\n    }[\"TempFilesPanel.useCallback[clearSelection]\"], []);\n    const deleteSelectedFiles = async ()=>{\n        if (selectedFiles.size === 0) return;\n        try {\n            setDeleting(true);\n            const response = await fetch('/api/system-analysis', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'delete-temp-files',\n                    filePaths: Array.from(selectedFiles)\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                alert(\"تم حذف \".concat(result.data.success.length, \" ملف بنجاح\"));\n                if (result.data.failed.length > 0) {\n                    alert(\"فشل في حذف \".concat(result.data.failed.length, \" ملف\"));\n                }\n                // تحديث القائمة\n                await fetchTempFiles();\n                setSelectedFiles(new Set());\n            }\n        } catch (error) {\n            console.error('خطأ في حذف الملفات:', error);\n            alert('فشل في حذف الملفات');\n        } finally{\n            setDeleting(false);\n        }\n    };\n    const formatTimestamp = (timestamp)=>{\n        return timestamp.toLocaleString('en-US', {\n            year: 'numeric',\n            month: '2-digit',\n            day: '2-digit',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    const getSelectedSize = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TempFilesPanel.useMemo[getSelectedSize]\": ()=>{\n            if (!tempData) return 0;\n            return tempData.files.filter({\n                \"TempFilesPanel.useMemo[getSelectedSize]\": (file)=>selectedFiles.has(file.path)\n            }[\"TempFilesPanel.useMemo[getSelectedSize]\"]).reduce({\n                \"TempFilesPanel.useMemo[getSelectedSize]\": (total, file)=>{\n                    // إضافة حجم الملف مضروباً في عدد النسخ المكررة\n                    const multiplier = file.isDuplicate && file.duplicateCount ? file.duplicateCount : 1;\n                    return total + file.size * multiplier;\n                }\n            }[\"TempFilesPanel.useMemo[getSelectedSize]\"], 0);\n        }\n    }[\"TempFilesPanel.useMemo[getSelectedSize]\"], [\n        tempData,\n        selectedFiles\n    ]);\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 B';\n        const k = 1024;\n        const sizes = [\n            'B',\n            'KB',\n            'MB',\n            'GB',\n            'TB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"mr-3 text-gray-600\",\n                        children: \"جاري تحميل الملفات المؤقتة...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                lineNumber: 231,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n            lineNumber: 230,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-6 h-6 text-orange-500 ml-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-800\",\n                                        children: \"الملفات المؤقتة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this),\n                                    tempData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-3 px-2 py-1 bg-orange-100 text-orange-800 text-sm rounded-full\",\n                                        children: [\n                                            tempData.count,\n                                            \" ملف\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: fetchTempFiles,\n                                disabled: refreshing,\n                                className: \"flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-4 h-4 ml-2 \".concat(refreshing ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"تحديث\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this),\n                    tempData && tempData.files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"البحث في الملفات...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>{\n                                            setSearchTerm(e.target.value);\n                                            setCurrentPage(1); // العودة للصفحة الأولى عند البحث\n                                        },\n                                        className: \"w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: sortBy,\n                                        onChange: (e)=>setSortBy(e.target.value),\n                                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"size\",\n                                                children: \"ترتيب حسب الحجم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"name\",\n                                                children: \"ترتيب حسب الاسم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"date\",\n                                                children: \"ترتيب حسب التاريخ\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc'),\n                                        className: \"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                        title: sortOrder === 'asc' ? 'تصاعدي' : 'تنازلي',\n                                        children: sortOrder === 'asc' ? '↑' : '↓'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: showOnlyDeletable,\n                                            onChange: (e)=>{\n                                                setShowOnlyDeletable(e.target.checked);\n                                                setCurrentPage(1);\n                                            },\n                                            className: \"ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-700\",\n                                            children: \"الملفات القابلة للحذف فقط\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 border border-green-200 rounded-lg p-4 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5 text-green-600 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-green-800\",\n                                            children: \"ملفات آمنة للحذف\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-700 mt-1\",\n                                            children: \"يعرض النظام الملفات المؤقتة الآمنة للحذف فقط. تم استبعاد ملفات قواعد البيانات والملفات الحساسة تلقائياً لحماية النظام.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, this),\n                    tempData && filteredAndSortedFiles.some((f)=>f.isDuplicate && f.duplicateCount && f.duplicateCount > 1) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5 text-yellow-600 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-yellow-800\",\n                                            children: \"ملفات مكررة تم دمجها\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-yellow-700 mt-1\",\n                                            children: \"تم دمج الملفات المتشابهة (نفس الاسم والحجم) في عنصر واحد. عند الحذف، سيتم حذف جميع النسخ المكررة تلقائياً.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, this),\n                    tempData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-5 gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-orange-600\",\n                                        children: \"إجمالي الحجم\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-800\",\n                                        children: tempData.totalSizeFormatted\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-blue-600\",\n                                        children: \"الملفات المعروضة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-800\",\n                                        children: filteredAndSortedFiles.length\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-yellow-600\",\n                                        children: \"ملفات مكررة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-800\",\n                                        children: filteredAndSortedFiles.filter((f)=>f.isDuplicate && f.duplicateCount && f.duplicateCount > 1).length\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-purple-600\",\n                                        children: \"الملفات المحددة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-800\",\n                                        children: selectedFiles.size\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-green-600\",\n                                        children: \"حجم المحدد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-800\",\n                                        children: formatFileSize(getSelectedSize)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 11\n                    }, this),\n                    tempData && filteredAndSortedFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: selectAllVisibleFiles,\n                                        className: \"flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"تحديد الصفحة الحالية\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: selectAllDeletableFiles,\n                                        className: \"flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"تحديد الكل (\",\n                                            filteredAndSortedFiles.filter((f)=>f.canDelete).length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: clearSelection,\n                                        className: \"flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إلغاء التحديد\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: deleteSelectedFiles,\n                                        disabled: selectedFiles.size === 0 || deleting,\n                                        className: \"flex items-center px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50\",\n                                        children: [\n                                            deleting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, this),\n                                            deleting ? 'جاري الحذف...' : \"حذف المحدد (\".concat(selectedFiles.size, \")\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this),\n                            totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"صفحة \",\n                                    currentPage,\n                                    \" من \",\n                                    totalPages,\n                                    \" (\",\n                                    filteredAndSortedFiles.length,\n                                    \" ملف)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: !tempData || tempData.files.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-lg\",\n                            children: \"لا توجد ملفات مؤقتة\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: \"النظام نظيف من الملفات المؤقتة\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 11\n                }, this) : filteredAndSortedFiles.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-lg\",\n                            children: \"لا توجد نتائج\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: \"جرب تغيير معايير البحث أو الفلترة\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                    lineNumber: 433,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: paginatedFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow \".concat(selectedFiles.has(file.path) ? 'bg-blue-50 border-blue-300' : ''),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>toggleFileSelection(file.path),\n                                                        className: \"ml-3\",\n                                                        disabled: !file.canDelete,\n                                                        children: selectedFiles.has(file.path) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-5 h-5 text-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-5 h-5 \".concat(file.canDelete ? 'text-gray-400 hover:text-blue-500' : 'text-gray-300')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-5 h-5 text-gray-400 ml-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-gray-800\",\n                                                                        children: file.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    file.isDuplicate && file.duplicateCount && file.duplicateCount > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-2 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full\",\n                                                                        children: [\n                                                                            file.duplicateCount,\n                                                                            \" نسخ\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                        lineNumber: 465,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 truncate max-w-md\",\n                                                                children: file.path\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            file.isDuplicate && file.duplicatePaths && file.duplicatePaths.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-orange-600 mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                                                    className: \"cursor-pointer\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                                            className: \"hover:text-orange-800\",\n                                                                            children: [\n                                                                                \"عرض جميع المواقع (\",\n                                                                                file.duplicatePaths.length,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                            lineNumber: 474,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-1 pr-4 max-h-32 overflow-y-auto\",\n                                                                            children: file.duplicatePaths.map((path, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs text-gray-500 py-1 border-r-2 border-orange-200 pr-2\",\n                                                                                    children: path\n                                                                                }, index, false, {\n                                                                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                                    lineNumber: 477,\n                                                                                    columnNumber: 35\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                            lineNumber: 475,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: [\n                                                                    \"آخر تعديل: \",\n                                                                    formatTimestamp(file.lastModified)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-end\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: file.sizeFormatted\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    file.isDuplicate && file.duplicateCount && file.duplicateCount > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-orange-600\",\n                                                        children: [\n                                                            \"إجمالي: \",\n                                                            formatFileSize(file.size * file.duplicateCount)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    !file.canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4 text-yellow-500 mt-1\",\n                                                        title: \"لا يمكن حذف هذا الملف\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 19\n                                    }, this)\n                                }, file.path, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 13\n                        }, this),\n                        totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mt-6 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage(Math.max(1, currentPage - 1)),\n                                    disabled: currentPage === 1,\n                                    className: \"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"السابق\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-1\",\n                                    children: Array.from({\n                                        length: Math.min(5, totalPages)\n                                    }, (_, i)=>{\n                                        let pageNum;\n                                        if (totalPages <= 5) {\n                                            pageNum = i + 1;\n                                        } else if (currentPage <= 3) {\n                                            pageNum = i + 1;\n                                        } else if (currentPage >= totalPages - 2) {\n                                            pageNum = totalPages - 4 + i;\n                                        } else {\n                                            pageNum = currentPage - 2 + i;\n                                        }\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentPage(pageNum),\n                                            className: \"px-3 py-2 rounded-lg \".concat(currentPage === pageNum ? 'bg-blue-500 text-white' : 'border border-gray-300 hover:bg-gray-50'),\n                                            children: pageNum\n                                        }, pageNum, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 23\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage(Math.min(totalPages, currentPage + 1)),\n                                    disabled: currentPage === totalPages,\n                                    className: \"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"التالي\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                lineNumber: 425,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n_s(TempFilesPanel, \"Cri2UDBcltCK6QvRS0pgJjCAcko=\");\n_c = TempFilesPanel;\nvar _c;\n$RefreshReg$(_c, \"TempFilesPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TempFilesPanel.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmoneer%5C%5Cfolders%5C%5Ccopy%20working%5C%5Cserver_control%5C%5Cserver-monitor%5C%5Csrc%5C%5Capp%5C%5Csystem-analysis%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);