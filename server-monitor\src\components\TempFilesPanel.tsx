'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Trash2, RefreshCw, FolderOpen, File, CheckSquare, Square, AlertCircle, Loader2, Search, Filter } from 'lucide-react';

interface TempFile {
  path: string;
  name: string;
  size: number;
  sizeFormatted: string;
  lastModified: Date;
  canDelete: boolean;
}

interface TempFilesData {
  files: TempFile[];
  totalSize: number;
  totalSizeFormatted: string;
  count: number;
}

export default function TempFilesPanel() {
  const [tempData, setTempData] = useState<TempFilesData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());
  const [deleting, setDeleting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'size' | 'date'>('size');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(25); // تقليل عدد العناصر لتحسين الأداء
  const [showOnlyDeletable, setShowOnlyDeletable] = useState(true);

  const fetchTempFiles = useCallback(async () => {
    try {
      setRefreshing(true);
      const response = await fetch('/api/system-analysis?type=temp-files');
      const result = await response.json();

      if (result.success) {
        // تحويل التواريخ من string إلى Date
        const filesWithDates = result.data.files.map((file: any) => ({
          ...file,
          lastModified: new Date(file.lastModified)
        }));

        setTempData({
          ...result.data,
          files: filesWithDates
        });
        setSelectedFiles(new Set()); // مسح التحديد عند التحديث
      }
    } catch (error) {
      console.error('خطأ في جلب الملفات المؤقتة:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    fetchTempFiles();
  }, [fetchTempFiles]);

  // تحسين التحديد باستخدام useCallback لمنع إعادة الرندر غير الضرورية
  const toggleFileSelection = useCallback((filePath: string) => {
    setSelectedFiles(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(filePath)) {
        newSelected.delete(filePath);
      } else {
        newSelected.add(filePath);
      }
      return newSelected;
    });
  }, []);

  // تحسين الفلترة والترتيب باستخدام useMemo
  const filteredAndSortedFiles = useMemo(() => {
    if (!tempData) return [];

    let filtered = tempData.files;

    // فلترة الملفات القابلة للحذف فقط
    if (showOnlyDeletable) {
      filtered = filtered.filter(file => file.canDelete);
    }

    // البحث النصي
    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      filtered = filtered.filter(file =>
        file.name.toLowerCase().includes(lowerSearchTerm) ||
        file.path.toLowerCase().includes(lowerSearchTerm)
      );
    }

    // الترتيب
    filtered.sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'size':
          comparison = a.size - b.size;
          break;
        case 'date':
          comparison = a.lastModified.getTime() - b.lastModified.getTime();
          break;
      }
      return sortOrder === 'desc' ? -comparison : comparison;
    });

    return filtered;
  }, [tempData, searchTerm, sortBy, sortOrder, showOnlyDeletable]);

  // حساب الصفحات
  const totalPages = Math.ceil(filteredAndSortedFiles.length / itemsPerPage);
  const paginatedFiles = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredAndSortedFiles.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredAndSortedFiles, currentPage, itemsPerPage]);

  const selectAllVisibleFiles = useCallback(() => {
    const visiblePaths = paginatedFiles
      .filter(file => file.canDelete)
      .map(file => file.path);

    setSelectedFiles(prev => {
      const newSelected = new Set(prev);
      const allVisibleSelected = visiblePaths.every(path => newSelected.has(path));

      if (allVisibleSelected) {
        // إلغاء تحديد الملفات المرئية
        visiblePaths.forEach(path => newSelected.delete(path));
      } else {
        // تحديد الملفات المرئية
        visiblePaths.forEach(path => newSelected.add(path));
      }
      return newSelected;
    });
  }, [paginatedFiles]);

  const selectAllDeletableFiles = useCallback(() => {
    if (!tempData) return;

    const deletableFiles = filteredAndSortedFiles
      .filter(file => file.canDelete)
      .map(file => file.path);

    setSelectedFiles(new Set(deletableFiles));
  }, [filteredAndSortedFiles, tempData]);

  const clearSelection = useCallback(() => {
    setSelectedFiles(new Set());
  }, []);

  const deleteSelectedFiles = async () => {
    if (selectedFiles.size === 0) return;

    // تأكيد الحذف
    const selectedCount = selectedFiles.size;
    const totalFilesToDelete = Array.from(selectedFiles).reduce((total, filePath) => {
      const file = tempData?.files.find(f => f.path === filePath);
      return total + (file?.duplicateCount || 1);
    }, 0);

    const confirmMessage = `هل أنت متأكد من حذف ${selectedCount} ملف؟\n` +
      (totalFilesToDelete > selectedCount ? `سيتم حذف ${totalFilesToDelete} ملف إجمالي (بما في ذلك النسخ المكررة)` : '');

    if (!confirm(confirmMessage)) {
      return;
    }

    try {
      setDeleting(true);

      // إضافة timeout للطلب
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 ثانية

      const response = await fetch('/api/system-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'delete-temp-files',
          filePaths: Array.from(selectedFiles)
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        const successCount = result.data.success.length;
        const failedCount = result.data.failed.length;

        // عرض نتائج مفصلة
        let message = `✅ تم حذف ${successCount} ملف بنجاح`;
        if (failedCount > 0) {
          message += `\n❌ فشل في حذف ${failedCount} ملف`;

          // عرض تفاصيل الملفات الفاشلة
          if (result.data.failed.length <= 5) {
            message += '\n\nالملفات الفاشلة:';
            result.data.failed.forEach((file: string) => {
              message += `\n• ${file.split('\\').pop() || file}`;
            });
          }
        }

        alert(message);

        // تحديث فوري للقائمة
        setSelectedFiles(new Set());

        // إعادة تحميل البيانات مع مؤشر تحميل
        setLoading(true);
        await fetchTempFiles();

        // تحديث إضافي بعد ثانية للتأكد
        setTimeout(async () => {
          await fetchTempFiles();
        }, 1000);

      } else {
        throw new Error(result.message || 'فشل في حذف الملفات');
      }
    } catch (error) {
      console.error('خطأ في حذف الملفات:', error);

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          alert('انتهت مهلة الحذف. يرجى المحاولة مرة أخرى.');
        } else {
          alert(`فشل في حذف الملفات: ${error.message}`);
        }
      } else {
        alert('فشل في حذف الملفات. يرجى المحاولة مرة أخرى.');
      }

      // إعادة تحميل البيانات حتى في حالة الخطأ
      await fetchTempFiles();
    } finally {
      setDeleting(false);
      setLoading(false);
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getSelectedSize = useMemo(() => {
    if (!tempData) return 0;

    return tempData.files
      .filter(file => selectedFiles.has(file.path))
      .reduce((total, file) => {
        // إضافة حجم الملف مضروباً في عدد النسخ المكررة
        const multiplier = file.isDuplicate && file.duplicateCount ? file.duplicateCount : 1;
        return total + (file.size * multiplier);
      }, 0);
  }, [tempData, selectedFiles]);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-center h-32">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-500" />
          <span className="mr-3 text-gray-600">جاري تحميل الملفات المؤقتة...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <FolderOpen className="w-6 h-6 text-orange-500 ml-3" />
            <h2 className="text-xl font-bold text-gray-800">الملفات المؤقتة</h2>
            {tempData && (
              <span className="mr-3 px-2 py-1 bg-orange-100 text-orange-800 text-sm rounded-full">
                {tempData.count} ملف
              </span>
            )}
          </div>
          <button
            onClick={fetchTempFiles}
            disabled={refreshing || loading || deleting}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ml-2 ${(refreshing || loading) ? 'animate-spin' : ''}`} />
            {refreshing || loading ? 'جاري التحديث...' : 'تحديث'}
          </button>
        </div>

        {/* Search and Filter */}
        {tempData && tempData.files.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="البحث في الملفات..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  setCurrentPage(1); // العودة للصفحة الأولى عند البحث
                }}
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Sort */}
            <div className="flex gap-2">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as 'name' | 'size' | 'date')}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="size">ترتيب حسب الحجم</option>
                <option value="name">ترتيب حسب الاسم</option>
                <option value="date">ترتيب حسب التاريخ</option>
              </select>
              <button
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                title={sortOrder === 'asc' ? 'تصاعدي' : 'تنازلي'}
              >
                {sortOrder === 'asc' ? '↑' : '↓'}
              </button>
            </div>

            {/* Filter */}
            <div className="flex items-center">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={showOnlyDeletable}
                  onChange={(e) => {
                    setShowOnlyDeletable(e.target.checked);
                    setCurrentPage(1);
                  }}
                  className="ml-2"
                />
                <span className="text-sm text-gray-700">الملفات القابلة للحذف فقط</span>
              </label>
            </div>
          </div>
        )}

        {/* Safety Notice */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-green-600 ml-2" />
            <div>
              <h3 className="text-sm font-medium text-green-800">ملفات آمنة للحذف</h3>
              <p className="text-sm text-green-700 mt-1">
                يعرض النظام الملفات المؤقتة الآمنة للحذف فقط. تم استبعاد ملفات قواعد البيانات والملفات الحساسة تلقائياً لحماية النظام.
              </p>
            </div>
          </div>
        </div>

        {/* Status Messages */}
        {deleting && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <div className="flex items-center">
              <Loader2 className="w-5 h-5 text-blue-600 ml-2 animate-spin" />
              <div>
                <h3 className="text-sm font-medium text-blue-800">جاري حذف الملفات...</h3>
                <p className="text-sm text-blue-700 mt-1">
                  يتم حذف الملفات المحددة وجميع النسخ المكررة. يرجى الانتظار...
                </p>
              </div>
            </div>
          </div>
        )}

        {(loading || refreshing) && !deleting && (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4">
            <div className="flex items-center">
              <Loader2 className="w-5 h-5 text-gray-600 ml-2 animate-spin" />
              <div>
                <h3 className="text-sm font-medium text-gray-800">جاري تحديث القائمة...</h3>
                <p className="text-sm text-gray-700 mt-1">
                  يتم تحديث قائمة الملفات المؤقتة...
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Duplicate Files Notice */}
        {tempData && filteredAndSortedFiles.some(f => f.isDuplicate && f.duplicateCount && f.duplicateCount > 1) && !deleting && !loading && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-yellow-600 ml-2" />
              <div>
                <h3 className="text-sm font-medium text-yellow-800">ملفات مكررة تم دمجها</h3>
                <p className="text-sm text-yellow-700 mt-1">
                  تم دمج الملفات المتشابهة (نفس الاسم والحجم) في عنصر واحد. عند الحذف، سيتم حذف جميع النسخ المكررة تلقائياً.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Statistics */}
        {tempData && (
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="text-sm text-orange-600">إجمالي الحجم</div>
              <div className="text-2xl font-bold text-orange-800">{tempData.totalSizeFormatted}</div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-sm text-blue-600">الملفات المعروضة</div>
              <div className="text-2xl font-bold text-blue-800">{filteredAndSortedFiles.length}</div>
            </div>
            <div className="bg-yellow-50 p-4 rounded-lg">
              <div className="text-sm text-yellow-600">ملفات مكررة</div>
              <div className="text-2xl font-bold text-yellow-800">
                {filteredAndSortedFiles.filter(f => f.isDuplicate && f.duplicateCount && f.duplicateCount > 1).length}
              </div>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-sm text-purple-600">الملفات المحددة</div>
              <div className="text-2xl font-bold text-purple-800">{selectedFiles.size}</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-sm text-green-600">حجم المحدد</div>
              <div className="text-2xl font-bold text-green-800">{formatFileSize(getSelectedSize)}</div>
            </div>
          </div>
        )}

        {/* Actions */}
        {tempData && filteredAndSortedFiles.length > 0 && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <button
                onClick={selectAllVisibleFiles}
                className="flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
              >
                <CheckSquare className="w-4 h-4 ml-2" />
                تحديد الصفحة الحالية
              </button>
              <button
                onClick={selectAllDeletableFiles}
                className="flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200"
              >
                <CheckSquare className="w-4 h-4 ml-2" />
                تحديد الكل ({filteredAndSortedFiles.filter(f => f.canDelete).length})
              </button>
              <button
                onClick={clearSelection}
                className="flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
              >
                <Square className="w-4 h-4 ml-2" />
                إلغاء التحديد
              </button>
              <button
                onClick={deleteSelectedFiles}
                disabled={selectedFiles.size === 0 || deleting}
                className="flex items-center px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50"
              >
                {deleting ? (
                  <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                ) : (
                  <Trash2 className="w-4 h-4 ml-2" />
                )}
                {deleting ? 'جاري الحذف...' : `حذف المحدد (${selectedFiles.size})`}
              </button>
            </div>

            {/* Pagination Info */}
            {totalPages > 1 && (
              <div className="text-sm text-gray-600">
                صفحة {currentPage} من {totalPages} ({filteredAndSortedFiles.length} ملف)
              </div>
            )}
          </div>
        )}
      </div>

      {/* Content */}
      <div className="p-6">
        {!tempData || tempData.files.length === 0 ? (
          <div className="text-center py-8">
            <File className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 text-lg">لا توجد ملفات مؤقتة</p>
            <p className="text-gray-400 text-sm">النظام نظيف من الملفات المؤقتة</p>
          </div>
        ) : filteredAndSortedFiles.length === 0 ? (
          <div className="text-center py-8">
            <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 text-lg">لا توجد نتائج</p>
            <p className="text-gray-400 text-sm">جرب تغيير معايير البحث أو الفلترة</p>
          </div>
        ) : (
          <>
            <div className="space-y-2">
              {paginatedFiles.map((file) => (
                <div
                  key={file.path}
                  className={`border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow ${selectedFiles.has(file.path) ? 'bg-blue-50 border-blue-300' : ''
                    }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center flex-1">
                      <button
                        onClick={() => toggleFileSelection(file.path)}
                        className="ml-3"
                        disabled={!file.canDelete}
                      >
                        {selectedFiles.has(file.path) ? (
                          <CheckSquare className="w-5 h-5 text-blue-500" />
                        ) : (
                          <Square className={`w-5 h-5 ${file.canDelete ? 'text-gray-400 hover:text-blue-500' : 'text-gray-300'}`} />
                        )}
                      </button>
                      <File className="w-5 h-5 text-gray-400 ml-3" />
                      <div className="flex-1">
                        <div className="flex items-center">
                          <span className="font-medium text-gray-800">{file.name}</span>
                          {file.isDuplicate && file.duplicateCount && file.duplicateCount > 1 && (
                            <span className="mr-2 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                              {file.duplicateCount} نسخ
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-500 truncate max-w-md">{file.path}</div>
                        {file.isDuplicate && file.duplicatePaths && file.duplicatePaths.length > 1 && (
                          <div className="text-xs text-orange-600 mt-1">
                            <details className="cursor-pointer">
                              <summary className="hover:text-orange-800">عرض جميع المواقع ({file.duplicatePaths.length})</summary>
                              <div className="mt-1 pr-4 max-h-32 overflow-y-auto">
                                {file.duplicatePaths.map((path, index) => (
                                  <div key={index} className="text-xs text-gray-500 py-1 border-r-2 border-orange-200 pr-2">
                                    {path}
                                  </div>
                                ))}
                              </div>
                            </details>
                          </div>
                        )}
                        <div className="text-xs text-gray-400">
                          آخر تعديل: {formatTimestamp(file.lastModified)}
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col items-end">
                      <span className="text-sm font-medium text-gray-600">
                        {file.sizeFormatted}
                      </span>
                      {file.isDuplicate && file.duplicateCount && file.duplicateCount > 1 && (
                        <span className="text-xs text-orange-600">
                          إجمالي: {formatFileSize(file.size * file.duplicateCount)}
                        </span>
                      )}
                      {!file.canDelete && (
                        <AlertCircle className="w-4 h-4 text-yellow-500 mt-1" title="لا يمكن حذف هذا الملف" />
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center mt-6 gap-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  السابق
                </button>

                {/* Page Numbers */}
                <div className="flex gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`px-3 py-2 rounded-lg ${currentPage === pageNum
                          ? 'bg-blue-500 text-white'
                          : 'border border-gray-300 hover:bg-gray-50'
                          }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  التالي
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
