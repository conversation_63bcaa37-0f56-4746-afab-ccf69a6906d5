# سجل التغييرات - نظام مراقبة السيرفرات

## الإصدار 1.2.0 - 2024-12-19

### 📄 تحويل التقارير إلى اللغة الإنجليزية
- **تحويل جميع نصوص التقارير** من العربية إلى الإنجليزية
- **إزالة نظام تحويل النصوص** المعقد (convertArabicText)
- **حل مشكلة الرموز المشوهة** في ملفات PDF نهائياً
- **تحسين استقرار التقارير** مع مكتبة jsPDF

#### الملفات المُحدثة:
- `src/lib/reportGenerator.ts` - تحويل كامل للنصوص الإنجليزية

## الإصدار 1.1.0 - 2024-12-19

### 🔧 التحسينات الرئيسية

#### تحديث نظام التاريخ
- **تغيير التاريخ إلى الميلادي**: تم تحديث جميع عروض التاريخ في النظام لتستخدم التقويم الميلادي بدلاً من الهجري
- **توحيد تنسيق التاريخ**: استخدام `toLocaleString('en-US')` في جميع أنحاء النظام
- **الملفات المُحدثة**:
  - `src/app/page.tsx` - الصفحة الرئيسية
  - `src/app/server/[id]/page.tsx` - صفحة تفاصيل السيرفر
  - `src/components/AlertsPanel.tsx` - لوحة التنبيهات
  - `src/components/ServerCard.tsx` - بطاقات السيرفرات
  - `src/components/PerformanceChart.tsx` - مخططات الأداء
  - `src/lib/reportGenerator.ts` - مولد التقارير
  - `src/lib/notifications.ts` - نظام الإشعارات

#### إصلاح مشكلة ترميز التقارير
- **حل مشكلة النصوص المشوهة**: إصلاح مشكلة ظهور النصوص العربية بشكل مشوه في تقارير PDF
- **نظام تحويل النصوص**: إضافة دالة `convertArabicText()` لتحويل النصوص العربية إلى إنجليزية في التقارير
- **تحسين جودة التقارير**: ضمان قابلية قراءة جميع النصوص في ملفات PDF

### 📋 التفاصيل التقنية

#### تحديثات الواجهة
```typescript
// قبل التحديث
آخر تحديث: {new Date().toLocaleString('ar-SA')} ({new Date().toLocaleString('en-US')})

// بعد التحديث
آخر تحديث: {new Date().toLocaleString('en-US')}
```

#### تحسينات التقارير
```typescript
// إضافة دالة تحويل النصوص
function convertArabicText(text: string): string {
  const arabicToEnglish: { [key: string]: string } = {
    'تقرير مراقبة السيرفرات': 'Server Monitoring Report',
    'ملخص عام': 'Executive Summary',
    'تفاصيل السيرفرات': 'Server Details',
    'التنبيهات النشطة': 'Active Alerts',
    'التوصيات': 'Recommendations',
    // ... المزيد من التحويلات
  };
  
  for (const [arabic, english] of Object.entries(arabicToEnglish)) {
    if (text.includes(arabic)) {
      text = text.replace(new RegExp(arabic, 'g'), english);
    }
  }
  
  return text;
}
```

### 🐛 الأخطاء المُصلحة

1. **مشكلة التاريخ المزدوج**: إزالة عرض التاريخ الهجري والاكتفاء بالميلادي
2. **ترميز النصوص في PDF**: حل مشكلة ظهور رموز غريبة مثل `þžþßþŽþÌþäþàþß`
3. **توحيد التنسيق**: ضمان استخدام نفس تنسيق التاريخ في جميع أجزاء النظام

### 📁 الملفات المُحدثة

#### الواجهة الأمامية
- `src/app/page.tsx` - تحديث عرض التاريخ في الصفحة الرئيسية
- `src/app/server/[id]/page.tsx` - تحديث عرض التاريخ في صفحة السيرفر
- `src/components/AlertsPanel.tsx` - تحديث عرض التاريخ في التنبيهات
- `src/components/ServerCard.tsx` - تحديث عرض التاريخ في بطاقات السيرفرات
- `src/components/PerformanceChart.tsx` - تحديث عرض التاريخ في مخططات الأداء

#### النظام الخلفي
- `src/lib/reportGenerator.ts` - إضافة نظام تحويل النصوص وإصلاح الترميز
- `src/lib/notifications.ts` - تحديث عرض التاريخ في الإشعارات

### 🔄 التوافق مع الإصدارات السابقة

- **البيانات المحفوظة**: جميع البيانات المحفوظة سابقاً متوافقة مع الإصدار الجديد
- **الإعدادات**: لا تحتاج إعدادات المستخدم لأي تغيير
- **API**: جميع نقاط API تعمل بنفس الطريقة

### 🚀 كيفية التحديث

1. **إيقاف النظام الحالي**:
```bash
# إذا كان يعمل في الخلفية
pm2 stop server-monitor

# أو إذا كان يعمل في المقدمة
Ctrl + C
```

2. **جلب التحديثات**:
```bash
git pull origin main
npm install
```

3. **إعادة تشغيل النظام**:
```bash
npm run dev
# أو للإنتاج
npm run build && npm start
```

### 📊 اختبار التحديثات

#### اختبار عرض التاريخ
1. افتح الصفحة الرئيسية: `http://localhost:3001`
2. تحقق من عرض التاريخ بالتنسيق الميلادي فقط
3. انتقل لصفحة تفاصيل أي سيرفر وتحقق من التاريخ
4. افتح لوحة التنبيهات وتحقق من تواريخ التنبيهات

#### اختبار التقارير
1. انتقل لصفحة التقارير: `http://localhost:3001/reports`
2. أنشئ تقرير سريع أو مخصص
3. تحقق من أن النصوص تظهر بشكل صحيح في ملف PDF
4. تأكد من عدم وجود رموز مشوهة

### 🔮 التحديثات المستقبلية

#### الإصدار 1.2.0 (مخطط)
- إضافة دعم تعدد اللغات الكامل
- تحسين أداء التقارير
- إضافة المزيد من أنواع الرسوم البيانية

#### الإصدار 1.3.0 (مخطط)
- دعم قواعد البيانات الخارجية
- نظام مصادقة متقدم
- تطبيق الهاتف المحمول

### 📞 الدعم والمساعدة

إذا واجهت أي مشاكل بعد التحديث:

1. **تحقق من السجلات**:
```bash
npm run dev
# راقب أي رسائل خطأ في وحدة التحكم
```

2. **إعادة تثبيت التبعيات**:
```bash
rm -rf node_modules package-lock.json
npm install
```

3. **التواصل للدعم**:
- راجع ملف `TROUBLESHOOTING.md`
- راجع ملف `تعليمات_المطور.md`
- أنشئ issue في GitHub

### 📝 ملاحظات المطور

#### تغييرات في الكود
- تم إزالة جميع استخدامات `'ar-SA'` من `toLocaleString()`
- تم إضافة دالة `convertArabicText()` في `reportGenerator.ts`
- تم تحديث جميع مراجع التاريخ لاستخدام `'en-US'`

#### أفضل الممارسات
- استخدم دائماً `toLocaleString('en-US')` للتواريخ
- تأكد من اختبار التقارير بعد أي تغييرات في النصوص
- احتفظ بنسخة احتياطية قبل أي تحديث رئيسي

---

**تاريخ الإصدار**: 19 ديسمبر 2024  
**رقم البناء**: 1.1.0-20241219  
**حالة الاستقرار**: مستقر ✅

---

## الإصدار 1.0.0 - 2024-12-18

### 🎉 الإصدار الأولي

#### الميزات الأساسية
- نظام مراقبة السيرفرات الشامل
- واجهة مستخدم عربية مع دعم RTL
- نظام تنبيهات متقدم
- تقارير PDF تفصيلية
- إعدادات شاملة للإيميل والـ Webhook
- مراقبة الأداء في الوقت الفعلي

#### الوثائق
- دليل المطور الشامل (`تعليمات_المطور.md`)
- دليل استكشاف الأخطاء (`TROUBLESHOOTING.md`)
- دليل النشر والصيانة (`DEPLOYMENT.md`)
- ملف README باللغة العربية

#### التقنيات المستخدمة
- Next.js 15.4.4
- TypeScript
- Tailwind CSS
- Recharts
- jsPDF
- Nodemailer

---

*للمزيد من المعلومات، راجع الوثائق الفنية في مجلد المشروع.*
