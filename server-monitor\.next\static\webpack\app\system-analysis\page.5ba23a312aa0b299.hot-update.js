"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/system-analysis/page",{

/***/ "(app-pages-browser)/./src/components/TempFilesPanel.tsx":
/*!*******************************************!*\
  !*** ./src/components/TempFilesPanel.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TempFilesPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckSquare,File,FolderOpen,Loader2,RefreshCw,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction TempFilesPanel() {\n    _s();\n    const [tempData, setTempData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFiles, setSelectedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [deleting, setDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('size');\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('desc');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [itemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(25); // تقليل عدد العناصر لتحسين الأداء\n    const [showOnlyDeletable, setShowOnlyDeletable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchTempFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TempFilesPanel.useCallback[fetchTempFiles]\": async ()=>{\n            try {\n                setRefreshing(true);\n                const response = await fetch('/api/system-analysis?type=temp-files');\n                const result = await response.json();\n                if (result.success) {\n                    // تحويل التواريخ من string إلى Date\n                    const filesWithDates = result.data.files.map({\n                        \"TempFilesPanel.useCallback[fetchTempFiles].filesWithDates\": (file)=>({\n                                ...file,\n                                lastModified: new Date(file.lastModified)\n                            })\n                    }[\"TempFilesPanel.useCallback[fetchTempFiles].filesWithDates\"]);\n                    setTempData({\n                        ...result.data,\n                        files: filesWithDates\n                    });\n                    setSelectedFiles(new Set()); // مسح التحديد عند التحديث\n                }\n            } catch (error) {\n                console.error('خطأ في جلب الملفات المؤقتة:', error);\n            } finally{\n                setLoading(false);\n                setRefreshing(false);\n            }\n        }\n    }[\"TempFilesPanel.useCallback[fetchTempFiles]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TempFilesPanel.useEffect\": ()=>{\n            fetchTempFiles();\n        }\n    }[\"TempFilesPanel.useEffect\"], [\n        fetchTempFiles\n    ]);\n    // تحسين التحديد باستخدام useCallback لمنع إعادة الرندر غير الضرورية\n    const toggleFileSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TempFilesPanel.useCallback[toggleFileSelection]\": (filePath)=>{\n            setSelectedFiles({\n                \"TempFilesPanel.useCallback[toggleFileSelection]\": (prev)=>{\n                    const newSelected = new Set(prev);\n                    if (newSelected.has(filePath)) {\n                        newSelected.delete(filePath);\n                    } else {\n                        newSelected.add(filePath);\n                    }\n                    return newSelected;\n                }\n            }[\"TempFilesPanel.useCallback[toggleFileSelection]\"]);\n        }\n    }[\"TempFilesPanel.useCallback[toggleFileSelection]\"], []);\n    // تحسين الفلترة والترتيب باستخدام useMemo\n    const filteredAndSortedFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TempFilesPanel.useMemo[filteredAndSortedFiles]\": ()=>{\n            if (!tempData) return [];\n            let filtered = tempData.files;\n            // فلترة الملفات القابلة للحذف فقط\n            if (showOnlyDeletable) {\n                filtered = filtered.filter({\n                    \"TempFilesPanel.useMemo[filteredAndSortedFiles]\": (file)=>file.canDelete\n                }[\"TempFilesPanel.useMemo[filteredAndSortedFiles]\"]);\n            }\n            // البحث النصي\n            if (searchTerm) {\n                const lowerSearchTerm = searchTerm.toLowerCase();\n                filtered = filtered.filter({\n                    \"TempFilesPanel.useMemo[filteredAndSortedFiles]\": (file)=>file.name.toLowerCase().includes(lowerSearchTerm) || file.path.toLowerCase().includes(lowerSearchTerm)\n                }[\"TempFilesPanel.useMemo[filteredAndSortedFiles]\"]);\n            }\n            // الترتيب\n            filtered.sort({\n                \"TempFilesPanel.useMemo[filteredAndSortedFiles]\": (a, b)=>{\n                    let comparison = 0;\n                    switch(sortBy){\n                        case 'name':\n                            comparison = a.name.localeCompare(b.name);\n                            break;\n                        case 'size':\n                            comparison = a.size - b.size;\n                            break;\n                        case 'date':\n                            comparison = a.lastModified.getTime() - b.lastModified.getTime();\n                            break;\n                    }\n                    return sortOrder === 'desc' ? -comparison : comparison;\n                }\n            }[\"TempFilesPanel.useMemo[filteredAndSortedFiles]\"]);\n            return filtered;\n        }\n    }[\"TempFilesPanel.useMemo[filteredAndSortedFiles]\"], [\n        tempData,\n        searchTerm,\n        sortBy,\n        sortOrder,\n        showOnlyDeletable\n    ]);\n    // حساب الصفحات\n    const totalPages = Math.ceil(filteredAndSortedFiles.length / itemsPerPage);\n    const paginatedFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TempFilesPanel.useMemo[paginatedFiles]\": ()=>{\n            const startIndex = (currentPage - 1) * itemsPerPage;\n            return filteredAndSortedFiles.slice(startIndex, startIndex + itemsPerPage);\n        }\n    }[\"TempFilesPanel.useMemo[paginatedFiles]\"], [\n        filteredAndSortedFiles,\n        currentPage,\n        itemsPerPage\n    ]);\n    const selectAllVisibleFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TempFilesPanel.useCallback[selectAllVisibleFiles]\": ()=>{\n            const visiblePaths = paginatedFiles.filter({\n                \"TempFilesPanel.useCallback[selectAllVisibleFiles].visiblePaths\": (file)=>file.canDelete\n            }[\"TempFilesPanel.useCallback[selectAllVisibleFiles].visiblePaths\"]).map({\n                \"TempFilesPanel.useCallback[selectAllVisibleFiles].visiblePaths\": (file)=>file.path\n            }[\"TempFilesPanel.useCallback[selectAllVisibleFiles].visiblePaths\"]);\n            setSelectedFiles({\n                \"TempFilesPanel.useCallback[selectAllVisibleFiles]\": (prev)=>{\n                    const newSelected = new Set(prev);\n                    const allVisibleSelected = visiblePaths.every({\n                        \"TempFilesPanel.useCallback[selectAllVisibleFiles].allVisibleSelected\": (path)=>newSelected.has(path)\n                    }[\"TempFilesPanel.useCallback[selectAllVisibleFiles].allVisibleSelected\"]);\n                    if (allVisibleSelected) {\n                        // إلغاء تحديد الملفات المرئية\n                        visiblePaths.forEach({\n                            \"TempFilesPanel.useCallback[selectAllVisibleFiles]\": (path)=>newSelected.delete(path)\n                        }[\"TempFilesPanel.useCallback[selectAllVisibleFiles]\"]);\n                    } else {\n                        // تحديد الملفات المرئية\n                        visiblePaths.forEach({\n                            \"TempFilesPanel.useCallback[selectAllVisibleFiles]\": (path)=>newSelected.add(path)\n                        }[\"TempFilesPanel.useCallback[selectAllVisibleFiles]\"]);\n                    }\n                    return newSelected;\n                }\n            }[\"TempFilesPanel.useCallback[selectAllVisibleFiles]\"]);\n        }\n    }[\"TempFilesPanel.useCallback[selectAllVisibleFiles]\"], [\n        paginatedFiles\n    ]);\n    const selectAllDeletableFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TempFilesPanel.useCallback[selectAllDeletableFiles]\": ()=>{\n            if (!tempData) return;\n            const deletableFiles = filteredAndSortedFiles.filter({\n                \"TempFilesPanel.useCallback[selectAllDeletableFiles].deletableFiles\": (file)=>file.canDelete\n            }[\"TempFilesPanel.useCallback[selectAllDeletableFiles].deletableFiles\"]).map({\n                \"TempFilesPanel.useCallback[selectAllDeletableFiles].deletableFiles\": (file)=>file.path\n            }[\"TempFilesPanel.useCallback[selectAllDeletableFiles].deletableFiles\"]);\n            setSelectedFiles(new Set(deletableFiles));\n        }\n    }[\"TempFilesPanel.useCallback[selectAllDeletableFiles]\"], [\n        filteredAndSortedFiles,\n        tempData\n    ]);\n    const clearSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TempFilesPanel.useCallback[clearSelection]\": ()=>{\n            setSelectedFiles(new Set());\n        }\n    }[\"TempFilesPanel.useCallback[clearSelection]\"], []);\n    const deleteSelectedFiles = async ()=>{\n        if (selectedFiles.size === 0) return;\n        try {\n            setDeleting(true);\n            const response = await fetch('/api/system-analysis', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'delete-temp-files',\n                    filePaths: Array.from(selectedFiles)\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                alert(\"تم حذف \".concat(result.data.success.length, \" ملف بنجاح\"));\n                if (result.data.failed.length > 0) {\n                    alert(\"فشل في حذف \".concat(result.data.failed.length, \" ملف\"));\n                }\n                // تحديث القائمة\n                await fetchTempFiles();\n                setSelectedFiles(new Set());\n            }\n        } catch (error) {\n            console.error('خطأ في حذف الملفات:', error);\n            alert('فشل في حذف الملفات');\n        } finally{\n            setDeleting(false);\n        }\n    };\n    const formatTimestamp = (timestamp)=>{\n        return timestamp.toLocaleString('en-US', {\n            year: 'numeric',\n            month: '2-digit',\n            day: '2-digit',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    const getSelectedSize = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TempFilesPanel.useMemo[getSelectedSize]\": ()=>{\n            if (!tempData) return 0;\n            return tempData.files.filter({\n                \"TempFilesPanel.useMemo[getSelectedSize]\": (file)=>selectedFiles.has(file.path)\n            }[\"TempFilesPanel.useMemo[getSelectedSize]\"]).reduce({\n                \"TempFilesPanel.useMemo[getSelectedSize]\": (total, file)=>{\n                    // إضافة حجم الملف مضروباً في عدد النسخ المكررة\n                    const multiplier = file.isDuplicate && file.duplicateCount ? file.duplicateCount : 1;\n                    return total + file.size * multiplier;\n                }\n            }[\"TempFilesPanel.useMemo[getSelectedSize]\"], 0);\n        }\n    }[\"TempFilesPanel.useMemo[getSelectedSize]\"], [\n        tempData,\n        selectedFiles\n    ]);\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 B';\n        const k = 1024;\n        const sizes = [\n            'B',\n            'KB',\n            'MB',\n            'GB',\n            'TB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"mr-3 text-gray-600\",\n                        children: \"جاري تحميل الملفات المؤقتة...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                lineNumber: 231,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n            lineNumber: 230,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-6 h-6 text-orange-500 ml-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-800\",\n                                        children: \"الملفات المؤقتة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this),\n                                    tempData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-3 px-2 py-1 bg-orange-100 text-orange-800 text-sm rounded-full\",\n                                        children: [\n                                            tempData.count,\n                                            \" ملف\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: fetchTempFiles,\n                                disabled: refreshing,\n                                className: \"flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-4 h-4 ml-2 \".concat(refreshing ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"تحديث\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this),\n                    tempData && tempData.files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"البحث في الملفات...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>{\n                                            setSearchTerm(e.target.value);\n                                            setCurrentPage(1); // العودة للصفحة الأولى عند البحث\n                                        },\n                                        className: \"w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: sortBy,\n                                        onChange: (e)=>setSortBy(e.target.value),\n                                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"size\",\n                                                children: \"ترتيب حسب الحجم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"name\",\n                                                children: \"ترتيب حسب الاسم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"date\",\n                                                children: \"ترتيب حسب التاريخ\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc'),\n                                        className: \"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                        title: sortOrder === 'asc' ? 'تصاعدي' : 'تنازلي',\n                                        children: sortOrder === 'asc' ? '↑' : '↓'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: showOnlyDeletable,\n                                            onChange: (e)=>{\n                                                setShowOnlyDeletable(e.target.checked);\n                                                setCurrentPage(1);\n                                            },\n                                            className: \"ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-700\",\n                                            children: \"الملفات القابلة للحذف فقط\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 border border-green-200 rounded-lg p-4 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5 text-green-600 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-green-800\",\n                                            children: \"ملفات آمنة للحذف\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-700 mt-1\",\n                                            children: \"يعرض النظام الملفات المؤقتة الآمنة للحذف فقط. تم استبعاد ملفات قواعد البيانات والملفات الحساسة تلقائياً لحماية النظام.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, this),\n                    tempData && filteredAndSortedFiles.some((f)=>f.isDuplicate && f.duplicateCount && f.duplicateCount > 1) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5 text-yellow-600 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-yellow-800\",\n                                            children: \"ملفات مكررة تم دمجها\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-yellow-700 mt-1\",\n                                            children: \"تم دمج الملفات المتشابهة (نفس الاسم والحجم) في عنصر واحد. عند الحذف، سيتم حذف جميع النسخ المكررة تلقائياً.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, this),\n                    tempData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-5 gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-orange-600\",\n                                        children: \"إجمالي الحجم\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-800\",\n                                        children: tempData.totalSizeFormatted\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-blue-600\",\n                                        children: \"الملفات المعروضة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-800\",\n                                        children: filteredAndSortedFiles.length\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-yellow-600\",\n                                        children: \"ملفات مكررة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-800\",\n                                        children: filteredAndSortedFiles.filter((f)=>f.isDuplicate && f.duplicateCount && f.duplicateCount > 1).length\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-purple-600\",\n                                        children: \"الملفات المحددة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-800\",\n                                        children: selectedFiles.size\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-green-600\",\n                                        children: \"حجم المحدد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-800\",\n                                        children: formatFileSize(getSelectedSize)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 11\n                    }, this),\n                    tempData && filteredAndSortedFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: selectAllVisibleFiles,\n                                        className: \"flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"تحديد الصفحة الحالية\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: selectAllDeletableFiles,\n                                        className: \"flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"تحديد الكل (\",\n                                            filteredAndSortedFiles.filter((f)=>f.canDelete).length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: clearSelection,\n                                        className: \"flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إلغاء التحديد\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: deleteSelectedFiles,\n                                        disabled: selectedFiles.size === 0 || deleting,\n                                        className: \"flex items-center px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50\",\n                                        children: [\n                                            deleting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, this),\n                                            deleting ? 'جاري الحذف...' : \"حذف المحدد (\".concat(selectedFiles.size, \")\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this),\n                            totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"صفحة \",\n                                    currentPage,\n                                    \" من \",\n                                    totalPages,\n                                    \" (\",\n                                    filteredAndSortedFiles.length,\n                                    \" ملف)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: !tempData || tempData.files.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-lg\",\n                            children: \"لا توجد ملفات مؤقتة\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: \"النظام نظيف من الملفات المؤقتة\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 11\n                }, this) : filteredAndSortedFiles.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-lg\",\n                            children: \"لا توجد نتائج\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: \"جرب تغيير معايير البحث أو الفلترة\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                    lineNumber: 433,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: paginatedFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow \".concat(selectedFiles.has(file.path) ? 'bg-blue-50 border-blue-300' : ''),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>toggleFileSelection(file.path),\n                                                        className: \"ml-3\",\n                                                        disabled: !file.canDelete,\n                                                        children: selectedFiles.has(file.path) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-5 h-5 text-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-5 h-5 \".concat(file.canDelete ? 'text-gray-400 hover:text-blue-500' : 'text-gray-300')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-5 h-5 text-gray-400 ml-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-gray-800\",\n                                                                        children: file.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    file.isDuplicate && file.duplicateCount && file.duplicateCount > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-2 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full\",\n                                                                        children: [\n                                                                            file.duplicateCount,\n                                                                            \" نسخ\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                        lineNumber: 465,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 truncate max-w-md\",\n                                                                children: file.path\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            file.isDuplicate && file.duplicatePaths && file.duplicatePaths.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-orange-600 mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                                                    className: \"cursor-pointer\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                                            className: \"hover:text-orange-800\",\n                                                                            children: [\n                                                                                \"عرض جميع المواقع (\",\n                                                                                file.duplicatePaths.length,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                            lineNumber: 474,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-1 pr-4 max-h-32 overflow-y-auto\",\n                                                                            children: file.duplicatePaths.map((path, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs text-gray-500 py-1 border-r-2 border-orange-200 pr-2\",\n                                                                                    children: path\n                                                                                }, index, false, {\n                                                                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                                    lineNumber: 477,\n                                                                                    columnNumber: 35\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                            lineNumber: 475,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: [\n                                                                    \"آخر تعديل: \",\n                                                                    formatTimestamp(file.lastModified)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-end\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: file.sizeFormatted\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    file.isDuplicate && file.duplicateCount && file.duplicateCount > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-orange-600\",\n                                                        children: [\n                                                            \"إجمالي: \",\n                                                            formatFileSize(file.size * file.duplicateCount)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    !file.canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckSquare_File_FolderOpen_Loader2_RefreshCw_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4 text-yellow-500 mt-1\",\n                                                        title: \"لا يمكن حذف هذا الملف\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 19\n                                    }, this)\n                                }, file.path, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 13\n                        }, this),\n                        totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mt-6 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage(Math.max(1, currentPage - 1)),\n                                    disabled: currentPage === 1,\n                                    className: \"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"السابق\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-1\",\n                                    children: Array.from({\n                                        length: Math.min(5, totalPages)\n                                    }, (_, i)=>{\n                                        let pageNum;\n                                        if (totalPages <= 5) {\n                                            pageNum = i + 1;\n                                        } else if (currentPage <= 3) {\n                                            pageNum = i + 1;\n                                        } else if (currentPage >= totalPages - 2) {\n                                            pageNum = totalPages - 4 + i;\n                                        } else {\n                                            pageNum = currentPage - 2 + i;\n                                        }\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentPage(pageNum),\n                                            className: \"px-3 py-2 rounded-lg \".concat(currentPage === pageNum ? 'bg-blue-500 text-white' : 'border border-gray-300 hover:bg-gray-50'),\n                                            children: pageNum\n                                        }, pageNum, false, {\n                                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 23\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage(Math.min(totalPages, currentPage + 1)),\n                                    disabled: currentPage === totalPages,\n                                    className: \"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"التالي\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n                lineNumber: 425,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\components\\\\TempFilesPanel.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n_s(TempFilesPanel, \"Cri2UDBcltCK6QvRS0pgJjCAcko=\");\n_c = TempFilesPanel;\nvar _c;\n$RefreshReg$(_c, \"TempFilesPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TempFilesPanel.tsx\n"));

/***/ })

});