/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/system-analysis/route";
exports.ids = ["app/api/system-analysis/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsystem-analysis%2Froute&page=%2Fapi%2Fsystem-analysis%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem-analysis%2Froute.ts&appDir=D%3A%5Cmoneer%5Cfolders%5Ccopy%20working%5Cserver_control%5Cserver-monitor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmoneer%5Cfolders%5Ccopy%20working%5Cserver_control%5Cserver-monitor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsystem-analysis%2Froute&page=%2Fapi%2Fsystem-analysis%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem-analysis%2Froute.ts&appDir=D%3A%5Cmoneer%5Cfolders%5Ccopy%20working%5Cserver_control%5Cserver-monitor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmoneer%5Cfolders%5Ccopy%20working%5Cserver_control%5Cserver-monitor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var D_moneer_folders_copy_working_server_control_server_monitor_src_app_api_system_analysis_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/system-analysis/route.ts */ \"(rsc)/./src/app/api/system-analysis/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/system-analysis/route\",\n        pathname: \"/api/system-analysis\",\n        filename: \"route\",\n        bundlePath: \"app/api/system-analysis/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"D:\\\\moneer\\\\folders\\\\copy working\\\\server_control\\\\server-monitor\\\\src\\\\app\\\\api\\\\system-analysis\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_moneer_folders_copy_working_server_control_server_monitor_src_app_api_system_analysis_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/system-analysis/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsystem-analysis%2Froute&page=%2Fapi%2Fsystem-analysis%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem-analysis%2Froute.ts&appDir=D%3A%5Cmoneer%5Cfolders%5Ccopy%20working%5Cserver_control%5Cserver-monitor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmoneer%5Cfolders%5Ccopy%20working%5Cserver_control%5Cserver-monitor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/system-analysis/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/system-analysis/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_systemAnalyzer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/systemAnalyzer */ \"(rsc)/./src/lib/systemAnalyzer.ts\");\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const type = searchParams.get('type');\n        switch(type){\n            case 'errors':\n                const errors = await _lib_systemAnalyzer__WEBPACK_IMPORTED_MODULE_1__.systemAnalyzer.getSystemErrors();\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: errors\n                });\n            case 'temp-files':\n                const tempFiles = await _lib_systemAnalyzer__WEBPACK_IMPORTED_MODULE_1__.systemAnalyzer.getTempFiles();\n                const totalTempSize = tempFiles.reduce((total, file)=>total + file.size, 0);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: {\n                        files: tempFiles,\n                        totalSize: totalTempSize,\n                        totalSizeFormatted: formatFileSize(totalTempSize),\n                        count: tempFiles.length\n                    }\n                });\n            case 'large-files':\n                const minSizeMB = parseInt(searchParams.get('minSize') || '100');\n                const largeFiles = await _lib_systemAnalyzer__WEBPACK_IMPORTED_MODULE_1__.systemAnalyzer.getLargeFiles(minSizeMB);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: {\n                        files: largeFiles,\n                        count: largeFiles.length,\n                        minSizeMB\n                    }\n                });\n            case 'full-analysis':\n            default:\n                const analysis = await _lib_systemAnalyzer__WEBPACK_IMPORTED_MODULE_1__.systemAnalyzer.analyzeSystem();\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: analysis\n                });\n        }\n    } catch (error) {\n        console.error('خطأ في تحليل النظام:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تحليل النظام'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action, filePaths } = body;\n        if (action === 'delete-temp-files' && Array.isArray(filePaths)) {\n            // التحقق من صحة المسارات\n            if (filePaths.length === 0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'لا توجد ملفات للحذف'\n                }, {\n                    status: 400\n                });\n            }\n            // التحقق من أن جميع المسارات صالحة\n            const invalidPaths = filePaths.filter((path)=>typeof path !== 'string' || path.trim() === '');\n            if (invalidPaths.length > 0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'مسارات ملفات غير صحيحة'\n                }, {\n                    status: 400\n                });\n            }\n            console.log(`بدء حذف ${filePaths.length} ملف...`);\n            const result = await _lib_systemAnalyzer__WEBPACK_IMPORTED_MODULE_1__.systemAnalyzer.deleteTempFiles(filePaths);\n            console.log(`انتهى الحذف: ${result.success.length} نجح، ${result.failed.length} فشل`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: result,\n                message: `تم حذف ${result.success.length} ملف بنجاح${result.failed.length > 0 ? `، فشل في حذف ${result.failed.length} ملف` : ''}`\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'إجراء غير صحيح'\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error('خطأ في معالجة الطلب:', error);\n        const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في معالجة الطلب',\n            details: errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\nfunction formatFileSize(bytes) {\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = [\n        'B',\n        'KB',\n        'MB',\n        'GB',\n        'TB'\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/system-analysis/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/systemAnalyzer.ts":
/*!***********************************!*\
  !*** ./src/lib/systemAnalyzer.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SystemAnalyzer: () => (/* binding */ SystemAnalyzer),\n/* harmony export */   systemAnalyzer: () => (/* binding */ systemAnalyzer)\n/* harmony export */ });\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var os__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! os */ \"os\");\n/* harmony import */ var os__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(os__WEBPACK_IMPORTED_MODULE_4__);\n// مكتبة تحليل النظام المتقدم - أخطاء السيرفر والملفات المؤقتة والملفات الكبيرة\n\n\n\n\n\nconst execAsync = (0,util__WEBPACK_IMPORTED_MODULE_1__.promisify)(child_process__WEBPACK_IMPORTED_MODULE_0__.exec);\nclass SystemAnalyzer {\n    static getInstance() {\n        if (!SystemAnalyzer.instance) {\n            SystemAnalyzer.instance = new SystemAnalyzer();\n        }\n        return SystemAnalyzer.instance;\n    }\n    /**\n   * تحليل شامل للنظام\n   */ async analyzeSystem() {\n        const [errors, tempFiles, largeFiles] = await Promise.all([\n            this.getSystemErrors(),\n            this.getTempFiles(),\n            this.getLargeFiles()\n        ]);\n        const totalTempSize = tempFiles.reduce((total, file)=>total + file.size, 0);\n        return {\n            errors,\n            tempFiles,\n            largeFiles,\n            totalTempSize,\n            totalTempSizeFormatted: this.formatFileSize(totalTempSize),\n            tempFileCount: tempFiles.length,\n            largeFileCount: largeFiles.length\n        };\n    }\n    /**\n   * الحصول على أخطاء النظام\n   */ async getSystemErrors() {\n        const errors = [];\n        try {\n            if (os__WEBPACK_IMPORTED_MODULE_4__.platform() === 'win32') {\n                await this.getWindowsErrors(errors);\n            } else {\n                await this.getLinuxErrors(errors);\n            }\n        } catch (error) {\n            console.error('خطأ في جمع أخطاء النظام:', error);\n        }\n        return errors.sort((a, b)=>b.timestamp.getTime() - a.timestamp.getTime());\n    }\n    /**\n   * الحصول على أخطاء Windows\n   */ async getWindowsErrors(errors) {\n        try {\n            // أخطاء Application Log\n            const appLogCommand = `Get-EventLog -LogName Application -EntryType Error -Newest 50 | ConvertTo-Json`;\n            const { stdout: appLogs } = await execAsync(`powershell -Command \"${appLogCommand}\"`);\n            if (appLogs.trim()) {\n                const appEvents = JSON.parse(appLogs);\n                const events = Array.isArray(appEvents) ? appEvents : [\n                    appEvents\n                ];\n                events.forEach((event)=>{\n                    errors.push({\n                        id: `app-${event.Index}`,\n                        timestamp: new Date(event.TimeGenerated),\n                        level: 'error',\n                        source: event.Source || 'Application',\n                        message: event.Message || 'رسالة غير متوفرة',\n                        details: `Event ID: ${event.EventID}`\n                    });\n                });\n            }\n            // أخطاء System Log\n            const sysLogCommand = `Get-EventLog -LogName System -EntryType Error -Newest 30 | ConvertTo-Json`;\n            const { stdout: sysLogs } = await execAsync(`powershell -Command \"${sysLogCommand}\"`);\n            if (sysLogs.trim()) {\n                const sysEvents = JSON.parse(sysLogs);\n                const events = Array.isArray(sysEvents) ? sysEvents : [\n                    sysEvents\n                ];\n                events.forEach((event)=>{\n                    errors.push({\n                        id: `sys-${event.Index}`,\n                        timestamp: new Date(event.TimeGenerated),\n                        level: 'critical',\n                        source: event.Source || 'System',\n                        message: event.Message || 'رسالة غير متوفرة',\n                        details: `Event ID: ${event.EventID}`\n                    });\n                });\n            }\n        } catch (error) {\n            console.error('خطأ في جمع أخطاء Windows:', error);\n        }\n    }\n    /**\n   * الحصول على أخطاء Linux\n   */ async getLinuxErrors(errors) {\n        try {\n            // فحص syslog\n            const { stdout: syslog } = await execAsync(`tail -n 100 /var/log/syslog | grep -i error || echo \"\"`);\n            if (syslog) {\n                const lines = syslog.split('\\n').filter((line)=>line.trim());\n                lines.forEach((line, index)=>{\n                    errors.push({\n                        id: `syslog-${index}`,\n                        timestamp: new Date(),\n                        level: 'error',\n                        source: 'syslog',\n                        message: line.trim()\n                    });\n                });\n            }\n            // فحص kern.log\n            const { stdout: kernlog } = await execAsync(`tail -n 50 /var/log/kern.log | grep -i error || echo \"\"`);\n            if (kernlog) {\n                const lines = kernlog.split('\\n').filter((line)=>line.trim());\n                lines.forEach((line, index)=>{\n                    errors.push({\n                        id: `kern-${index}`,\n                        timestamp: new Date(),\n                        level: 'critical',\n                        source: 'kernel',\n                        message: line.trim()\n                    });\n                });\n            }\n        } catch (error) {\n            console.error('خطأ في جمع أخطاء Linux:', error);\n        }\n    }\n    /**\n   * الحصول على الملفات المؤقتة\n   */ async getTempFiles() {\n        const tempFiles = [];\n        try {\n            if (os__WEBPACK_IMPORTED_MODULE_4__.platform() === 'win32') {\n                await this.getWindowsTempFiles(tempFiles);\n            } else {\n                await this.getLinuxTempFiles(tempFiles);\n            }\n            // فلترة الملفات الآمنة للحذف فقط\n            const safeFiles = tempFiles.filter((file)=>this.isSafeToDelete(file));\n            // إزالة الملفات المكررة\n            const uniqueFiles = this.removeDuplicateFiles(safeFiles);\n            return uniqueFiles.sort((a, b)=>b.size - a.size);\n        } catch (error) {\n            console.error('خطأ في جمع الملفات المؤقتة:', error);\n            return [];\n        }\n    }\n    /**\n   * الحصول على الملفات المؤقتة في Windows\n   */ async getWindowsTempFiles(tempFiles) {\n        // مجلدات الملفات المؤقتة الآمنة فقط\n        const safeTempDirs = [\n            // مجلدات المستخدم الآمنة\n            process.env.TEMP,\n            process.env.TMP,\n            // مجلدات إضافية آمنة\n            'C:\\\\Temp',\n            'C:\\\\tmp',\n            // مجلدات المتصفحات (إذا كانت موجودة)\n            path__WEBPACK_IMPORTED_MODULE_3__.join(process.env.LOCALAPPDATA || '', 'Temp'),\n            // مجلدات التحديثات المؤقتة\n            'C:\\\\Windows\\\\SoftwareDistribution\\\\Download',\n            'C:\\\\Windows\\\\Temp\\\\MpCmdRun'\n        ].filter(Boolean); // إزالة القيم الفارغة\n        for (const tempDir of safeTempDirs){\n            try {\n                if (tempDir && fs__WEBPACK_IMPORTED_MODULE_2__.existsSync(tempDir)) {\n                    await this.scanDirectory(tempDir, tempFiles, true);\n                }\n            } catch (error) {\n                console.error(`خطأ في فحص مجلد ${tempDir}:`, error);\n            }\n        }\n    }\n    /**\n   * الحصول على الملفات المؤقتة في Linux\n   */ async getLinuxTempFiles(tempFiles) {\n        const tempDirs = [\n            '/tmp',\n            '/var/tmp',\n            '/dev/shm'\n        ];\n        for (const tempDir of tempDirs){\n            try {\n                if (fs__WEBPACK_IMPORTED_MODULE_2__.existsSync(tempDir)) {\n                    await this.scanDirectory(tempDir, tempFiles, true);\n                }\n            } catch (error) {\n                console.error(`خطأ في فحص مجلد ${tempDir}:`, error);\n            }\n        }\n    }\n    /**\n   * الحصول على الملفات الكبيرة\n   */ async getLargeFiles(minSizeMB = 100) {\n        const largeFiles = [];\n        const minSize = minSizeMB * 1024 * 1024; // تحويل إلى بايت\n        try {\n            if (os__WEBPACK_IMPORTED_MODULE_4__.platform() === 'win32') {\n                await this.getWindowsLargeFiles(largeFiles, minSize);\n            } else {\n                await this.getLinuxLargeFiles(largeFiles, minSize);\n            }\n        } catch (error) {\n            console.error('خطأ في جمع الملفات الكبيرة:', error);\n        }\n        return largeFiles.sort((a, b)=>b.size - a.size);\n    }\n    /**\n   * الحصول على الملفات الكبيرة في Windows\n   */ async getWindowsLargeFiles(largeFiles, minSize) {\n        const drives = [\n            'C:',\n            'D:',\n            'E:',\n            'F:'\n        ];\n        for (const drive of drives){\n            try {\n                if (fs__WEBPACK_IMPORTED_MODULE_2__.existsSync(drive)) {\n                    await this.scanDirectoryForLargeFiles(drive, largeFiles, minSize, 2); // عمق 2 مستويات\n                }\n            } catch (error) {\n                console.error(`خطأ في فحص القرص ${drive}:`, error);\n            }\n        }\n    }\n    /**\n   * الحصول على الملفات الكبيرة في Linux\n   */ async getLinuxLargeFiles(largeFiles, minSize) {\n        const searchDirs = [\n            '/',\n            '/home',\n            '/var',\n            '/opt'\n        ];\n        for (const searchDir of searchDirs){\n            try {\n                if (fs__WEBPACK_IMPORTED_MODULE_2__.existsSync(searchDir)) {\n                    await this.scanDirectoryForLargeFiles(searchDir, largeFiles, minSize, 3); // عمق 3 مستويات\n                }\n            } catch (error) {\n                console.error(`خطأ في فحص مجلد ${searchDir}:`, error);\n            }\n        }\n    }\n    /**\n   * فحص مجلد للملفات\n   */ async scanDirectory(dirPath, files, isTempFile, maxDepth = 2, currentDepth = 0) {\n        if (currentDepth >= maxDepth) return;\n        try {\n            const items = fs__WEBPACK_IMPORTED_MODULE_2__.readdirSync(dirPath);\n            for (const item of items){\n                const fullPath = path__WEBPACK_IMPORTED_MODULE_3__.join(dirPath, item);\n                try {\n                    const stats = fs__WEBPACK_IMPORTED_MODULE_2__.statSync(fullPath);\n                    if (stats.isFile() && isTempFile) {\n                        files.push({\n                            path: fullPath,\n                            name: item,\n                            size: stats.size,\n                            sizeFormatted: this.formatFileSize(stats.size),\n                            lastModified: stats.mtime,\n                            canDelete: this.canDeleteTempFile(fullPath, stats)\n                        });\n                    } else if (stats.isDirectory() && currentDepth < maxDepth - 1) {\n                        await this.scanDirectory(fullPath, files, isTempFile, maxDepth, currentDepth + 1);\n                    }\n                } catch (error) {\n                // تجاهل الملفات التي لا يمكن الوصول إليها\n                }\n            }\n        } catch (error) {\n            console.error(`خطأ في قراءة مجلد ${dirPath}:`, error);\n        }\n    }\n    /**\n   * فحص مجلد للملفات الكبيرة\n   */ async scanDirectoryForLargeFiles(dirPath, files, minSize, maxDepth, currentDepth = 0) {\n        if (currentDepth >= maxDepth) return;\n        try {\n            const items = fs__WEBPACK_IMPORTED_MODULE_2__.readdirSync(dirPath);\n            for (const item of items){\n                const fullPath = path__WEBPACK_IMPORTED_MODULE_3__.join(dirPath, item);\n                try {\n                    const stats = fs__WEBPACK_IMPORTED_MODULE_2__.statSync(fullPath);\n                    if (stats.isFile() && stats.size >= minSize) {\n                        files.push({\n                            path: fullPath,\n                            name: item,\n                            size: stats.size,\n                            sizeFormatted: this.formatFileSize(stats.size),\n                            lastModified: stats.mtime,\n                            directory: path__WEBPACK_IMPORTED_MODULE_3__.dirname(fullPath)\n                        });\n                    } else if (stats.isDirectory() && currentDepth < maxDepth - 1) {\n                        await this.scanDirectoryForLargeFiles(fullPath, files, minSize, maxDepth, currentDepth + 1);\n                    }\n                } catch (error) {\n                // تجاهل الملفات التي لا يمكن الوصول إليها\n                }\n            }\n        } catch (error) {\n            console.error(`خطأ في قراءة مجلد ${dirPath}:`, error);\n        }\n    }\n    /**\n   * تحديد إمكانية حذف الملف المؤقت\n   */ canDeleteTempFile(filePath, stats) {\n        try {\n            // فحص إذا كان الملف قديم (أكثر من يوم)\n            const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);\n            if (stats.mtime < oneDayAgo) {\n                return true;\n            }\n            // فحص امتدادات الملفات المؤقتة الشائعة\n            const tempExtensions = [\n                '.tmp',\n                '.temp',\n                '.log',\n                '.bak',\n                '.cache'\n            ];\n            const ext = path__WEBPACK_IMPORTED_MODULE_3__.extname(filePath).toLowerCase();\n            return tempExtensions.includes(ext);\n        } catch (error) {\n            return false;\n        }\n    }\n    /**\n   * تنسيق حجم الملف\n   */ formatFileSize(bytes) {\n        if (bytes === 0) return '0 B';\n        const k = 1024;\n        const sizes = [\n            'B',\n            'KB',\n            'MB',\n            'GB',\n            'TB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    }\n    /**\n   * حذف الملفات المؤقتة\n   */ async deleteTempFiles(filePaths) {\n        const success = [];\n        const failed = [];\n        if (!filePaths || filePaths.length === 0) {\n            return {\n                success,\n                failed\n            };\n        }\n        console.log(`بدء حذف ${filePaths.length} ملف...`);\n        try {\n            // الحصول على قائمة الملفات الحالية لمعرفة الملفات المكررة\n            const currentFiles = await this.getTempFiles();\n            const duplicateMap = new Map();\n            // بناء خريطة الملفات المكررة\n            for (const file of currentFiles){\n                if (file.isDuplicate && file.duplicatePaths) {\n                    duplicateMap.set(file.path, file.duplicatePaths);\n                }\n            }\n            for (const filePath of filePaths){\n                if (!filePath || typeof filePath !== 'string') {\n                    failed.push(filePath || 'مسار غير صحيح');\n                    continue;\n                }\n                try {\n                    // التحقق من وجود الملف أولاً\n                    let fileExists = false;\n                    try {\n                        fileExists = fs__WEBPACK_IMPORTED_MODULE_2__.existsSync(filePath);\n                    } catch (checkError) {\n                        console.error(`خطأ في فحص وجود الملف ${filePath}:`, checkError);\n                        failed.push(filePath);\n                        continue;\n                    }\n                    // حذف الملف الأساسي\n                    if (fileExists) {\n                        try {\n                            // التحقق من صلاحيات الكتابة\n                            fs__WEBPACK_IMPORTED_MODULE_2__.accessSync(filePath, fs__WEBPACK_IMPORTED_MODULE_2__.constants.W_OK);\n                            fs__WEBPACK_IMPORTED_MODULE_2__.unlinkSync(filePath);\n                            success.push(filePath);\n                            console.log(`تم حذف الملف: ${filePath}`);\n                        } catch (deleteError) {\n                            console.error(`فشل في حذف الملف ${filePath}:`, deleteError);\n                            failed.push(filePath);\n                            continue;\n                        }\n                    } else {\n                        // الملف غير موجود، قد يكون محذوف مسبقاً\n                        console.log(`الملف غير موجود: ${filePath}`);\n                        success.push(filePath); // نعتبره نجاح لأن الهدف تحقق\n                    }\n                    // حذف جميع النسخ المكررة إذا كانت موجودة\n                    if (duplicateMap.has(filePath)) {\n                        const duplicatePaths = duplicateMap.get(filePath);\n                        console.log(`حذف ${duplicatePaths.length} نسخة مكررة للملف ${filePath}`);\n                        for (const duplicatePath of duplicatePaths){\n                            if (duplicatePath !== filePath) {\n                                try {\n                                    if (fs__WEBPACK_IMPORTED_MODULE_2__.existsSync(duplicatePath)) {\n                                        // التحقق من صلاحيات الكتابة\n                                        fs__WEBPACK_IMPORTED_MODULE_2__.accessSync(duplicatePath, fs__WEBPACK_IMPORTED_MODULE_2__.constants.W_OK);\n                                        fs__WEBPACK_IMPORTED_MODULE_2__.unlinkSync(duplicatePath);\n                                        success.push(duplicatePath);\n                                        console.log(`تم حذف النسخة المكررة: ${duplicatePath}`);\n                                    } else {\n                                        // النسخة المكررة غير موجودة\n                                        success.push(duplicatePath);\n                                    }\n                                } catch (duplicateError) {\n                                    failed.push(duplicatePath);\n                                    console.error(`فشل في حذف النسخة المكررة ${duplicatePath}:`, duplicateError);\n                                }\n                            }\n                        }\n                    }\n                } catch (error) {\n                    failed.push(filePath);\n                    console.error(`خطأ عام في حذف الملف ${filePath}:`, error);\n                }\n            }\n        } catch (error) {\n            console.error('خطأ في عملية الحذف:', error);\n            // في حالة خطأ عام، نضع جميع الملفات في قائمة الفشل\n            filePaths.forEach((path)=>{\n                if (!success.includes(path) && !failed.includes(path)) {\n                    failed.push(path);\n                }\n            });\n        }\n        console.log(`انتهى الحذف: ${success.length} نجح، ${failed.length} فشل`);\n        return {\n            success,\n            failed\n        };\n    }\n    /**\n   * التحقق من أن الملف آمن للحذف\n   */ isSafeToDelete(file) {\n        const fileName = file.name.toLowerCase();\n        const filePath = file.path.toLowerCase();\n        // الملفات والمجلدات الحساسة التي يجب تجنب حذفها\n        const sensitivePatterns = [\n            // قواعد البيانات\n            /\\.db$/i,\n            /\\.sqlite$/i,\n            /\\.mdb$/i,\n            /\\.accdb$/i,\n            /\\.sql$/i,\n            /\\.bak$/i,\n            /\\.backup$/i,\n            // ملفات النظام الحساسة\n            /system32/i,\n            /windows/i,\n            /program files/i,\n            /programdata/i,\n            /users.*appdata.*local.*microsoft/i,\n            /users.*appdata.*roaming.*microsoft/i,\n            // ملفات التطبيقات الحساسة\n            /\\.config$/i,\n            /\\.ini$/i,\n            /\\.reg$/i,\n            /\\.dll$/i,\n            /\\.exe$/i,\n            /\\.sys$/i,\n            // ملفات قواعد البيانات المحددة\n            /mysql/i,\n            /postgresql/i,\n            /mongodb/i,\n            /redis/i,\n            /elasticsearch/i,\n            /oracle/i,\n            /sqlserver/i,\n            // ملفات التطبيقات المهمة\n            /node_modules/i,\n            /\\.git/i,\n            /\\.svn/i,\n            /vendor/i,\n            /composer/i,\n            /npm/i,\n            // ملفات الأمان والشهادات\n            /\\.key$/i,\n            /\\.pem$/i,\n            /\\.crt$/i,\n            /\\.cert$/i,\n            /\\.p12$/i,\n            /\\.pfx$/i,\n            // ملفات التشغيل الحساسة\n            /\\.pid$/i,\n            /\\.lock$/i,\n            /\\.running$/i,\n            // مجلدات النظام المهمة\n            /temp.*microsoft/i,\n            /temp.*windows/i,\n            /temp.*system/i,\n            /temp.*service/i,\n            /temp.*iis/i,\n            /temp.*sql/i\n        ];\n        // التحقق من الأنماط الحساسة\n        for (const pattern of sensitivePatterns){\n            if (pattern.test(fileName) || pattern.test(filePath)) {\n                return false;\n            }\n        }\n        // الملفات الآمنة للحذف\n        const safePatterns = [\n            // ملفات مؤقتة عامة\n            /\\.tmp$/i,\n            /\\.temp$/i,\n            /~\\$.*$/i,\n            /\\.cache$/i,\n            /\\.old$/i,\n            // ملفات المتصفحات\n            /browser.*cache/i,\n            /chrome.*cache/i,\n            /firefox.*cache/i,\n            /edge.*cache/i,\n            // ملفات التحديث\n            /update.*temp/i,\n            /download.*temp/i,\n            /install.*temp/i,\n            // ملفات الوسائط المؤقتة\n            /\\.part$/i,\n            /\\.crdownload$/i,\n            /\\.download$/i,\n            // ملفات النصوص المؤقتة\n            /\\.txt$/i,\n            /\\.log$/i,\n            /\\.out$/i,\n            // ملفات الضغط المؤقتة\n            /\\.zip\\.tmp$/i,\n            /\\.rar\\.tmp$/i,\n            /extract.*temp/i\n        ];\n        // التحقق من الأنماط الآمنة\n        for (const pattern of safePatterns){\n            if (pattern.test(fileName) || pattern.test(filePath)) {\n                return true;\n            }\n        }\n        // إذا كان الملف في مجلد temp عام وليس حساساً\n        const generalTempPaths = [\n            /\\\\temp\\\\/i,\n            /\\/tmp\\//i,\n            /\\\\tmp\\\\/i,\n            /temp$/i,\n            /tmp$/i\n        ];\n        for (const pattern of generalTempPaths){\n            if (pattern.test(filePath)) {\n                // تحقق إضافي للتأكد من عدم وجود كلمات حساسة\n                const sensitiveWords = [\n                    'database',\n                    'db',\n                    'sql',\n                    'config',\n                    'system',\n                    'microsoft',\n                    'windows'\n                ];\n                const hassensitiveWord = sensitiveWords.some((word)=>fileName.includes(word) || filePath.includes(word));\n                if (!hassensitiveWord) {\n                    return true;\n                }\n            }\n        }\n        // افتراضياً، لا تحذف الملفات غير المؤكدة\n        return false;\n    }\n    /**\n   * إزالة الملفات المكررة بناءً على الاسم والحجم\n   */ removeDuplicateFiles(files) {\n        const uniqueFiles = new Map();\n        const duplicateGroups = new Map();\n        for (const file of files){\n            // إنشاء مفتاح فريد بناءً على اسم الملف والحجم\n            const key = `${file.name}_${file.size}`;\n            if (!uniqueFiles.has(key)) {\n                // أول ملف من هذا النوع\n                uniqueFiles.set(key, {\n                    ...file,\n                    isDuplicate: false,\n                    duplicateCount: 1,\n                    duplicatePaths: [\n                        file.path\n                    ]\n                });\n                duplicateGroups.set(key, [\n                    file\n                ]);\n            } else {\n                // ملف مكرر\n                const existingFile = uniqueFiles.get(key);\n                const duplicates = duplicateGroups.get(key);\n                duplicates.push(file);\n                // تحديث معلومات الملف الأساسي\n                uniqueFiles.set(key, {\n                    ...existingFile,\n                    isDuplicate: true,\n                    duplicateCount: duplicates.length,\n                    duplicatePaths: duplicates.map((f)=>f.path),\n                    // اختيار أحدث ملف كممثل للمجموعة\n                    lastModified: duplicates.reduce((latest, current)=>current.lastModified > latest ? current.lastModified : latest, existingFile.lastModified),\n                    // اختيار أقصر مسار كمسار أساسي\n                    path: duplicates.reduce((shortest, current)=>current.path.length < shortest.length ? current.path : shortest, existingFile.path)\n                });\n            }\n        }\n        return Array.from(uniqueFiles.values());\n    }\n}\n// تصدير المثيل الوحيد\nconst systemAnalyzer = SystemAnalyzer.getInstance();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/systemAnalyzer.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsystem-analysis%2Froute&page=%2Fapi%2Fsystem-analysis%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem-analysis%2Froute.ts&appDir=D%3A%5Cmoneer%5Cfolders%5Ccopy%20working%5Cserver_control%5Cserver-monitor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmoneer%5Cfolders%5Ccopy%20working%5Cserver_control%5Cserver-monitor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();