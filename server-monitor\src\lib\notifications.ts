import nodemailer from 'nodemailer';

export interface NotificationConfig {
  email: {
    enabled: boolean;
    smtp: {
      host: string;
      port: number;
      secure: boolean;
      auth: {
        user: string;
        pass: string;
      };
    };
    from: string;
    to: string[];
  };
  webhook: {
    enabled: boolean;
    url: string;
  };
}

export interface Alert {
  id: string;
  type: 'warning' | 'error' | 'info';
  title: string;
  message: string;
  serverName: string;
  timestamp: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

class NotificationService {
  private config: NotificationConfig;
  private transporter: nodemailer.Transporter | null = null;

  constructor(config: NotificationConfig) {
    this.config = config;
    this.initializeEmailTransporter();
  }

  private initializeEmailTransporter() {
    if (this.config.email.enabled) {
      try {
        this.transporter = nodemailer.createTransporter(this.config.email.smtp);
      } catch (error) {
        console.error('خطأ في إعداد خدمة الإيميل:', error);
      }
    }
  }

  async sendAlert(alert: Alert): Promise<boolean> {
    const results = await Promise.allSettled([
      this.sendEmailAlert(alert),
      this.sendWebhookAlert(alert),
    ]);

    // إرجاع true إذا نجح إرسال واحد على الأقل
    return results.some(result => result.status === 'fulfilled' && result.value);
  }

  private async sendEmailAlert(alert: Alert): Promise<boolean> {
    if (!this.config.email.enabled || !this.transporter) {
      return false;
    }

    try {
      const subject = `تنبيه من نظام مراقبة السيرفرات: ${alert.title}`;
      const html = this.generateEmailTemplate(alert);

      await this.transporter.sendMail({
        from: this.config.email.from,
        to: this.config.email.to,
        subject,
        html,
      });

      console.log(`تم إرسال تنبيه بالإيميل: ${alert.id}`);
      return true;
    } catch (error) {
      console.error('خطأ في إرسال الإيميل:', error);
      return false;
    }
  }

  private async sendWebhookAlert(alert: Alert): Promise<boolean> {
    if (!this.config.webhook.enabled) {
      return false;
    }

    try {
      const response = await fetch(this.config.webhook.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          alert,
          timestamp: new Date().toISOString(),
        }),
      });

      if (response.ok) {
        console.log(`تم إرسال تنبيه عبر Webhook: ${alert.id}`);
        return true;
      } else {
        console.error('فشل في إرسال Webhook:', response.statusText);
        return false;
      }
    } catch (error) {
      console.error('خطأ في إرسال Webhook:', error);
      return false;
    }
  }

  private generateEmailTemplate(alert: Alert): string {
    const severityColor = {
      low: '#10B981',
      medium: '#F59E0B',
      high: '#EF4444',
      critical: '#DC2626',
    };

    const typeIcon = {
      info: 'ℹ️',
      warning: '⚠️',
      error: '❌',
    };

    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تنبيه من نظام مراقبة السيرفرات</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
          .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
          .header { background-color: ${severityColor[alert.severity]}; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; }
          .alert-info { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
          .footer { background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${typeIcon[alert.type]} ${alert.title}</h1>
          </div>
          <div class="content">
            <div class="alert-info">
              <p><strong>السيرفر:</strong> ${alert.serverName}</p>
              <p><strong>نوع التنبيه:</strong> ${alert.type === 'warning' ? 'تحذير' : alert.type === 'error' ? 'خطأ' : 'معلومات'}</p>
              <p><strong>مستوى الخطورة:</strong> ${alert.severity === 'critical' ? 'حرج' : alert.severity === 'high' ? 'عالي' : alert.severity === 'medium' ? 'متوسط' : 'منخفض'}</p>
              <p><strong>الوقت:</strong> ${new Date(alert.timestamp).toLocaleString('en-US')}</p>
            </div>
            <h3>تفاصيل التنبيه:</h3>
            <p>${alert.message}</p>
            <p>يرجى مراجعة نظام مراقبة السيرفرات لمزيد من التفاصيل.</p>
          </div>
          <div class="footer">
            <p>هذا تنبيه تلقائي من نظام مراقبة السيرفرات</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}

// إعدادات افتراضية للتنبيهات
const defaultConfig: NotificationConfig = {
  email: {
    enabled: false, // يجب تفعيلها وإعداد SMTP
    smtp: {
      host: 'smtp.gmail.com',
      port: 587,
      secure: false,
      auth: {
        user: '',
        pass: '',
      },
    },
    from: '<EMAIL>',
    to: ['<EMAIL>'],
  },
  webhook: {
    enabled: false,
    url: '',
  },
};

export const notificationService = new NotificationService(defaultConfig);

// دالة لإنشاء تنبيه جديد
export function createAlert(
  type: Alert['type'],
  title: string,
  message: string,
  serverName: string,
  severity: Alert['severity'] = 'medium'
): Alert {
  return {
    id: `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    type,
    title,
    message,
    serverName,
    timestamp: new Date().toISOString(),
    severity,
  };
}

// دالة لفحص الحالات وإرسال التنبيهات
export async function checkAndSendAlerts(systemInfo: any, serverName: string) {
  const alerts: Alert[] = [];

  // فحص استخدام المعالج
  if (systemInfo.cpu > 90) {
    alerts.push(createAlert(
      'error',
      'استخدام المعالج مرتفع جداً',
      `استخدام المعالج وصل إلى ${systemInfo.cpu}% في ${serverName}`,
      serverName,
      'critical'
    ));
  } else if (systemInfo.cpu > 80) {
    alerts.push(createAlert(
      'warning',
      'استخدام المعالج مرتفع',
      `استخدام المعالج وصل إلى ${systemInfo.cpu}% في ${serverName}`,
      serverName,
      'high'
    ));
  }

  // فحص استخدام الذاكرة
  if (systemInfo.memory > 90) {
    alerts.push(createAlert(
      'error',
      'استخدام الذاكرة مرتفع جداً',
      `استخدام الذاكرة وصل إلى ${systemInfo.memory}% في ${serverName}`,
      serverName,
      'critical'
    ));
  } else if (systemInfo.memory > 85) {
    alerts.push(createAlert(
      'warning',
      'استخدام الذاكرة مرتفع',
      `استخدام الذاكرة وصل إلى ${systemInfo.memory}% في ${serverName}`,
      serverName,
      'high'
    ));
  }

  // فحص الأقراص
  if (systemInfo.disks) {
    systemInfo.disks.forEach((disk: any) => {
      if (disk.usage > 95) {
        alerts.push(createAlert(
          'error',
          'مساحة التخزين ممتلئة تقريباً',
          `مساحة التخزين في القرص ${disk.name} وصلت إلى ${disk.usage}% في ${serverName}`,
          serverName,
          'critical'
        ));
      } else if (disk.usage > 85) {
        alerts.push(createAlert(
          'warning',
          'مساحة التخزين منخفضة',
          `مساحة التخزين في القرص ${disk.name} وصلت إلى ${disk.usage}% في ${serverName}`,
          serverName,
          'medium'
        ));
      }

      if (disk.badSectors && disk.badSectors > 0) {
        alerts.push(createAlert(
          'error',
          'Bad Sectors مكتشفة',
          `تم اكتشاف ${disk.badSectors} Bad Sectors في القرص ${disk.name} في ${serverName}`,
          serverName,
          'high'
        ));
      }
    });
  }

  // إرسال التنبيهات
  for (const alert of alerts) {
    try {
      await notificationService.sendAlert(alert);
    } catch (error) {
      console.error('خطأ في إرسال التنبيه:', error);
    }
  }

  return alerts;
}
