import { NextResponse } from 'next/server';
import { serverDiscovery } from '@/lib/serverDiscovery';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { action, range } = body;

    switch (action) {
      case 'discover_local':
        // اكتشاف السيرفرات في الشبكة المحلية
        const localServers = await serverDiscovery.discoverLocalNetwork();
        return NextResponse.json({
          success: true,
          servers: localServers,
          message: `تم اكتشاف ${localServers.length} سيرفر في الشبكة المحلية`
        });

      case 'discover_range':
        // اكتشاف السيرفرات في نطاق محدد
        if (!range || !range.startIP || !range.endIP) {
          return NextResponse.json(
            { error: 'نطاق IP مطلوب' },
            { status: 400 }
          );
        }
        
        const rangeServers = await serverDiscovery.discoverServers(range);
        return NextResponse.json({
          success: true,
          servers: rangeServers,
          message: `تم اكتشاف ${rangeServers.length} سيرفر في النطاق المحدد`
        });

      case 'get_discovered':
        // الحصول على السيرفرات المكتشفة
        const discoveredServers = serverDiscovery.getDiscoveredServers();
        return NextResponse.json({
          success: true,
          servers: discoveredServers
        });

      case 'clear_discovered':
        // مسح السيرفرات المكتشفة
        serverDiscovery.clearDiscoveredServers();
        return NextResponse.json({
          success: true,
          message: 'تم مسح السيرفرات المكتشفة'
        });

      default:
        return NextResponse.json(
          { error: 'إجراء غير مدعوم' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('خطأ في اكتشاف السيرفرات:', error);
    return NextResponse.json(
      { error: 'فشل في اكتشاف السيرفرات', details: error instanceof Error ? error.message : 'خطأ غير معروف' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const discoveredServers = serverDiscovery.getDiscoveredServers();
    return NextResponse.json({
      success: true,
      servers: discoveredServers,
      count: discoveredServers.length
    });
  } catch (error) {
    console.error('خطأ في جلب السيرفرات المكتشفة:', error);
    return NextResponse.json(
      { error: 'فشل في جلب السيرفرات المكتشفة' },
      { status: 500 }
    );
  }
}
