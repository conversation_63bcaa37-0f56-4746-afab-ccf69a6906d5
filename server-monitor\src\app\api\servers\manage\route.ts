import { NextResponse } from 'next/server';
import { remoteMonitoring } from '@/lib/remoteMonitoring';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { action, server } = body;

    switch (action) {
      case 'add':
        // إضافة سيرفر جديد للمراقبة
        if (!server || !server.name || !server.ip) {
          return NextResponse.json(
            { error: 'بيانات السيرفر مطلوبة (name, ip)' },
            { status: 400 }
          );
        }

        const newServer = {
          id: `server-${Date.now()}`,
          name: server.name,
          ip: server.ip,
          os: server.os || 'unknown',
          credentials: server.credentials,
          status: 'offline' as const,
          lastUpdate: new Date()
        };

        remoteMonitoring.addServer(newServer);

        // محاولة مراقبة السيرفر فوراً للتحقق من الاتصال
        try {
          await remoteMonitoring.monitorServer(newServer.id);
        } catch (error) {
          console.warn(`تحذير: لا يمكن الاتصال بالسيرفر ${newServer.name} حالياً`);
        }

        return NextResponse.json({
          success: true,
          server: newServer,
          message: `تم إضافة السيرفر ${server.name} بنجاح`
        });

      case 'remove':
        // إزالة سيرفر من المراقبة
        if (!server || !server.id) {
          return NextResponse.json(
            { error: 'معرف السيرفر مطلوب' },
            { status: 400 }
          );
        }

        const removed = remoteMonitoring.removeServer(server.id);
        if (!removed) {
          return NextResponse.json(
            { error: 'السيرفر غير موجود' },
            { status: 404 }
          );
        }

        return NextResponse.json({
          success: true,
          message: 'تم حذف السيرفر بنجاح'
        });

      case 'update':
        // تحديث بيانات سيرفر
        if (!server || !server.id) {
          return NextResponse.json(
            { error: 'معرف السيرفر مطلوب' },
            { status: 400 }
          );
        }

        const existingServer = remoteMonitoring.getServer(server.id);
        if (!existingServer) {
          return NextResponse.json(
            { error: 'السيرفر غير موجود' },
            { status: 404 }
          );
        }

        // تحديث البيانات
        const updatedServer = {
          ...existingServer,
          name: server.name || existingServer.name,
          ip: server.ip || existingServer.ip,
          os: server.os || existingServer.os,
          credentials: server.credentials || existingServer.credentials
        };

        remoteMonitoring.removeServer(server.id);
        remoteMonitoring.addServer(updatedServer);

        return NextResponse.json({
          success: true,
          server: updatedServer,
          message: 'تم تحديث السيرفر بنجاح'
        });

      case 'test_connection':
        // اختبار الاتصال بسيرفر
        if (!server || !server.id) {
          return NextResponse.json(
            { error: 'معرف السيرفر مطلوب' },
            { status: 400 }
          );
        }

        try {
          const systemInfo = await remoteMonitoring.monitorServer(server.id);
          return NextResponse.json({
            success: true,
            connected: systemInfo !== null,
            systemInfo,
            message: systemInfo ? 'الاتصال ناجح' : 'فشل في الاتصال'
          });
        } catch (error) {
          return NextResponse.json({
            success: false,
            connected: false,
            error: error instanceof Error ? error.message : 'خطأ في الاتصال',
            message: 'فشل في الاتصال بالسيرفر'
          });
        }

      default:
        return NextResponse.json(
          { error: 'إجراء غير مدعوم' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('خطأ في إدارة السيرفرات:', error);
    return NextResponse.json(
      { error: 'فشل في إدارة السيرفرات', details: error instanceof Error ? error.message : 'خطأ غير معروف' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const servers = remoteMonitoring.getServers();
    return NextResponse.json({
      success: true,
      servers,
      count: servers.length
    });
  } catch (error) {
    console.error('خطأ في جلب السيرفرات:', error);
    return NextResponse.json(
      { error: 'فشل في جلب السيرفرات' },
      { status: 500 }
    );
  }
}
